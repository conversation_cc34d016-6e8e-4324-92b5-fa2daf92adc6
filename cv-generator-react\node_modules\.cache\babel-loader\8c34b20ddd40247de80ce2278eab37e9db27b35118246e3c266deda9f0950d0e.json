{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\cv-generator-react\\\\src\\\\pages\\\\SimpleCVBuilder.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCV } from '../contexts/CVContext';\nimport Button from '../components/ui/Button';\nimport Input from '../components/ui/Input';\nimport Select from '../components/ui/Select';\nimport Textarea from '../components/ui/Textarea';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleCVBuilder = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    createCV\n  } = useCV();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isLoading, setIsLoading] = useState(false);\n  const [cvTitle, setCvTitle] = useState('My CV');\n  const [templateType, setTemplateType] = useState('modern');\n  const [personalInfo, setPersonalInfo] = useState({\n    first_name: '',\n    last_name: '',\n    email: '',\n    phone: '',\n    address: '',\n    website: '',\n    linkedin: '',\n    github: '',\n    summary: ''\n  });\n  const steps = [{\n    title: 'Basic Info',\n    component: 'basic'\n  }, {\n    title: 'Personal Info',\n    component: 'personal'\n  }];\n  const handleNext = () => {\n    if (currentStep < steps.length - 1) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n  const handlePrevious = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  const handleSave = async () => {\n    setIsLoading(true);\n    try {\n      const cvData = {\n        title: cvTitle,\n        template_type: templateType,\n        data: {\n          personal_info: personalInfo,\n          education: [],\n          experience: [],\n          skills: [],\n          projects: [],\n          languages: []\n        }\n      };\n      await createCV(cvData);\n      navigate('/dashboard');\n    } catch (error) {\n      console.error('Error saving CV:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const updatePersonalInfo = (field, value) => {\n    setPersonalInfo(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const renderBasicInfo = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        fontSize: '1.125rem',\n        fontWeight: '500',\n        color: '#111827',\n        marginBottom: '1.5rem'\n      },\n      children: \"Basic Information\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n        gap: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Input, {\n        label: \"CV Title\",\n        placeholder: \"e.g., Software Engineer Resume\",\n        value: cvTitle,\n        onChange: setCvTitle,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        label: \"Template\",\n        value: templateType,\n        onChange: setTemplateType,\n        options: [{\n          value: 'modern',\n          label: 'Modern'\n        }, {\n          value: 'classic',\n          label: 'Classic'\n        }, {\n          value: 'creative',\n          label: 'Creative'\n        }, {\n          value: 'minimal',\n          label: 'Minimal'\n        }],\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n  const renderPersonalInfo = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        fontSize: '1.125rem',\n        fontWeight: '500',\n        color: '#111827',\n        marginBottom: '1.5rem'\n      },\n      children: \"Personal Information\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n        gap: '1rem',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Input, {\n        label: \"First Name\",\n        placeholder: \"John\",\n        value: personalInfo.first_name,\n        onChange: value => updatePersonalInfo('first_name', value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Last Name\",\n        placeholder: \"Doe\",\n        value: personalInfo.last_name,\n        onChange: value => updatePersonalInfo('last_name', value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Email\",\n        type: \"email\",\n        placeholder: \"<EMAIL>\",\n        value: personalInfo.email,\n        onChange: value => updatePersonalInfo('email', value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Phone\",\n        type: \"tel\",\n        placeholder: \"+****************\",\n        value: personalInfo.phone,\n        onChange: value => updatePersonalInfo('phone', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Address\",\n        placeholder: \"123 Main St, City, State 12345\",\n        value: personalInfo.address,\n        onChange: value => updatePersonalInfo('address', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n        gap: '1rem',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Input, {\n        label: \"Website\",\n        type: \"url\",\n        placeholder: \"https://johndoe.com\",\n        value: personalInfo.website,\n        onChange: value => updatePersonalInfo('website', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"LinkedIn\",\n        type: \"url\",\n        placeholder: \"https://linkedin.com/in/johndoe\",\n        value: personalInfo.linkedin,\n        onChange: value => updatePersonalInfo('linkedin', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        label: \"GitHub\",\n        type: \"url\",\n        placeholder: \"https://github.com/johndoe\",\n        value: personalInfo.github,\n        onChange: value => updatePersonalInfo('github', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Textarea, {\n      label: \"Professional Summary\",\n      placeholder: \"Brief description of your professional background and goals...\",\n      value: personalInfo.summary,\n      onChange: value => updatePersonalInfo('summary', value),\n      rows: 4\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n  const renderCurrentStep = () => {\n    switch (steps[currentStep].component) {\n      case 'basic':\n        return renderBasicInfo();\n      case 'personal':\n        return renderPersonalInfo();\n      default:\n        return renderBasicInfo();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '64rem',\n      margin: '0 auto',\n      padding: '2rem 1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          fontSize: '1.875rem',\n          fontWeight: 'bold',\n          color: '#111827',\n          marginBottom: '0.5rem'\n        },\n        children: \"Create New CV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#6b7280'\n        },\n        children: \"Fill out the information below to create your professional CV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            marginRight: index !== steps.length - 1 ? '2rem' : '0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              width: '2rem',\n              height: '2rem',\n              borderRadius: '50%',\n              backgroundColor: index < currentStep ? '#3b82f6' : index === currentStep ? 'white' : 'white',\n              border: index === currentStep ? '2px solid #3b82f6' : '2px solid #d1d5db',\n              color: index < currentStep ? 'white' : index === currentStep ? '#3b82f6' : '#6b7280',\n              fontSize: '0.875rem',\n              fontWeight: '500'\n            },\n            children: index < currentStep ? /*#__PURE__*/_jsxDEV(\"svg\", {\n              style: {\n                width: '1.25rem',\n                height: '1.25rem'\n              },\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 19\n            }, this) : index + 1\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '1rem',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: index === currentStep ? '#3b82f6' : '#6b7280'\n            },\n            children: step.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this)]\n        }, step.title, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',\n        borderRadius: '0.5rem',\n        padding: '1.5rem',\n        marginBottom: '2rem'\n      },\n      children: renderCurrentStep()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: currentStep > 0 && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePrevious,\n          variant: \"outline\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSave,\n          variant: \"outline\",\n          loading: isLoading,\n          disabled: isLoading,\n          children: \"Save CV\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), currentStep < steps.length - 1 ? /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleNext,\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSave,\n          loading: isLoading,\n          disabled: isLoading,\n          children: isLoading ? 'Saving...' : 'Finish'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleCVBuilder, \"5lYgqWHuR/7dl8uHlhDeelJJ5N8=\", false, function () {\n  return [useNavigate, useCV];\n});\n_c = SimpleCVBuilder;\nexport default SimpleCVBuilder;\nvar _c;\n$RefreshReg$(_c, \"SimpleCVBuilder\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useCV", "<PERSON><PERSON>", "Input", "Select", "Textarea", "jsxDEV", "_jsxDEV", "SimpleCVBuilder", "_s", "navigate", "createCV", "currentStep", "setCurrentStep", "isLoading", "setIsLoading", "cvTitle", "setCvTitle", "templateType", "setTemplateType", "personalInfo", "setPersonalInfo", "first_name", "last_name", "email", "phone", "address", "website", "linkedin", "github", "summary", "steps", "title", "component", "handleNext", "length", "handlePrevious", "handleSave", "cvData", "template_type", "data", "personal_info", "education", "experience", "skills", "projects", "languages", "error", "console", "updatePersonalInfo", "field", "value", "prev", "renderBasicInfo", "children", "style", "fontSize", "fontWeight", "color", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "gridTemplateColumns", "gap", "label", "placeholder", "onChange", "required", "options", "renderPersonalInfo", "type", "rows", "renderCurrentStep", "max<PERSON><PERSON><PERSON>", "margin", "padding", "alignItems", "map", "step", "index", "marginRight", "justifyContent", "width", "height", "borderRadius", "backgroundColor", "border", "fill", "viewBox", "fillRule", "d", "clipRule", "marginLeft", "boxShadow", "onClick", "variant", "loading", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/cv-generator-react/src/pages/SimpleCVBuilder.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCV } from '../contexts/CVContext';\nimport Button from '../components/ui/Button';\nimport Input from '../components/ui/Input';\nimport Select from '../components/ui/Select';\nimport Textarea from '../components/ui/Textarea';\n\nconst SimpleCVBuilder: React.FC = () => {\n  const navigate = useNavigate();\n  const { createCV } = useCV();\n  \n  const [currentStep, setCurrentStep] = useState(0);\n  const [isLoading, setIsLoading] = useState(false);\n  const [cvTitle, setCvTitle] = useState('My CV');\n  const [templateType, setTemplateType] = useState('modern');\n  \n  const [personalInfo, setPersonalInfo] = useState({\n    first_name: '',\n    last_name: '',\n    email: '',\n    phone: '',\n    address: '',\n    website: '',\n    linkedin: '',\n    github: '',\n    summary: '',\n  });\n\n  const steps = [\n    { title: 'Basic Info', component: 'basic' },\n    { title: 'Personal Info', component: 'personal' },\n  ];\n\n  const handleNext = () => {\n    if (currentStep < steps.length - 1) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const handleSave = async () => {\n    setIsLoading(true);\n    try {\n      const cvData = {\n        title: cvTitle,\n        template_type: templateType,\n        data: {\n          personal_info: personalInfo,\n          education: [],\n          experience: [],\n          skills: [],\n          projects: [],\n          languages: [],\n        }\n      };\n      \n      await createCV(cvData);\n      navigate('/dashboard');\n    } catch (error) {\n      console.error('Error saving CV:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const updatePersonalInfo = (field: string, value: string) => {\n    setPersonalInfo(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const renderBasicInfo = () => (\n    <div>\n      <h3 style={{ fontSize: '1.125rem', fontWeight: '500', color: '#111827', marginBottom: '1.5rem' }}>\n        Basic Information\n      </h3>\n      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>\n        <Input\n          label=\"CV Title\"\n          placeholder=\"e.g., Software Engineer Resume\"\n          value={cvTitle}\n          onChange={setCvTitle}\n          required\n        />\n        <Select\n          label=\"Template\"\n          value={templateType}\n          onChange={setTemplateType}\n          options={[\n            { value: 'modern', label: 'Modern' },\n            { value: 'classic', label: 'Classic' },\n            { value: 'creative', label: 'Creative' },\n            { value: 'minimal', label: 'Minimal' },\n          ]}\n          required\n        />\n      </div>\n    </div>\n  );\n\n  const renderPersonalInfo = () => (\n    <div>\n      <h3 style={{ fontSize: '1.125rem', fontWeight: '500', color: '#111827', marginBottom: '1.5rem' }}>\n        Personal Information\n      </h3>\n      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginBottom: '1rem' }}>\n        <Input\n          label=\"First Name\"\n          placeholder=\"John\"\n          value={personalInfo.first_name}\n          onChange={(value) => updatePersonalInfo('first_name', value)}\n          required\n        />\n        <Input\n          label=\"Last Name\"\n          placeholder=\"Doe\"\n          value={personalInfo.last_name}\n          onChange={(value) => updatePersonalInfo('last_name', value)}\n          required\n        />\n        <Input\n          label=\"Email\"\n          type=\"email\"\n          placeholder=\"<EMAIL>\"\n          value={personalInfo.email}\n          onChange={(value) => updatePersonalInfo('email', value)}\n          required\n        />\n        <Input\n          label=\"Phone\"\n          type=\"tel\"\n          placeholder=\"+****************\"\n          value={personalInfo.phone}\n          onChange={(value) => updatePersonalInfo('phone', value)}\n        />\n      </div>\n      <div style={{ marginBottom: '1rem' }}>\n        <Input\n          label=\"Address\"\n          placeholder=\"123 Main St, City, State 12345\"\n          value={personalInfo.address}\n          onChange={(value) => updatePersonalInfo('address', value)}\n        />\n      </div>\n      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginBottom: '1rem' }}>\n        <Input\n          label=\"Website\"\n          type=\"url\"\n          placeholder=\"https://johndoe.com\"\n          value={personalInfo.website}\n          onChange={(value) => updatePersonalInfo('website', value)}\n        />\n        <Input\n          label=\"LinkedIn\"\n          type=\"url\"\n          placeholder=\"https://linkedin.com/in/johndoe\"\n          value={personalInfo.linkedin}\n          onChange={(value) => updatePersonalInfo('linkedin', value)}\n        />\n      </div>\n      <div style={{ marginBottom: '1rem' }}>\n        <Input\n          label=\"GitHub\"\n          type=\"url\"\n          placeholder=\"https://github.com/johndoe\"\n          value={personalInfo.github}\n          onChange={(value) => updatePersonalInfo('github', value)}\n        />\n      </div>\n      <Textarea\n        label=\"Professional Summary\"\n        placeholder=\"Brief description of your professional background and goals...\"\n        value={personalInfo.summary}\n        onChange={(value) => updatePersonalInfo('summary', value)}\n        rows={4}\n      />\n    </div>\n  );\n\n  const renderCurrentStep = () => {\n    switch (steps[currentStep].component) {\n      case 'basic':\n        return renderBasicInfo();\n      case 'personal':\n        return renderPersonalInfo();\n      default:\n        return renderBasicInfo();\n    }\n  };\n\n  return (\n    <div style={{ maxWidth: '64rem', margin: '0 auto', padding: '2rem 1rem' }}>\n      {/* Header */}\n      <div style={{ marginBottom: '2rem' }}>\n        <h1 style={{ fontSize: '1.875rem', fontWeight: 'bold', color: '#111827', marginBottom: '0.5rem' }}>\n          Create New CV\n        </h1>\n        <p style={{ color: '#6b7280' }}>\n          Fill out the information below to create your professional CV\n        </p>\n      </div>\n\n      {/* Progress Steps */}\n      <div style={{ marginBottom: '2rem' }}>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          {steps.map((step, index) => (\n            <div key={step.title} style={{ display: 'flex', alignItems: 'center', marginRight: index !== steps.length - 1 ? '2rem' : '0' }}>\n              <div\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  width: '2rem',\n                  height: '2rem',\n                  borderRadius: '50%',\n                  backgroundColor: index < currentStep ? '#3b82f6' : index === currentStep ? 'white' : 'white',\n                  border: index === currentStep ? '2px solid #3b82f6' : '2px solid #d1d5db',\n                  color: index < currentStep ? 'white' : index === currentStep ? '#3b82f6' : '#6b7280',\n                  fontSize: '0.875rem',\n                  fontWeight: '500'\n                }}\n              >\n                {index < currentStep ? (\n                  <svg style={{ width: '1.25rem', height: '1.25rem' }} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                ) : (\n                  index + 1\n                )}\n              </div>\n              <span style={{ \n                marginLeft: '1rem', \n                fontSize: '0.875rem', \n                fontWeight: '500',\n                color: index === currentStep ? '#3b82f6' : '#6b7280'\n              }}>\n                {step.title}\n              </span>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Form Content */}\n      <div style={{ backgroundColor: 'white', boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)', borderRadius: '0.5rem', padding: '1.5rem', marginBottom: '2rem' }}>\n        {renderCurrentStep()}\n      </div>\n\n      {/* Navigation Buttons */}\n      <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n        <div>\n          {currentStep > 0 && (\n            <Button\n              onClick={handlePrevious}\n              variant=\"outline\"\n            >\n              Previous\n            </Button>\n          )}\n        </div>\n        <div style={{ display: 'flex', gap: '1rem' }}>\n          <Button\n            onClick={handleSave}\n            variant=\"outline\"\n            loading={isLoading}\n            disabled={isLoading}\n          >\n            Save CV\n          </Button>\n          {currentStep < steps.length - 1 ? (\n            <Button onClick={handleNext}>\n              Next\n            </Button>\n          ) : (\n            <Button onClick={handleSave} loading={isLoading} disabled={isLoading}>\n              {isLoading ? 'Saving...' : 'Finish'}\n            </Button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SimpleCVBuilder;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,QAAQ,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW;EAAS,CAAC,GAAGV,KAAK,CAAC,CAAC;EAE5B,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACe,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,OAAO,CAAC;EAC/C,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,QAAQ,CAAC;EAE1D,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC;IAC/CuB,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAMC,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,YAAY;IAAEC,SAAS,EAAE;EAAQ,CAAC,EAC3C;IAAED,KAAK,EAAE,eAAe;IAAEC,SAAS,EAAE;EAAW,CAAC,CAClD;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAItB,WAAW,GAAGmB,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MAClCtB,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMwB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIxB,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMyB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BtB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMuB,MAAM,GAAG;QACbN,KAAK,EAAEhB,OAAO;QACduB,aAAa,EAAErB,YAAY;QAC3BsB,IAAI,EAAE;UACJC,aAAa,EAAErB,YAAY;UAC3BsB,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAE;QACb;MACF,CAAC;MAED,MAAMnC,QAAQ,CAAC2B,MAAM,CAAC;MACtB5B,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IAC1C,CAAC,SAAS;MACRhC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMkC,kBAAkB,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAAK;IAC3D9B,eAAe,CAAC+B,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,kBACtB9C,OAAA;IAAA+C,QAAA,gBACE/C,OAAA;MAAIgD,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,UAAU,EAAE,KAAK;QAAEC,KAAK,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAS,CAAE;MAAAL,QAAA,EAAC;IAElG;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACLxD,OAAA;MAAKgD,KAAK,EAAE;QAAES,OAAO,EAAE,MAAM;QAAEC,mBAAmB,EAAE,sCAAsC;QAAEC,GAAG,EAAE;MAAS,CAAE;MAAAZ,QAAA,gBAC1G/C,OAAA,CAACJ,KAAK;QACJgE,KAAK,EAAC,UAAU;QAChBC,WAAW,EAAC,gCAAgC;QAC5CjB,KAAK,EAAEnC,OAAQ;QACfqD,QAAQ,EAAEpD,UAAW;QACrBqD,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA,CAACH,MAAM;QACL+D,KAAK,EAAC,UAAU;QAChBhB,KAAK,EAAEjC,YAAa;QACpBmD,QAAQ,EAAElD,eAAgB;QAC1BoD,OAAO,EAAE,CACP;UAAEpB,KAAK,EAAE,QAAQ;UAAEgB,KAAK,EAAE;QAAS,CAAC,EACpC;UAAEhB,KAAK,EAAE,SAAS;UAAEgB,KAAK,EAAE;QAAU,CAAC,EACtC;UAAEhB,KAAK,EAAE,UAAU;UAAEgB,KAAK,EAAE;QAAW,CAAC,EACxC;UAAEhB,KAAK,EAAE,SAAS;UAAEgB,KAAK,EAAE;QAAU,CAAC,CACtC;QACFG,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMS,kBAAkB,GAAGA,CAAA,kBACzBjE,OAAA;IAAA+C,QAAA,gBACE/C,OAAA;MAAIgD,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,UAAU,EAAE,KAAK;QAAEC,KAAK,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAS,CAAE;MAAAL,QAAA,EAAC;IAElG;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACLxD,OAAA;MAAKgD,KAAK,EAAE;QAAES,OAAO,EAAE,MAAM;QAAEC,mBAAmB,EAAE,sCAAsC;QAAEC,GAAG,EAAE,MAAM;QAAEP,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBAC9H/C,OAAA,CAACJ,KAAK;QACJgE,KAAK,EAAC,YAAY;QAClBC,WAAW,EAAC,MAAM;QAClBjB,KAAK,EAAE/B,YAAY,CAACE,UAAW;QAC/B+C,QAAQ,EAAGlB,KAAK,IAAKF,kBAAkB,CAAC,YAAY,EAAEE,KAAK,CAAE;QAC7DmB,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA,CAACJ,KAAK;QACJgE,KAAK,EAAC,WAAW;QACjBC,WAAW,EAAC,KAAK;QACjBjB,KAAK,EAAE/B,YAAY,CAACG,SAAU;QAC9B8C,QAAQ,EAAGlB,KAAK,IAAKF,kBAAkB,CAAC,WAAW,EAAEE,KAAK,CAAE;QAC5DmB,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA,CAACJ,KAAK;QACJgE,KAAK,EAAC,OAAO;QACbM,IAAI,EAAC,OAAO;QACZL,WAAW,EAAC,sBAAsB;QAClCjB,KAAK,EAAE/B,YAAY,CAACI,KAAM;QAC1B6C,QAAQ,EAAGlB,KAAK,IAAKF,kBAAkB,CAAC,OAAO,EAAEE,KAAK,CAAE;QACxDmB,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA,CAACJ,KAAK;QACJgE,KAAK,EAAC,OAAO;QACbM,IAAI,EAAC,KAAK;QACVL,WAAW,EAAC,mBAAmB;QAC/BjB,KAAK,EAAE/B,YAAY,CAACK,KAAM;QAC1B4C,QAAQ,EAAGlB,KAAK,IAAKF,kBAAkB,CAAC,OAAO,EAAEE,KAAK;MAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNxD,OAAA;MAAKgD,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,eACnC/C,OAAA,CAACJ,KAAK;QACJgE,KAAK,EAAC,SAAS;QACfC,WAAW,EAAC,gCAAgC;QAC5CjB,KAAK,EAAE/B,YAAY,CAACM,OAAQ;QAC5B2C,QAAQ,EAAGlB,KAAK,IAAKF,kBAAkB,CAAC,SAAS,EAAEE,KAAK;MAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNxD,OAAA;MAAKgD,KAAK,EAAE;QAAES,OAAO,EAAE,MAAM;QAAEC,mBAAmB,EAAE,sCAAsC;QAAEC,GAAG,EAAE,MAAM;QAAEP,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBAC9H/C,OAAA,CAACJ,KAAK;QACJgE,KAAK,EAAC,SAAS;QACfM,IAAI,EAAC,KAAK;QACVL,WAAW,EAAC,qBAAqB;QACjCjB,KAAK,EAAE/B,YAAY,CAACO,OAAQ;QAC5B0C,QAAQ,EAAGlB,KAAK,IAAKF,kBAAkB,CAAC,SAAS,EAAEE,KAAK;MAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACFxD,OAAA,CAACJ,KAAK;QACJgE,KAAK,EAAC,UAAU;QAChBM,IAAI,EAAC,KAAK;QACVL,WAAW,EAAC,iCAAiC;QAC7CjB,KAAK,EAAE/B,YAAY,CAACQ,QAAS;QAC7ByC,QAAQ,EAAGlB,KAAK,IAAKF,kBAAkB,CAAC,UAAU,EAAEE,KAAK;MAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNxD,OAAA;MAAKgD,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,eACnC/C,OAAA,CAACJ,KAAK;QACJgE,KAAK,EAAC,QAAQ;QACdM,IAAI,EAAC,KAAK;QACVL,WAAW,EAAC,4BAA4B;QACxCjB,KAAK,EAAE/B,YAAY,CAACS,MAAO;QAC3BwC,QAAQ,EAAGlB,KAAK,IAAKF,kBAAkB,CAAC,QAAQ,EAAEE,KAAK;MAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNxD,OAAA,CAACF,QAAQ;MACP8D,KAAK,EAAC,sBAAsB;MAC5BC,WAAW,EAAC,gEAAgE;MAC5EjB,KAAK,EAAE/B,YAAY,CAACU,OAAQ;MAC5BuC,QAAQ,EAAGlB,KAAK,IAAKF,kBAAkB,CAAC,SAAS,EAAEE,KAAK,CAAE;MAC1DuB,IAAI,EAAE;IAAE;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMY,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQ5C,KAAK,CAACnB,WAAW,CAAC,CAACqB,SAAS;MAClC,KAAK,OAAO;QACV,OAAOoB,eAAe,CAAC,CAAC;MAC1B,KAAK,UAAU;QACb,OAAOmB,kBAAkB,CAAC,CAAC;MAC7B;QACE,OAAOnB,eAAe,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,oBACE9C,OAAA;IAAKgD,KAAK,EAAE;MAAEqB,QAAQ,EAAE,OAAO;MAAEC,MAAM,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAY,CAAE;IAAAxB,QAAA,gBAExE/C,OAAA;MAAKgD,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBACnC/C,OAAA;QAAIgD,KAAK,EAAE;UAAEC,QAAQ,EAAE,UAAU;UAAEC,UAAU,EAAE,MAAM;UAAEC,KAAK,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAS,CAAE;QAAAL,QAAA,EAAC;MAEnG;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLxD,OAAA;QAAGgD,KAAK,EAAE;UAAEG,KAAK,EAAE;QAAU,CAAE;QAAAJ,QAAA,EAAC;MAEhC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNxD,OAAA;MAAKgD,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,eACnC/C,OAAA;QAAKgD,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEe,UAAU,EAAE;QAAS,CAAE;QAAAzB,QAAA,EACnDvB,KAAK,CAACiD,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrB3E,OAAA;UAAsBgD,KAAK,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEe,UAAU,EAAE,QAAQ;YAAEI,WAAW,EAAED,KAAK,KAAKnD,KAAK,CAACI,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;UAAI,CAAE;UAAAmB,QAAA,gBAC7H/C,OAAA;YACEgD,KAAK,EAAE;cACLS,OAAO,EAAE,MAAM;cACfe,UAAU,EAAE,QAAQ;cACpBK,cAAc,EAAE,QAAQ;cACxBC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBC,eAAe,EAAEN,KAAK,GAAGtE,WAAW,GAAG,SAAS,GAAGsE,KAAK,KAAKtE,WAAW,GAAG,OAAO,GAAG,OAAO;cAC5F6E,MAAM,EAAEP,KAAK,KAAKtE,WAAW,GAAG,mBAAmB,GAAG,mBAAmB;cACzE8C,KAAK,EAAEwB,KAAK,GAAGtE,WAAW,GAAG,OAAO,GAAGsE,KAAK,KAAKtE,WAAW,GAAG,SAAS,GAAG,SAAS;cACpF4C,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE;YACd,CAAE;YAAAH,QAAA,EAED4B,KAAK,GAAGtE,WAAW,gBAClBL,OAAA;cAAKgD,KAAK,EAAE;gBAAE8B,KAAK,EAAE,SAAS;gBAAEC,MAAM,EAAE;cAAU,CAAE;cAACI,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAArC,QAAA,eAC1F/C,OAAA;gBAAMqF,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,oHAAoH;gBAACC,QAAQ,EAAC;cAAS;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClK,CAAC,GAENmB,KAAK,GAAG;UACT;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNxD,OAAA;YAAMgD,KAAK,EAAE;cACXwC,UAAU,EAAE,MAAM;cAClBvC,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAEwB,KAAK,KAAKtE,WAAW,GAAG,SAAS,GAAG;YAC7C,CAAE;YAAA0C,QAAA,EACC2B,IAAI,CAACjD;UAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA,GA/BCkB,IAAI,CAACjD,KAAK;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgCf,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxD,OAAA;MAAKgD,KAAK,EAAE;QAAEiC,eAAe,EAAE,OAAO;QAAEQ,SAAS,EAAE,gCAAgC;QAAET,YAAY,EAAE,QAAQ;QAAET,OAAO,EAAE,QAAQ;QAAEnB,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,EACpJqB,iBAAiB,CAAC;IAAC;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGNxD,OAAA;MAAKgD,KAAK,EAAE;QAAES,OAAO,EAAE,MAAM;QAAEoB,cAAc,EAAE;MAAgB,CAAE;MAAA9B,QAAA,gBAC/D/C,OAAA;QAAA+C,QAAA,EACG1C,WAAW,GAAG,CAAC,iBACdL,OAAA,CAACL,MAAM;UACL+F,OAAO,EAAE7D,cAAe;UACxB8D,OAAO,EAAC,SAAS;UAAA5C,QAAA,EAClB;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNxD,OAAA;QAAKgD,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE;QAAO,CAAE;QAAAZ,QAAA,gBAC3C/C,OAAA,CAACL,MAAM;UACL+F,OAAO,EAAE5D,UAAW;UACpB6D,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAErF,SAAU;UACnBsF,QAAQ,EAAEtF,SAAU;UAAAwC,QAAA,EACrB;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRnD,WAAW,GAAGmB,KAAK,CAACI,MAAM,GAAG,CAAC,gBAC7B5B,OAAA,CAACL,MAAM;UAAC+F,OAAO,EAAE/D,UAAW;UAAAoB,QAAA,EAAC;QAE7B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETxD,OAAA,CAACL,MAAM;UAAC+F,OAAO,EAAE5D,UAAW;UAAC8D,OAAO,EAAErF,SAAU;UAACsF,QAAQ,EAAEtF,SAAU;UAAAwC,QAAA,EAClExC,SAAS,GAAG,WAAW,GAAG;QAAQ;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CAzRID,eAAyB;EAAA,QACZR,WAAW,EACPC,KAAK;AAAA;AAAAoG,EAAA,GAFtB7F,eAAyB;AA2R/B,eAAeA,eAAe;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}