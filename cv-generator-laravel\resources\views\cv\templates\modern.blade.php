<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $data['personal_info']['first_name'] }} {{ $data['personal_info']['last_name'] }} - CV</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            font-size: 14px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }
        
        .header {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 15px;
            font-size: 12px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2563eb;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        
        .summary {
            text-align: justify;
            line-height: 1.7;
        }
        
        .experience-item, .education-item {
            margin-bottom: 20px;
            padding-left: 20px;
            border-left: 3px solid #e5e7eb;
            position: relative;
        }
        
        .experience-item::before, .education-item::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 5px;
            width: 10px;
            height: 10px;
            background: #2563eb;
            border-radius: 50%;
        }
        
        .item-title {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
        }
        
        .item-company {
            color: #2563eb;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .item-date {
            color: #6b7280;
            font-size: 12px;
            margin-bottom: 8px;
        }
        
        .item-description {
            text-align: justify;
            line-height: 1.6;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .skill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .skill-name {
            font-weight: 600;
        }
        
        .skill-level {
            background: #e5e7eb;
            color: #374151;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
        }
        
        .projects-grid {
            display: grid;
            gap: 15px;
        }
        
        .project-item {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
        }
        
        .project-title {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .project-description {
            text-align: justify;
            margin-bottom: 10px;
        }
        
        .project-tech {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .tech-tag {
            background: #dbeafe;
            color: #1e40af;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
        }
        
        .languages-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }
        
        .language-item {
            text-align: center;
        }
        
        .language-name {
            font-weight: 600;
            margin-bottom: 3px;
        }
        
        .language-level {
            color: #6b7280;
            font-size: 12px;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        @media print {
            body { font-size: 12px; }
            .header { padding: 30px 20px; }
            .content { padding: 20px; }
            .section { margin-bottom: 20px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>{{ $data['personal_info']['first_name'] }} {{ $data['personal_info']['last_name'] }}</h1>
            <div class="contact-info">
                @if(!empty($data['personal_info']['email']))
                    <div class="contact-item">📧 {{ $data['personal_info']['email'] }}</div>
                @endif
                @if(!empty($data['personal_info']['phone']))
                    <div class="contact-item">📞 {{ $data['personal_info']['phone'] }}</div>
                @endif
                @if(!empty($data['personal_info']['address']))
                    <div class="contact-item">📍 {{ $data['personal_info']['address'] }}</div>
                @endif
                @if(!empty($data['personal_info']['website']))
                    <div class="contact-item">🌐 {{ $data['personal_info']['website'] }}</div>
                @endif
            </div>
        </div>

        <div class="content">
            <!-- Professional Summary -->
            @if(!empty($data['personal_info']['summary']))
                <div class="section">
                    <h2 class="section-title">Professional Summary</h2>
                    <p class="summary">{{ $data['personal_info']['summary'] }}</p>
                </div>
            @endif

            <div class="two-column">
                <div>
                    <!-- Experience -->
                    @if(!empty($data['experience']) && count($data['experience']) > 0)
                        <div class="section">
                            <h2 class="section-title">Work Experience</h2>
                            @foreach($data['experience'] as $exp)
                                <div class="experience-item">
                                    <div class="item-title">{{ $exp['position'] }}</div>
                                    <div class="item-company">{{ $exp['company'] }}</div>
                                    <div class="item-date">
                                        {{ $exp['start_date'] }} - {{ $exp['current'] ? 'Present' : $exp['end_date'] }}
                                    </div>
                                    @if(!empty($exp['description']))
                                        <div class="item-description">{{ $exp['description'] }}</div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    @endif

                    <!-- Education -->
                    @if(!empty($data['education']) && count($data['education']) > 0)
                        <div class="section">
                            <h2 class="section-title">Education</h2>
                            @foreach($data['education'] as $edu)
                                <div class="education-item">
                                    <div class="item-title">{{ $edu['degree'] }}</div>
                                    <div class="item-company">{{ $edu['institution'] }}</div>
                                    <div class="item-date">
                                        {{ $edu['field_of_study'] }} • 
                                        {{ $edu['start_date'] }} - {{ $edu['current'] ? 'Present' : $edu['end_date'] }}
                                        @if(!empty($edu['gpa']))
                                            • GPA: {{ $edu['gpa'] }}
                                        @endif
                                    </div>
                                    @if(!empty($edu['description']))
                                        <div class="item-description">{{ $edu['description'] }}</div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    @endif

                    <!-- Projects -->
                    @if(!empty($data['projects']) && count($data['projects']) > 0)
                        <div class="section">
                            <h2 class="section-title">Projects</h2>
                            <div class="projects-grid">
                                @foreach($data['projects'] as $project)
                                    <div class="project-item">
                                        <div class="project-title">{{ $project['name'] }}</div>
                                        <div class="project-description">{{ $project['description'] }}</div>
                                        @if(!empty($project['technologies']) && count($project['technologies']) > 0)
                                            <div class="project-tech">
                                                @foreach($project['technologies'] as $tech)
                                                    <span class="tech-tag">{{ $tech }}</span>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>

                <div>
                    <!-- Skills -->
                    @if(!empty($data['skills']) && count($data['skills']) > 0)
                        <div class="section">
                            <h2 class="section-title">Skills</h2>
                            <div class="skills-grid">
                                @foreach($data['skills'] as $skill)
                                    <div class="skill-item">
                                        <span class="skill-name">{{ $skill['name'] }}</span>
                                        <span class="skill-level">{{ $skill['level'] }}</span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Languages -->
                    @if(!empty($data['languages']) && count($data['languages']) > 0)
                        <div class="section">
                            <h2 class="section-title">Languages</h2>
                            <div class="languages-grid">
                                @foreach($data['languages'] as $language)
                                    <div class="language-item">
                                        <div class="language-name">{{ $language['name'] }}</div>
                                        <div class="language-level">{{ $language['proficiency'] }}</div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</body>
</html>
