{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\frontend\\\\src\\\\pages\\\\Register.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport Button from '../components/ui/Button';\nimport Input from '../components/ui/Input';\nimport { isValidEmail } from '../utils/helpers';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    register,\n    isLoading\n  } = useAuth();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: ''\n  });\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [errors, setErrors] = useState({});\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.trim().length < 2) {\n      newErrors.name = 'Name must be at least 2 characters long';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!isValidEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n    if (!formData.password.trim()) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters long';\n    }\n    if (!confirmPassword.trim()) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (confirmPassword !== formData.password) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    try {\n      await register(formData);\n      navigate('/dashboard');\n    } catch (error) {\n      // Error is handled by the auth context\n    }\n  };\n  const handleInputChange = field => value => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: undefined\n      }));\n    }\n  };\n  const handleConfirmPasswordChange = value => {\n    setConfirmPassword(value);\n    if (errors.confirmPassword) {\n      setErrors(prev => ({\n        ...prev,\n        confirmPassword: undefined\n      }));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-6 w-6 text-primary-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n          children: \"Create your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-center text-sm text-gray-600\",\n          children: [\"Or\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"font-medium text-primary-600 hover:text-primary-500\",\n            children: \"sign in to your existing account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"mt-8 space-y-6\",\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            label: \"Full Name\",\n            type: \"text\",\n            placeholder: \"Enter your full name\",\n            value: formData.name,\n            onChange: handleInputChange('name'),\n            error: errors.name,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Email address\",\n            type: \"email\",\n            placeholder: \"Enter your email\",\n            value: formData.email,\n            onChange: handleInputChange('email'),\n            error: errors.email,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Password\",\n            type: \"password\",\n            placeholder: \"Create a password (min. 6 characters)\",\n            value: formData.password,\n            onChange: handleInputChange('password'),\n            error: errors.password,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Confirm Password\",\n            type: \"password\",\n            placeholder: \"Confirm your password\",\n            value: confirmPassword,\n            onChange: handleConfirmPasswordChange,\n            error: errors.confirmPassword,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"agree-terms\",\n            name: \"agree-terms\",\n            type: \"checkbox\",\n            required: true,\n            className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"agree-terms\",\n            className: \"ml-2 block text-sm text-gray-900\",\n            children: [\"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"text-primary-600 hover:text-primary-500 bg-transparent border-none cursor-pointer p-0\",\n              onClick: () => alert('Terms of Service coming soon!'),\n              children: \"Terms of Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"text-primary-600 hover:text-primary-500 bg-transparent border-none cursor-pointer p-0\",\n              onClick: () => alert('Privacy Policy coming soon!'),\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          className: \"w-full\",\n          loading: isLoading,\n          disabled: isLoading,\n          children: \"Create Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 flex items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full border-t border-gray-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative flex justify-center text-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-2 bg-gray-50 text-gray-500\",\n              children: \"Already have an account?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-primary-600 bg-white hover:bg-gray-50\",\n            children: \"Sign in instead\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"SLl47FYcnCwEuX98M/CaoKIoEGA=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "<PERSON><PERSON>", "Input", "isValidEmail", "jsxDEV", "_jsxDEV", "Register", "_s", "navigate", "register", "isLoading", "formData", "setFormData", "name", "email", "password", "confirmPassword", "setConfirmPassword", "errors", "setErrors", "validateForm", "newErrors", "trim", "length", "Object", "keys", "handleSubmit", "e", "preventDefault", "error", "handleInputChange", "field", "value", "prev", "undefined", "handleConfirmPasswordChange", "className", "children", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onSubmit", "label", "type", "placeholder", "onChange", "required", "id", "htmlFor", "onClick", "alert", "loading", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/frontend/src/pages/Register.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport Button from '../components/ui/Button';\nimport Input from '../components/ui/Input';\nimport { RegisterData } from '../types';\nimport { isValidEmail } from '../utils/helpers';\n\nconst Register: React.FC = () => {\n  const navigate = useNavigate();\n  const { register, isLoading } = useAuth();\n  \n  const [formData, setFormData] = useState<RegisterData>({\n    name: '',\n    email: '',\n    password: '',\n  });\n  \n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [errors, setErrors] = useState<Partial<RegisterData & { confirmPassword: string }>>({});\n\n  const validateForm = (): boolean => {\n    const newErrors: Partial<RegisterData & { confirmPassword: string }> = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.trim().length < 2) {\n      newErrors.name = 'Name must be at least 2 characters long';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!isValidEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    if (!formData.password.trim()) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters long';\n    }\n\n    if (!confirmPassword.trim()) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (confirmPassword !== formData.password) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n\n    try {\n      await register(formData);\n      navigate('/dashboard');\n    } catch (error) {\n      // Error is handled by the auth context\n    }\n  };\n\n  const handleInputChange = (field: keyof RegisterData) => (value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: undefined }));\n    }\n  };\n\n  const handleConfirmPasswordChange = (value: string) => {\n    setConfirmPassword(value);\n    if (errors.confirmPassword) {\n      setErrors(prev => ({ ...prev, confirmPassword: undefined }));\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <div className=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100\">\n            <svg\n              className=\"h-6 w-6 text-primary-600\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n              />\n            </svg>\n          </div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Or{' '}\n            <Link\n              to=\"/login\"\n              className=\"font-medium text-primary-600 hover:text-primary-500\"\n            >\n              sign in to your existing account\n            </Link>\n          </p>\n        </div>\n        \n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            <Input\n              label=\"Full Name\"\n              type=\"text\"\n              placeholder=\"Enter your full name\"\n              value={formData.name}\n              onChange={handleInputChange('name')}\n              error={errors.name}\n              required\n            />\n            \n            <Input\n              label=\"Email address\"\n              type=\"email\"\n              placeholder=\"Enter your email\"\n              value={formData.email}\n              onChange={handleInputChange('email')}\n              error={errors.email}\n              required\n            />\n            \n            <Input\n              label=\"Password\"\n              type=\"password\"\n              placeholder=\"Create a password (min. 6 characters)\"\n              value={formData.password}\n              onChange={handleInputChange('password')}\n              error={errors.password}\n              required\n            />\n            \n            <Input\n              label=\"Confirm Password\"\n              type=\"password\"\n              placeholder=\"Confirm your password\"\n              value={confirmPassword}\n              onChange={handleConfirmPasswordChange}\n              error={errors.confirmPassword}\n              required\n            />\n          </div>\n\n          <div className=\"flex items-center\">\n            <input\n              id=\"agree-terms\"\n              name=\"agree-terms\"\n              type=\"checkbox\"\n              required\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label htmlFor=\"agree-terms\" className=\"ml-2 block text-sm text-gray-900\">\n              I agree to the{' '}\n              <button\n                type=\"button\"\n                className=\"text-primary-600 hover:text-primary-500 bg-transparent border-none cursor-pointer p-0\"\n                onClick={() => alert('Terms of Service coming soon!')}\n              >\n                Terms of Service\n              </button>{' '}\n              and{' '}\n              <button\n                type=\"button\"\n                className=\"text-primary-600 hover:text-primary-500 bg-transparent border-none cursor-pointer p-0\"\n                onClick={() => alert('Privacy Policy coming soon!')}\n              >\n                Privacy Policy\n              </button>\n            </label>\n          </div>\n\n          <Button\n            type=\"submit\"\n            className=\"w-full\"\n            loading={isLoading}\n            disabled={isLoading}\n          >\n            Create Account\n          </Button>\n        </form>\n        \n        <div className=\"mt-6\">\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-gray-300\" />\n            </div>\n            <div className=\"relative flex justify-center text-sm\">\n              <span className=\"px-2 bg-gray-50 text-gray-500\">\n                Already have an account?\n              </span>\n            </div>\n          </div>\n          \n          <div className=\"mt-6\">\n            <Link\n              to=\"/login\"\n              className=\"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-primary-600 bg-white hover:bg-gray-50\"\n            >\n              Sign in instead\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAE1C,SAASC,YAAY,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU,QAAQ;IAAEC;EAAU,CAAC,GAAGV,OAAO,CAAC,CAAC;EAEzC,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAe;IACrDgB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqB,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAsD,CAAC,CAAC,CAAC;EAE7F,MAAMuB,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,SAA8D,GAAG,CAAC,CAAC;IAEzE,IAAI,CAACV,QAAQ,CAACE,IAAI,CAACS,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACR,IAAI,GAAG,kBAAkB;IACrC,CAAC,MAAM,IAAIF,QAAQ,CAACE,IAAI,CAACS,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1CF,SAAS,CAACR,IAAI,GAAG,yCAAyC;IAC5D;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,CAACQ,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACP,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAACX,YAAY,CAACQ,QAAQ,CAACG,KAAK,CAAC,EAAE;MACxCO,SAAS,CAACP,KAAK,GAAG,oCAAoC;IACxD;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,CAACO,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACN,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,CAACQ,MAAM,GAAG,CAAC,EAAE;MACvCF,SAAS,CAACN,QAAQ,GAAG,6CAA6C;IACpE;IAEA,IAAI,CAACC,eAAe,CAACM,IAAI,CAAC,CAAC,EAAE;MAC3BD,SAAS,CAACL,eAAe,GAAG,8BAA8B;IAC5D,CAAC,MAAM,IAAIA,eAAe,KAAKL,QAAQ,CAACI,QAAQ,EAAE;MAChDM,SAAS,CAACL,eAAe,GAAG,wBAAwB;IACtD;IAEAG,SAAS,CAACE,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;IAErB,IAAI;MACF,MAAMX,QAAQ,CAACE,QAAQ,CAAC;MACxBH,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACd;IAAA;EAEJ,CAAC;EAED,MAAMC,iBAAiB,GAAIC,KAAyB,IAAMC,KAAa,IAAK;IAC1EpB,WAAW,CAACqB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;IAClD;IACA,IAAId,MAAM,CAACa,KAAK,CAAC,EAAE;MACjBZ,SAAS,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAGG;MAAU,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMC,2BAA2B,GAAIH,KAAa,IAAK;IACrDf,kBAAkB,CAACe,KAAK,CAAC;IACzB,IAAId,MAAM,CAACF,eAAe,EAAE;MAC1BG,SAAS,CAACc,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEjB,eAAe,EAAEkB;MAAU,CAAC,CAAC,CAAC;IAC9D;EACF,CAAC;EAED,oBACE7B,OAAA;IAAK+B,SAAS,EAAC,qFAAqF;IAAAC,QAAA,eAClGhC,OAAA;MAAK+B,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxChC,OAAA;QAAAgC,QAAA,gBACEhC,OAAA;UAAK+B,SAAS,EAAC,gFAAgF;UAAAC,QAAA,eAC7FhC,OAAA;YACE+B,SAAS,EAAC,0BAA0B;YACpCE,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,OAAO,EAAC,WAAW;YAAAH,QAAA,eAEnBhC,OAAA;cACEoC,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAAsH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3C,OAAA;UAAI+B,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEvE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3C,OAAA;UAAG+B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,GAAC,IAClD,EAAC,GAAG,eACNhC,OAAA,CAACP,IAAI;YACHmD,EAAE,EAAC,QAAQ;YACXb,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAChE;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN3C,OAAA;QAAM+B,SAAS,EAAC,gBAAgB;QAACc,QAAQ,EAAExB,YAAa;QAAAW,QAAA,gBACtDhC,OAAA;UAAK+B,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhC,OAAA,CAACH,KAAK;YACJiD,KAAK,EAAC,WAAW;YACjBC,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,sBAAsB;YAClCrB,KAAK,EAAErB,QAAQ,CAACE,IAAK;YACrByC,QAAQ,EAAExB,iBAAiB,CAAC,MAAM,CAAE;YACpCD,KAAK,EAAEX,MAAM,CAACL,IAAK;YACnB0C,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEF3C,OAAA,CAACH,KAAK;YACJiD,KAAK,EAAC,eAAe;YACrBC,IAAI,EAAC,OAAO;YACZC,WAAW,EAAC,kBAAkB;YAC9BrB,KAAK,EAAErB,QAAQ,CAACG,KAAM;YACtBwC,QAAQ,EAAExB,iBAAiB,CAAC,OAAO,CAAE;YACrCD,KAAK,EAAEX,MAAM,CAACJ,KAAM;YACpByC,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEF3C,OAAA,CAACH,KAAK;YACJiD,KAAK,EAAC,UAAU;YAChBC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,uCAAuC;YACnDrB,KAAK,EAAErB,QAAQ,CAACI,QAAS;YACzBuC,QAAQ,EAAExB,iBAAiB,CAAC,UAAU,CAAE;YACxCD,KAAK,EAAEX,MAAM,CAACH,QAAS;YACvBwC,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEF3C,OAAA,CAACH,KAAK;YACJiD,KAAK,EAAC,kBAAkB;YACxBC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,uBAAuB;YACnCrB,KAAK,EAAEhB,eAAgB;YACvBsC,QAAQ,EAAEnB,2BAA4B;YACtCN,KAAK,EAAEX,MAAM,CAACF,eAAgB;YAC9BuC,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN3C,OAAA;UAAK+B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChChC,OAAA;YACEmD,EAAE,EAAC,aAAa;YAChB3C,IAAI,EAAC,aAAa;YAClBuC,IAAI,EAAC,UAAU;YACfG,QAAQ;YACRnB,SAAS,EAAC;UAAyE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,eACF3C,OAAA;YAAOoD,OAAO,EAAC,aAAa;YAACrB,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAAC,gBAC1D,EAAC,GAAG,eAClBhC,OAAA;cACE+C,IAAI,EAAC,QAAQ;cACbhB,SAAS,EAAC,uFAAuF;cACjGsB,OAAO,EAAEA,CAAA,KAAMC,KAAK,CAAC,+BAA+B,CAAE;cAAAtB,QAAA,EACvD;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EAAC,KACX,EAAC,GAAG,eACP3C,OAAA;cACE+C,IAAI,EAAC,QAAQ;cACbhB,SAAS,EAAC,uFAAuF;cACjGsB,OAAO,EAAEA,CAAA,KAAMC,KAAK,CAAC,6BAA6B,CAAE;cAAAtB,QAAA,EACrD;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN3C,OAAA,CAACJ,MAAM;UACLmD,IAAI,EAAC,QAAQ;UACbhB,SAAS,EAAC,QAAQ;UAClBwB,OAAO,EAAElD,SAAU;UACnBmD,QAAQ,EAAEnD,SAAU;UAAA2B,QAAA,EACrB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP3C,OAAA;QAAK+B,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBhC,OAAA;UAAK+B,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBhC,OAAA;YAAK+B,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eACjDhC,OAAA;cAAK+B,SAAS,EAAC;YAAiC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACN3C,OAAA;YAAK+B,SAAS,EAAC,sCAAsC;YAAAC,QAAA,eACnDhC,OAAA;cAAM+B,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3C,OAAA;UAAK+B,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBhC,OAAA,CAACP,IAAI;YACHmD,EAAE,EAAC,QAAQ;YACXb,SAAS,EAAC,iJAAiJ;YAAAC,QAAA,EAC5J;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzC,EAAA,CAlNID,QAAkB;EAAA,QACLP,WAAW,EACIC,OAAO;AAAA;AAAA8D,EAAA,GAFnCxD,QAAkB;AAoNxB,eAAeA,QAAQ;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}