import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useCV } from '../contexts/CVContext';
import Button from '../components/ui/Button';
import ModernTemplate from '../components/templates/ModernTemplate';
import ClassicTemplate from '../components/templates/ClassicTemplate';
import apiService from '../services/api';

const CVPreview: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentCV, fetchCV } = useCV();
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  useEffect(() => {
    if (id && id !== 'new') {
      fetchCV(parseInt(id));
    }
  }, [id, fetchCV]);

  const handleGeneratePDF = async () => {
    if (!currentCV) return;

    try {
      setIsGeneratingPDF(true);
      const pdfBlob = await apiService.generatePDF(currentCV.id);
      
      // Create download link
      const url = window.URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${currentCV.title}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to generate PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const renderTemplate = () => {
    if (!currentCV) return null;

    switch (currentCV.template_type) {
      case 'modern':
        return <ModernTemplate data={currentCV.cv_data} />;
      case 'classic':
        return <ClassicTemplate data={currentCV.cv_data} />;
      case 'creative':
        return <ModernTemplate data={currentCV.cv_data} />; // Fallback to modern for now
      case 'minimal':
        return <ClassicTemplate data={currentCV.cv_data} />; // Fallback to classic for now
      default:
        return <ModernTemplate data={currentCV.cv_data} />;
    }
  };

  if (!currentCV) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Hidden when printing */}
      <div className="bg-white shadow-sm border-b print:hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{currentCV.title}</h1>
              <p className="text-gray-600">Preview your CV before downloading</p>
            </div>
            <div className="flex space-x-4">
              <Button
                onClick={() => navigate(`/cv-builder/${currentCV.id}`)}
                variant="outline"
              >
                Edit CV
              </Button>
              <Button
                onClick={handlePrint}
                variant="outline"
              >
                Print
              </Button>
              <Button
                onClick={handleGeneratePDF}
                loading={isGeneratingPDF}
                disabled={isGeneratingPDF}
              >
                Download PDF
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* CV Preview */}
      <div className="py-8 print:py-0">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 print:px-0 print:max-w-none">
          <div className="bg-white rounded-lg shadow-lg print:shadow-none print:rounded-none">
            {renderTemplate()}
          </div>
        </div>
      </div>

      {/* Template Switcher - Hidden when printing */}
      <div className="fixed bottom-6 right-6 print:hidden">
        <div className="bg-white rounded-lg shadow-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Template</h3>
          <div className="space-y-2">
            <button
              onClick={() => {
                // This would update the template type
                console.log('Switch to Modern template');
              }}
              className={`block w-full text-left px-3 py-2 rounded text-sm ${
                currentCV.template_type === 'modern'
                  ? 'bg-primary-100 text-primary-800'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              Modern
            </button>
            <button
              onClick={() => {
                // This would update the template type
                console.log('Switch to Classic template');
              }}
              className={`block w-full text-left px-3 py-2 rounded text-sm ${
                currentCV.template_type === 'classic'
                  ? 'bg-primary-100 text-primary-800'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              Classic
            </button>
            <button
              onClick={() => {
                // This would update the template type
                console.log('Switch to Creative template');
              }}
              className={`block w-full text-left px-3 py-2 rounded text-sm ${
                currentCV.template_type === 'creative'
                  ? 'bg-primary-100 text-primary-800'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              Creative
            </button>
            <button
              onClick={() => {
                // This would update the template type
                console.log('Switch to Minimal template');
              }}
              className={`block w-full text-left px-3 py-2 rounded text-sm ${
                currentCV.template_type === 'minimal'
                  ? 'bg-primary-100 text-primary-800'
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
            >
              Minimal
            </button>
          </div>
        </div>
      </div>

      {/* Print Styles */}
      <style>{`
        @media print {
          body {
            margin: 0;
            padding: 0;
          }
          .print\\:hidden {
            display: none !important;
          }
          .print\\:shadow-none {
            box-shadow: none !important;
          }
          .print\\:rounded-none {
            border-radius: 0 !important;
          }
          .print\\:py-0 {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
          }
          .print\\:px-0 {
            padding-left: 0 !important;
            padding-right: 0 !important;
          }
          .print\\:max-w-none {
            max-width: none !important;
          }
        }
      `}</style>
    </div>
  );
};

export default CVPreview;
