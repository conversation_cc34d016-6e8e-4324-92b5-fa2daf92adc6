# CV Generator - Full-Stack Web Application

A modern, full-stack CV/Resume generator built with React (TypeScript) frontend and Laravel backend. Create professional CVs with multiple templates, real-time preview, and PDF export functionality.

## 🚀 Features

### Core Features
- **User Authentication**: Secure registration and login system
- **Multi-Step CV Builder**: Comprehensive form with sections for:
  - Personal Information
  - Education
  - Work Experience
  - Skills
  - Projects
  - Languages
- **Multiple Templates**: Professional CV templates (Modern, Classic, Creative, Minimal)
- **Live Preview**: Real-time CV preview with template switching
- **PDF Export**: Generate and download professional PDFs
- **CV Management**: Create, edit, delete, and duplicate CVs
- **Responsive Design**: Mobile-first design with Tailwind CSS

### Advanced Features
- **Public CV Sharing**: Share CVs via public links
- **Template Switching**: Change templates on the fly
- **Auto-Save**: Automatic draft saving
- **Print Support**: Optimized for printing

## 🛠 Tech Stack

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **React Router** for navigation
- **Axios** for HTTP requests
- **Context API** for state management

### Backend
- **<PERSON>vel 12** framework
- **Laravel Sanctum** for API authentication
- **MySQL** database
- **DomPDF** for PDF generation
- **RESTful API** architecture

## Project Structure

```
cv-generator/
├── frontend/          # React frontend application
├── backend/           # Node.js backend API
├── database/          # Database schema and migrations
└── docs/             # Documentation
```

## Getting Started

### Prerequisites
- Node.js (v18 or higher)
- MySQL
- npm or yarn

### Installation

1. Clone the repository
2. Install backend dependencies:
   ```bash
   cd backend
   npm install
   ```

3. Install frontend dependencies:
   ```bash
   cd frontend
   npm install
   ```

4. Set up the database:
   ```bash
   # Create database and run migrations
   cd database
   # Import schema.sql into your MySQL database
   ```

5. Configure environment variables:
   ```bash
   # Copy .env.example to .env in backend directory
   # Update database credentials and JWT secret
   ```

6. Start the development servers:
   ```bash
   # Terminal 1 - Backend
   cd backend
   npm run dev

   # Terminal 2 - Frontend
   cd frontend
   npm start
   ```

## API Documentation

API documentation will be available at `/api/docs` when the server is running.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License.
