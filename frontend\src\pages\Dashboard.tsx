import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useCV } from '../contexts/CVContext';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/ui/Button';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { cvs, fetchCVs, isLoading } = useCV();

  useEffect(() => {
    fetchCVs();
  }, [fetchCVs]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.firstName} {user?.lastName}!
        </h1>
        <p className="mt-2 text-gray-600">
          Manage your CVs and create new ones
        </p>
      </div>

      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Your CVs</h2>
        <Link to="/cv-builder">
          <Button>Create New CV</Button>
        </Link>
      </div>

      {cvs.length === 0 ? (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No CVs yet</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first CV
          </p>
          <div className="mt-6">
            <Link to="/cv-builder">
              <Button>Create Your First CV</Button>
            </Link>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {cvs.map((cv) => (
            <div
              key={cv.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {cv.title}
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                Updated {new Date(cv.updatedAt).toLocaleDateString()}
              </p>
              <div className="flex space-x-2">
                <Link to={`/cv-builder/${cv.id}`}>
                  <Button size="sm" variant="outline">
                    Edit
                  </Button>
                </Link>
                <Link to={`/cv-preview/${cv.id}`}>
                  <Button size="sm" variant="outline">
                    Preview
                  </Button>
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Dashboard;
