{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\cv-generator-react\\\\src\\\\components\\\\ui\\\\Select.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Select = ({\n  label,\n  value,\n  onChange,\n  options,\n  error,\n  disabled = false,\n  required = false,\n  className = ''\n}) => {\n  const selectClasses = `\n    block w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm\n    ${error ? 'border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'}\n    ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'}\n    ${className}\n  `;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-1\",\n    children: [label && /*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-700\",\n      children: [label, required && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-red-500 ml-1\",\n        children: \"*\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 24\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n      className: selectClasses,\n      value: value,\n      onChange: e => onChange(e.target.value),\n      disabled: disabled,\n      required: required,\n      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n        value: \"\",\n        children: \"Select an option\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), options.map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n        value: option.value,\n        children: option.label\n      }, option.value, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-sm text-red-600\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_c = Select;\nexport default Select;\nvar _c;\n$RefreshReg$(_c, \"Select\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Select", "label", "value", "onChange", "options", "error", "disabled", "required", "className", "selectClasses", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "e", "target", "map", "option", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/cv-generator-react/src/components/ui/Select.tsx"], "sourcesContent": ["import React from 'react';\nimport { SelectProps } from '../../types';\n\nconst Select: React.FC<SelectProps> = ({\n  label,\n  value,\n  onChange,\n  options,\n  error,\n  disabled = false,\n  required = false,\n  className = '',\n}) => {\n  const selectClasses = `\n    block w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm\n    ${error \n      ? 'border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500' \n      : 'border-gray-300'\n    }\n    ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'}\n    ${className}\n  `;\n\n  return (\n    <div className=\"space-y-1\">\n      {label && (\n        <label className=\"block text-sm font-medium text-gray-700\">\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      <select\n        className={selectClasses}\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        disabled={disabled}\n        required={required}\n      >\n        <option value=\"\">Select an option</option>\n        {options.map((option) => (\n          <option key={option.value} value={option.value}>\n            {option.label}\n          </option>\n        ))}\n      </select>\n      {error && (\n        <p className=\"text-sm text-red-600\">{error}</p>\n      )}\n    </div>\n  );\n};\n\nexport default Select;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1B,MAAMC,MAA6B,GAAGA,CAAC;EACrCC,KAAK;EACLC,KAAK;EACLC,QAAQ;EACRC,OAAO;EACPC,KAAK;EACLC,QAAQ,GAAG,KAAK;EAChBC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,aAAa,GAAG;AACxB;AACA;AACA,MAAMJ,KAAK,GACH,qEAAqE,GACrE,iBAAiB;AACzB,MACMC,QAAQ,GAAG,+BAA+B,GAAG,UAAU;AAC7D,MAAME,SAAS;AACf,GAAG;EAED,oBACET,OAAA;IAAKS,SAAS,EAAC,WAAW;IAAAE,QAAA,GACvBT,KAAK,iBACJF,OAAA;MAAOS,SAAS,EAAC,yCAAyC;MAAAE,QAAA,GACvDT,KAAK,EACLM,QAAQ,iBAAIR,OAAA;QAAMS,SAAS,EAAC,mBAAmB;QAAAE,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CACR,eACDf,OAAA;MACES,SAAS,EAAEC,aAAc;MACzBP,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAGY,CAAC,IAAKZ,QAAQ,CAACY,CAAC,CAACC,MAAM,CAACd,KAAK,CAAE;MAC1CI,QAAQ,EAAEA,QAAS;MACnBC,QAAQ,EAAEA,QAAS;MAAAG,QAAA,gBAEnBX,OAAA;QAAQG,KAAK,EAAC,EAAE;QAAAQ,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACzCV,OAAO,CAACa,GAAG,CAAEC,MAAM,iBAClBnB,OAAA;QAA2BG,KAAK,EAAEgB,MAAM,CAAChB,KAAM;QAAAQ,QAAA,EAC5CQ,MAAM,CAACjB;MAAK,GADFiB,MAAM,CAAChB,KAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEjB,CACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EACRT,KAAK,iBACJN,OAAA;MAAGS,SAAS,EAAC,sBAAsB;MAAAE,QAAA,EAAEL;IAAK;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAC/C;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACK,EAAA,GAhDInB,MAA6B;AAkDnC,eAAeA,MAAM;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}