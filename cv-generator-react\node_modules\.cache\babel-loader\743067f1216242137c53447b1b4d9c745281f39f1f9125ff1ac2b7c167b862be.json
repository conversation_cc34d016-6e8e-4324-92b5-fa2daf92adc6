{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\cv-generator-react\\\\src\\\\components\\\\ui\\\\Button.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Button = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  onClick,\n  type = 'button',\n  className = ''\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n  const variantClasses = {\n    primary: 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500',\n    secondary: 'bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500',\n    outline: 'border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-primary-500',\n    ghost: 'bg-transparent hover:bg-gray-100 text-gray-700 focus:ring-primary-500',\n    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500'\n  };\n  const sizeClasses = {\n    sm: 'px-3 py-2 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base'\n  };\n  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    type: type,\n    className: classes,\n    disabled: disabled || loading,\n    onClick: onClick,\n    children: [loading && /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"animate-spin -ml-1 mr-2 h-4 w-4\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n        className: \"opacity-25\",\n        cx: \"12\",\n        cy: \"12\",\n        r: \"10\",\n        stroke: \"currentColor\",\n        strokeWidth: \"4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        className: \"opacity-75\",\n        fill: \"currentColor\",\n        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 9\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_c = Button;\nexport default Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "children", "variant", "size", "disabled", "loading", "onClick", "type", "className", "baseClasses", "variantClasses", "primary", "secondary", "outline", "ghost", "danger", "sizeClasses", "sm", "md", "lg", "classes", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/cv-generator-react/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { ButtonProps } from '../../types';\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  onClick,\n  type = 'button',\n  className = '',\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variantClasses = {\n    primary: 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500',\n    secondary: 'bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500',\n    outline: 'border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-primary-500',\n    ghost: 'bg-transparent hover:bg-gray-100 text-gray-700 focus:ring-primary-500',\n    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',\n  };\n  \n  const sizeClasses = {\n    sm: 'px-3 py-2 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-6 py-3 text-base',\n  };\n  \n  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;\n  \n  return (\n    <button\n      type={type}\n      className={classes}\n      disabled={disabled || loading}\n      onClick={onClick}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          ></circle>\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          ></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1B,MAAMC,MAA6B,GAAGA,CAAC;EACrCC,QAAQ;EACRC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,IAAI;EACXC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,OAAO;EACPC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAG,sLAAsL;EAE1M,MAAMC,cAAc,GAAG;IACrBC,OAAO,EAAE,uEAAuE;IAChFC,SAAS,EAAE,8DAA8D;IACzEC,OAAO,EAAE,uFAAuF;IAChGC,KAAK,EAAE,uEAAuE;IAC9EC,MAAM,EAAE;EACV,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE,mBAAmB;IACvBC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,OAAO,GAAG,GAAGX,WAAW,IAAIC,cAAc,CAACR,OAAO,CAAC,IAAIc,WAAW,CAACb,IAAI,CAAC,IAAIK,SAAS,EAAE;EAE7F,oBACET,OAAA;IACEQ,IAAI,EAAEA,IAAK;IACXC,SAAS,EAAEY,OAAQ;IACnBhB,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;IAC9BC,OAAO,EAAEA,OAAQ;IAAAL,QAAA,GAEhBI,OAAO,iBACNN,OAAA;MACES,SAAS,EAAC,iCAAiC;MAC3Ca,KAAK,EAAC,4BAA4B;MAClCC,IAAI,EAAC,MAAM;MACXC,OAAO,EAAC,WAAW;MAAAtB,QAAA,gBAEnBF,OAAA;QACES,SAAS,EAAC,YAAY;QACtBgB,EAAE,EAAC,IAAI;QACPC,EAAE,EAAC,IAAI;QACPC,CAAC,EAAC,IAAI;QACNC,MAAM,EAAC,cAAc;QACrBC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACVjC,OAAA;QACES,SAAS,EAAC,YAAY;QACtBc,IAAI,EAAC,cAAc;QACnBW,CAAC,EAAC;MAAiH;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9G,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACA/B,QAAQ;EAAA;IAAA4B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACE,EAAA,GA5DIlC,MAA6B;AA8DnC,eAAeA,MAAM;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}