{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\cv-generator-react\\\\src\\\\pages\\\\CVBuilder.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useCV } from '../contexts/CVContext';\nimport Button from '../components/ui/Button';\nimport Input from '../components/ui/Input';\nimport Select from '../components/ui/Select';\nimport Textarea from '../components/ui/Textarea';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CVBuilder = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    currentCV,\n    fetchCV,\n    createCV,\n    updateCV,\n    setCurrentCV\n  } = useCV();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [cvTitle, setCvTitle] = useState('');\n  const [templateType, setTemplateType] = useState('modern');\n  const [cvData, setCvData] = useState({\n    personal_info: {\n      first_name: '',\n      last_name: '',\n      email: '',\n      phone: '',\n      address: '',\n      website: '',\n      linkedin: '',\n      github: '',\n      summary: ''\n    },\n    education: [],\n    experience: [],\n    skills: [],\n    projects: [],\n    languages: []\n  });\n  const steps = [{\n    title: 'Basic Info',\n    component: 'basic'\n  }, {\n    title: 'Personal Info',\n    component: 'personal'\n  }, {\n    title: 'Education',\n    component: 'education'\n  }, {\n    title: 'Experience',\n    component: 'experience'\n  }, {\n    title: 'Skills',\n    component: 'skills'\n  }, {\n    title: 'Projects',\n    component: 'projects'\n  }, {\n    title: 'Languages',\n    component: 'languages'\n  }];\n  useEffect(() => {\n    if (id && id !== 'new') {\n      fetchCV(parseInt(id));\n    } else {\n      setCurrentCV(null);\n    }\n  }, [id, fetchCV, setCurrentCV]);\n  useEffect(() => {\n    if (currentCV) {\n      setCvTitle(currentCV.title);\n      setTemplateType(currentCV.template_type);\n      setCvData(currentCV.cv_data);\n    }\n  }, [currentCV]);\n  const handleSave = async () => {\n    try {\n      const cvPayload = {\n        title: cvTitle,\n        template_type: templateType,\n        cv_data: cvData\n      };\n      if (id && id !== 'new') {\n        await updateCV(parseInt(id), cvPayload);\n      } else {\n        const newCV = await createCV(cvPayload);\n        navigate(`/cv-builder/${newCV.id}`, {\n          replace: true\n        });\n      }\n    } catch (error) {\n      console.error('Failed to save CV:', error);\n    }\n  };\n  const handleNext = () => {\n    if (currentStep < steps.length - 1) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n  const handlePrevious = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  const updatePersonalInfo = (field, value) => {\n    setCvData(prev => ({\n      ...prev,\n      personal_info: {\n        ...prev.personal_info,\n        [field]: value\n      }\n    }));\n  };\n  const addEducation = () => {\n    const newEducation = {\n      id: Date.now().toString(),\n      institution: '',\n      degree: '',\n      field_of_study: '',\n      start_date: '',\n      end_date: '',\n      current: false,\n      description: '',\n      gpa: ''\n    };\n    setCvData(prev => ({\n      ...prev,\n      education: [...prev.education, newEducation]\n    }));\n  };\n  const updateEducation = (index, field, value) => {\n    setCvData(prev => ({\n      ...prev,\n      education: prev.education.map((edu, i) => i === index ? {\n        ...edu,\n        [field]: value\n      } : edu)\n    }));\n  };\n  const removeEducation = index => {\n    setCvData(prev => ({\n      ...prev,\n      education: prev.education.filter((_, i) => i !== index)\n    }));\n  };\n  const addExperience = () => {\n    const newExperience = {\n      id: Date.now().toString(),\n      company: '',\n      position: '',\n      start_date: '',\n      end_date: '',\n      current: false,\n      description: '',\n      achievements: []\n    };\n    setCvData(prev => ({\n      ...prev,\n      experience: [...prev.experience, newExperience]\n    }));\n  };\n  const updateExperience = (index, field, value) => {\n    setCvData(prev => ({\n      ...prev,\n      experience: prev.experience.map((exp, i) => i === index ? {\n        ...exp,\n        [field]: value\n      } : exp)\n    }));\n  };\n  const removeExperience = index => {\n    setCvData(prev => ({\n      ...prev,\n      experience: prev.experience.filter((_, i) => i !== index)\n    }));\n  };\n  const addSkill = () => {\n    const newSkill = {\n      id: Date.now().toString(),\n      name: '',\n      level: 'Intermediate',\n      category: ''\n    };\n    setCvData(prev => ({\n      ...prev,\n      skills: [...prev.skills, newSkill]\n    }));\n  };\n  const updateSkill = (index, field, value) => {\n    setCvData(prev => ({\n      ...prev,\n      skills: prev.skills.map((skill, i) => i === index ? {\n        ...skill,\n        [field]: value\n      } : skill)\n    }));\n  };\n  const removeSkill = index => {\n    setCvData(prev => ({\n      ...prev,\n      skills: prev.skills.filter((_, i) => i !== index)\n    }));\n  };\n  const renderBasicInfo = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-lg font-medium text-gray-900\",\n      children: \"Basic Information\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(Input, {\n        label: \"CV Title\",\n        placeholder: \"e.g., Software Engineer Resume\",\n        value: cvTitle,\n        onChange: setCvTitle,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        label: \"Template\",\n        value: templateType,\n        onChange: value => setTemplateType(value),\n        options: [{\n          value: 'modern',\n          label: 'Modern'\n        }, {\n          value: 'classic',\n          label: 'Classic'\n        }, {\n          value: 'creative',\n          label: 'Creative'\n        }, {\n          value: 'minimal',\n          label: 'Minimal'\n        }],\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 201,\n    columnNumber: 5\n  }, this);\n  const renderPersonalInfo = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-lg font-medium text-gray-900\",\n      children: \"Personal Information\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(Input, {\n        label: \"First Name\",\n        placeholder: \"John\",\n        value: cvData.personal_info.first_name,\n        onChange: value => updatePersonalInfo('first_name', value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Last Name\",\n        placeholder: \"Doe\",\n        value: cvData.personal_info.last_name,\n        onChange: value => updatePersonalInfo('last_name', value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Email\",\n        type: \"email\",\n        placeholder: \"<EMAIL>\",\n        value: cvData.personal_info.email,\n        onChange: value => updatePersonalInfo('email', value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Phone\",\n        type: \"tel\",\n        placeholder: \"+****************\",\n        value: cvData.personal_info.phone,\n        onChange: value => updatePersonalInfo('phone', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Address\",\n        placeholder: \"123 Main St, City, State 12345\",\n        value: cvData.personal_info.address,\n        onChange: value => updatePersonalInfo('address', value),\n        className: \"md:col-span-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Website\",\n        type: \"url\",\n        placeholder: \"https://johndoe.com\",\n        value: cvData.personal_info.website,\n        onChange: value => updatePersonalInfo('website', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"LinkedIn\",\n        type: \"url\",\n        placeholder: \"https://linkedin.com/in/johndoe\",\n        value: cvData.personal_info.linkedin,\n        onChange: value => updatePersonalInfo('linkedin', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"GitHub\",\n        type: \"url\",\n        placeholder: \"https://github.com/johndoe\",\n        value: cvData.personal_info.github,\n        onChange: value => updatePersonalInfo('github', value),\n        className: \"md:col-span-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Textarea, {\n      label: \"Professional Summary\",\n      placeholder: \"Brief description of your professional background and goals...\",\n      value: cvData.personal_info.summary,\n      onChange: value => updatePersonalInfo('summary', value),\n      rows: 4\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 228,\n    columnNumber: 5\n  }, this);\n  const renderEducation = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900\",\n        children: \"Education\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: addEducation,\n        size: \"sm\",\n        children: \"Add Education\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this), cvData.education.map((edu, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border border-gray-200 rounded-lg p-6 space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-md font-medium text-gray-800\",\n          children: [\"Education #\", index + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => removeEducation(index),\n          variant: \"danger\",\n          size: \"sm\",\n          children: \"Remove\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          label: \"Institution\",\n          placeholder: \"University of Example\",\n          value: edu.institution,\n          onChange: value => updateEducation(index, 'institution', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Degree\",\n          placeholder: \"Bachelor of Science\",\n          value: edu.degree,\n          onChange: value => updateEducation(index, 'degree', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Field of Study\",\n          placeholder: \"Computer Science\",\n          value: edu.field_of_study,\n          onChange: value => updateEducation(index, 'field_of_study', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"GPA (Optional)\",\n          placeholder: \"3.8\",\n          value: edu.gpa || '',\n          onChange: value => updateEducation(index, 'gpa', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Start Date\",\n          type: \"date\",\n          value: edu.start_date,\n          onChange: value => updateEducation(index, 'start_date', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"End Date\",\n          type: \"date\",\n          value: edu.end_date || '',\n          onChange: value => updateEducation(index, 'end_date', value),\n          disabled: edu.current\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          id: `current-education-${index}`,\n          checked: edu.current,\n          onChange: e => updateEducation(index, 'current', e.target.checked),\n          className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: `current-education-${index}`,\n          className: \"ml-2 text-sm text-gray-700\",\n          children: \"Currently studying here\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Textarea, {\n        label: \"Description (Optional)\",\n        placeholder: \"Relevant coursework, achievements, activities...\",\n        value: edu.description || '',\n        onChange: value => updateEducation(index, 'description', value),\n        rows: 3\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 11\n      }, this)]\n    }, edu.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 9\n    }, this)), cvData.education.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No education entries yet. Click \\\"Add Education\\\" to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 301,\n    columnNumber: 5\n  }, this);\n  const renderExperience = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900\",\n        children: \"Work Experience\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: addExperience,\n        size: \"sm\",\n        children: \"Add Experience\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 7\n    }, this), cvData.experience.map((exp, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border border-gray-200 rounded-lg p-6 space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-md font-medium text-gray-800\",\n          children: [\"Experience #\", index + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => removeExperience(index),\n          variant: \"danger\",\n          size: \"sm\",\n          children: \"Remove\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          label: \"Company\",\n          placeholder: \"Tech Company Inc.\",\n          value: exp.company,\n          onChange: value => updateExperience(index, 'company', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Position\",\n          placeholder: \"Software Engineer\",\n          value: exp.position,\n          onChange: value => updateExperience(index, 'position', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Start Date\",\n          type: \"date\",\n          value: exp.start_date,\n          onChange: value => updateExperience(index, 'start_date', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"End Date\",\n          type: \"date\",\n          value: exp.end_date || '',\n          onChange: value => updateExperience(index, 'end_date', value),\n          disabled: exp.current\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          id: `current-experience-${index}`,\n          checked: exp.current,\n          onChange: e => updateExperience(index, 'current', e.target.checked),\n          className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: `current-experience-${index}`,\n          className: \"ml-2 text-sm text-gray-700\",\n          children: \"Currently working here\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Textarea, {\n        label: \"Job Description\",\n        placeholder: \"Describe your responsibilities and achievements...\",\n        value: exp.description || '',\n        onChange: value => updateExperience(index, 'description', value),\n        rows: 4\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 11\n      }, this)]\n    }, exp.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 9\n    }, this)), cvData.experience.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No work experience entries yet. Click \\\"Add Experience\\\" to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 391,\n    columnNumber: 5\n  }, this);\n  const renderSkills = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900\",\n        children: \"Skills\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: addSkill,\n        size: \"sm\",\n        children: \"Add Skill\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n      children: cvData.skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border border-gray-200 rounded-lg p-4 space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-start\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-gray-800\",\n            children: [\"Skill #\", index + 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => removeSkill(index),\n            variant: \"danger\",\n            size: \"sm\",\n            children: \"Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Skill Name\",\n          placeholder: \"JavaScript\",\n          value: skill.name,\n          onChange: value => updateSkill(index, 'name', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          label: \"Proficiency Level\",\n          value: skill.level,\n          onChange: value => updateSkill(index, 'level', value),\n          options: [{\n            value: 'Beginner',\n            label: 'Beginner'\n          }, {\n            value: 'Intermediate',\n            label: 'Intermediate'\n          }, {\n            value: 'Advanced',\n            label: 'Advanced'\n          }, {\n            value: 'Expert',\n            label: 'Expert'\n          }],\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Category (Optional)\",\n          placeholder: \"Programming Languages\",\n          value: skill.category || '',\n          onChange: value => updateSkill(index, 'category', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 13\n        }, this)]\n      }, skill.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 7\n    }, this), cvData.skills.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No skills added yet. Click \\\"Add Skill\\\" to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 468,\n    columnNumber: 5\n  }, this);\n  const renderProjects = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900\",\n        children: \"Projects\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => {\n          const newProject = {\n            id: Date.now().toString(),\n            name: '',\n            description: '',\n            technologies: [],\n            start_date: '',\n            end_date: '',\n            url: '',\n            github_url: ''\n          };\n          setCvData(prev => ({\n            ...prev,\n            projects: [...prev.projects, newProject]\n          }));\n        },\n        size: \"sm\",\n        children: \"Add Project\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 7\n    }, this), cvData.projects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border border-gray-200 rounded-lg p-6 space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-md font-medium text-gray-800\",\n          children: [\"Project #\", index + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setCvData(prev => ({\n            ...prev,\n            projects: prev.projects.filter((_, i) => i !== index)\n          })),\n          variant: \"danger\",\n          size: \"sm\",\n          children: \"Remove\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          label: \"Project Name\",\n          placeholder: \"My Awesome Project\",\n          value: project.name,\n          onChange: value => setCvData(prev => ({\n            ...prev,\n            projects: prev.projects.map((p, i) => i === index ? {\n              ...p,\n              name: value\n            } : p)\n          })),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Technologies\",\n          placeholder: \"React, Node.js, MongoDB\",\n          value: project.technologies.join(', '),\n          onChange: value => setCvData(prev => ({\n            ...prev,\n            projects: prev.projects.map((p, i) => i === index ? {\n              ...p,\n              technologies: value.split(',').map(t => t.trim())\n            } : p)\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Project URL (Optional)\",\n          type: \"url\",\n          placeholder: \"https://myproject.com\",\n          value: project.url || '',\n          onChange: value => setCvData(prev => ({\n            ...prev,\n            projects: prev.projects.map((p, i) => i === index ? {\n              ...p,\n              url: value\n            } : p)\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"GitHub URL (Optional)\",\n          type: \"url\",\n          placeholder: \"https://github.com/user/project\",\n          value: project.github_url || '',\n          onChange: value => setCvData(prev => ({\n            ...prev,\n            projects: prev.projects.map((p, i) => i === index ? {\n              ...p,\n              github_url: value\n            } : p)\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Textarea, {\n        label: \"Project Description\",\n        placeholder: \"Describe what the project does and your role...\",\n        value: project.description,\n        onChange: value => setCvData(prev => ({\n          ...prev,\n          projects: prev.projects.map((p, i) => i === index ? {\n            ...p,\n            description: value\n          } : p)\n        })),\n        rows: 3\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 11\n      }, this)]\n    }, project.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 9\n    }, this)), cvData.projects.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No projects added yet. Click \\\"Add Project\\\" to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 523,\n    columnNumber: 5\n  }, this);\n  const renderLanguages = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900\",\n        children: \"Languages\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => {\n          const newLanguage = {\n            id: Date.now().toString(),\n            name: '',\n            proficiency: 'Conversational'\n          };\n          setCvData(prev => ({\n            ...prev,\n            languages: [...prev.languages, newLanguage]\n          }));\n        },\n        size: \"sm\",\n        children: \"Add Language\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 633,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 631,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n      children: cvData.languages.map((language, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border border-gray-200 rounded-lg p-4 space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-start\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-gray-800\",\n            children: [\"Language #\", index + 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setCvData(prev => ({\n              ...prev,\n              languages: prev.languages.filter((_, i) => i !== index)\n            })),\n            variant: \"danger\",\n            size: \"sm\",\n            children: \"Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Language\",\n          placeholder: \"English\",\n          value: language.name,\n          onChange: value => setCvData(prev => ({\n            ...prev,\n            languages: prev.languages.map((l, i) => i === index ? {\n              ...l,\n              name: value\n            } : l)\n          })),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          label: \"Proficiency Level\",\n          value: language.proficiency,\n          onChange: value => setCvData(prev => ({\n            ...prev,\n            languages: prev.languages.map((l, i) => i === index ? {\n              ...l,\n              proficiency: value\n            } : l)\n          })),\n          options: [{\n            value: 'Basic',\n            label: 'Basic'\n          }, {\n            value: 'Conversational',\n            label: 'Conversational'\n          }, {\n            value: 'Fluent',\n            label: 'Fluent'\n          }, {\n            value: 'Native',\n            label: 'Native'\n          }],\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 13\n        }, this)]\n      }, language.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 647,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 645,\n      columnNumber: 7\n    }, this), cvData.languages.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No languages added yet. Click \\\"Add Language\\\" to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 695,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 694,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 630,\n    columnNumber: 5\n  }, this);\n  const renderCurrentStep = () => {\n    switch (steps[currentStep].component) {\n      case 'basic':\n        return renderBasicInfo();\n      case 'personal':\n        return renderPersonalInfo();\n      case 'education':\n        return renderEducation();\n      case 'experience':\n        return renderExperience();\n      case 'skills':\n        return renderSkills();\n      case 'projects':\n        return renderProjects();\n      case 'languages':\n        return renderLanguages();\n      default:\n        return renderBasicInfo();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-900\",\n        children: id && id !== 'new' ? 'Edit CV' : 'Create New CV'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-2 text-gray-600\",\n        children: \"Fill out the information below to create your professional CV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 729,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 725,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        \"aria-label\": \"Progress\",\n        children: /*#__PURE__*/_jsxDEV(\"ol\", {\n          className: \"flex items-center\",\n          children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: `relative ${index !== steps.length - 1 ? 'pr-8 sm:pr-20' : ''}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `relative flex h-8 w-8 items-center justify-center rounded-full ${index < currentStep ? 'bg-primary-600' : index === currentStep ? 'border-2 border-primary-600 bg-white' : 'border-2 border-gray-300 bg-white'}`,\n                children: index < currentStep ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-white\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 752,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 751,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-sm font-medium ${index === currentStep ? 'text-primary-600' : 'text-gray-500'}`,\n                  children: index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `ml-4 text-sm font-medium ${index === currentStep ? 'text-primary-600' : 'text-gray-500'}`,\n                children: step.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 17\n            }, this), index !== steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300\",\n              \"aria-hidden\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 19\n            }, this)]\n          }, step.title, true, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 736,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 735,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-sm rounded-lg p-6 mb-8\",\n      children: renderCurrentStep()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 774,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: currentStep > 0 && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePrevious,\n          variant: \"outline\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 782,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 780,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSave,\n          variant: \"outline\",\n          children: \"Save Draft\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 11\n        }, this), currentStep < steps.length - 1 ? /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleNext,\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => navigate(`/cv-preview/${(currentCV === null || currentCV === void 0 ? void 0 : currentCV.id) || 'new'}`),\n          children: \"Preview CV\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 802,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 790,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 779,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 723,\n    columnNumber: 5\n  }, this);\n};\n_s(CVBuilder, \"wJ+6w1BvdvPEml4cEZ+wUR8AtrQ=\", false, function () {\n  return [useParams, useNavigate, useCV];\n});\n_c = CVBuilder;\nexport default CVBuilder;\nvar _c;\n$RefreshReg$(_c, \"CVBuilder\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useCV", "<PERSON><PERSON>", "Input", "Select", "Textarea", "jsxDEV", "_jsxDEV", "CVBuilder", "_s", "id", "navigate", "currentCV", "fetchCV", "createCV", "updateCV", "setCurrentCV", "currentStep", "setCurrentStep", "cvTitle", "setCvTitle", "templateType", "setTemplateType", "cvData", "setCvData", "personal_info", "first_name", "last_name", "email", "phone", "address", "website", "linkedin", "github", "summary", "education", "experience", "skills", "projects", "languages", "steps", "title", "component", "parseInt", "template_type", "cv_data", "handleSave", "cvPayload", "newCV", "replace", "error", "console", "handleNext", "length", "handlePrevious", "updatePersonalInfo", "field", "value", "prev", "addEducation", "newEducation", "Date", "now", "toString", "institution", "degree", "field_of_study", "start_date", "end_date", "current", "description", "gpa", "updateEducation", "index", "map", "edu", "i", "removeEducation", "filter", "_", "addExperience", "newExperience", "company", "position", "achievements", "updateExperience", "exp", "removeExperience", "addSkill", "newSkill", "name", "level", "category", "updateSkill", "skill", "removeSkill", "renderBasicInfo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "placeholder", "onChange", "required", "options", "renderPersonalInfo", "type", "rows", "renderEducation", "onClick", "size", "variant", "disabled", "checked", "e", "target", "htmlFor", "renderExperience", "renderSkills", "renderProjects", "newProject", "technologies", "url", "github_url", "project", "p", "join", "split", "t", "trim", "renderLanguages", "newLanguage", "proficiency", "language", "l", "renderCurrentStep", "step", "fill", "viewBox", "fillRule", "d", "clipRule", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/cv-generator-react/src/pages/CVBuilder.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useCV } from '../contexts/CVContext';\nimport Button from '../components/ui/Button';\nimport Input from '../components/ui/Input';\nimport Select from '../components/ui/Select';\nimport Textarea from '../components/ui/Textarea';\nimport { CVData, PersonalInfo, Education, Experience, Skill, Project, Language } from '../types';\n\nconst CVBuilder: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const { currentCV, fetchCV, createCV, updateCV, setCurrentCV } = useCV();\n  \n  const [currentStep, setCurrentStep] = useState(0);\n  const [cvTitle, setCvTitle] = useState('');\n  const [templateType, setTemplateType] = useState<'modern' | 'classic' | 'creative' | 'minimal'>('modern');\n  const [cvData, setCvData] = useState<CVData>({\n    personal_info: {\n      first_name: '',\n      last_name: '',\n      email: '',\n      phone: '',\n      address: '',\n      website: '',\n      linkedin: '',\n      github: '',\n      summary: '',\n    },\n    education: [],\n    experience: [],\n    skills: [],\n    projects: [],\n    languages: [],\n  });\n\n  const steps = [\n    { title: 'Basic Info', component: 'basic' },\n    { title: 'Personal Info', component: 'personal' },\n    { title: 'Education', component: 'education' },\n    { title: 'Experience', component: 'experience' },\n    { title: 'Skills', component: 'skills' },\n    { title: 'Projects', component: 'projects' },\n    { title: 'Languages', component: 'languages' },\n  ];\n\n  useEffect(() => {\n    if (id && id !== 'new') {\n      fetchCV(parseInt(id));\n    } else {\n      setCurrentCV(null);\n    }\n  }, [id, fetchCV, setCurrentCV]);\n\n  useEffect(() => {\n    if (currentCV) {\n      setCvTitle(currentCV.title);\n      setTemplateType(currentCV.template_type);\n      setCvData(currentCV.cv_data);\n    }\n  }, [currentCV]);\n\n  const handleSave = async () => {\n    try {\n      const cvPayload = {\n        title: cvTitle,\n        template_type: templateType,\n        cv_data: cvData,\n      };\n\n      if (id && id !== 'new') {\n        await updateCV(parseInt(id), cvPayload);\n      } else {\n        const newCV = await createCV(cvPayload);\n        navigate(`/cv-builder/${newCV.id}`, { replace: true });\n      }\n    } catch (error) {\n      console.error('Failed to save CV:', error);\n    }\n  };\n\n  const handleNext = () => {\n    if (currentStep < steps.length - 1) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const updatePersonalInfo = (field: keyof PersonalInfo, value: string) => {\n    setCvData(prev => ({\n      ...prev,\n      personal_info: {\n        ...prev.personal_info,\n        [field]: value,\n      },\n    }));\n  };\n\n  const addEducation = () => {\n    const newEducation: Education = {\n      id: Date.now().toString(),\n      institution: '',\n      degree: '',\n      field_of_study: '',\n      start_date: '',\n      end_date: '',\n      current: false,\n      description: '',\n      gpa: '',\n    };\n    setCvData(prev => ({\n      ...prev,\n      education: [...prev.education, newEducation],\n    }));\n  };\n\n  const updateEducation = (index: number, field: keyof Education, value: string | boolean) => {\n    setCvData(prev => ({\n      ...prev,\n      education: prev.education.map((edu, i) => \n        i === index ? { ...edu, [field]: value } : edu\n      ),\n    }));\n  };\n\n  const removeEducation = (index: number) => {\n    setCvData(prev => ({\n      ...prev,\n      education: prev.education.filter((_, i) => i !== index),\n    }));\n  };\n\n  const addExperience = () => {\n    const newExperience: Experience = {\n      id: Date.now().toString(),\n      company: '',\n      position: '',\n      start_date: '',\n      end_date: '',\n      current: false,\n      description: '',\n      achievements: [],\n    };\n    setCvData(prev => ({\n      ...prev,\n      experience: [...prev.experience, newExperience],\n    }));\n  };\n\n  const updateExperience = (index: number, field: keyof Experience, value: string | boolean | string[]) => {\n    setCvData(prev => ({\n      ...prev,\n      experience: prev.experience.map((exp, i) => \n        i === index ? { ...exp, [field]: value } : exp\n      ),\n    }));\n  };\n\n  const removeExperience = (index: number) => {\n    setCvData(prev => ({\n      ...prev,\n      experience: prev.experience.filter((_, i) => i !== index),\n    }));\n  };\n\n  const addSkill = () => {\n    const newSkill: Skill = {\n      id: Date.now().toString(),\n      name: '',\n      level: 'Intermediate',\n      category: '',\n    };\n    setCvData(prev => ({\n      ...prev,\n      skills: [...prev.skills, newSkill],\n    }));\n  };\n\n  const updateSkill = (index: number, field: keyof Skill, value: string) => {\n    setCvData(prev => ({\n      ...prev,\n      skills: prev.skills.map((skill, i) => \n        i === index ? { ...skill, [field]: value } : skill\n      ),\n    }));\n  };\n\n  const removeSkill = (index: number) => {\n    setCvData(prev => ({\n      ...prev,\n      skills: prev.skills.filter((_, i) => i !== index),\n    }));\n  };\n\n  const renderBasicInfo = () => (\n    <div className=\"space-y-6\">\n      <h3 className=\"text-lg font-medium text-gray-900\">Basic Information</h3>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <Input\n          label=\"CV Title\"\n          placeholder=\"e.g., Software Engineer Resume\"\n          value={cvTitle}\n          onChange={setCvTitle}\n          required\n        />\n        <Select\n          label=\"Template\"\n          value={templateType}\n          onChange={(value) => setTemplateType(value as any)}\n          options={[\n            { value: 'modern', label: 'Modern' },\n            { value: 'classic', label: 'Classic' },\n            { value: 'creative', label: 'Creative' },\n            { value: 'minimal', label: 'Minimal' },\n          ]}\n          required\n        />\n      </div>\n    </div>\n  );\n\n  const renderPersonalInfo = () => (\n    <div className=\"space-y-6\">\n      <h3 className=\"text-lg font-medium text-gray-900\">Personal Information</h3>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <Input\n          label=\"First Name\"\n          placeholder=\"John\"\n          value={cvData.personal_info.first_name}\n          onChange={(value) => updatePersonalInfo('first_name', value)}\n          required\n        />\n        <Input\n          label=\"Last Name\"\n          placeholder=\"Doe\"\n          value={cvData.personal_info.last_name}\n          onChange={(value) => updatePersonalInfo('last_name', value)}\n          required\n        />\n        <Input\n          label=\"Email\"\n          type=\"email\"\n          placeholder=\"<EMAIL>\"\n          value={cvData.personal_info.email}\n          onChange={(value) => updatePersonalInfo('email', value)}\n          required\n        />\n        <Input\n          label=\"Phone\"\n          type=\"tel\"\n          placeholder=\"+****************\"\n          value={cvData.personal_info.phone}\n          onChange={(value) => updatePersonalInfo('phone', value)}\n        />\n        <Input\n          label=\"Address\"\n          placeholder=\"123 Main St, City, State 12345\"\n          value={cvData.personal_info.address}\n          onChange={(value) => updatePersonalInfo('address', value)}\n          className=\"md:col-span-2\"\n        />\n        <Input\n          label=\"Website\"\n          type=\"url\"\n          placeholder=\"https://johndoe.com\"\n          value={cvData.personal_info.website}\n          onChange={(value) => updatePersonalInfo('website', value)}\n        />\n        <Input\n          label=\"LinkedIn\"\n          type=\"url\"\n          placeholder=\"https://linkedin.com/in/johndoe\"\n          value={cvData.personal_info.linkedin}\n          onChange={(value) => updatePersonalInfo('linkedin', value)}\n        />\n        <Input\n          label=\"GitHub\"\n          type=\"url\"\n          placeholder=\"https://github.com/johndoe\"\n          value={cvData.personal_info.github}\n          onChange={(value) => updatePersonalInfo('github', value)}\n          className=\"md:col-span-2\"\n        />\n      </div>\n      <Textarea\n        label=\"Professional Summary\"\n        placeholder=\"Brief description of your professional background and goals...\"\n        value={cvData.personal_info.summary}\n        onChange={(value) => updatePersonalInfo('summary', value)}\n        rows={4}\n      />\n    </div>\n  );\n\n  const renderEducation = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Education</h3>\n        <Button onClick={addEducation} size=\"sm\">Add Education</Button>\n      </div>\n      {cvData.education.map((edu, index) => (\n        <div key={edu.id} className=\"border border-gray-200 rounded-lg p-6 space-y-4\">\n          <div className=\"flex justify-between items-start\">\n            <h4 className=\"text-md font-medium text-gray-800\">Education #{index + 1}</h4>\n            <Button\n              onClick={() => removeEducation(index)}\n              variant=\"danger\"\n              size=\"sm\"\n            >\n              Remove\n            </Button>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <Input\n              label=\"Institution\"\n              placeholder=\"University of Example\"\n              value={edu.institution}\n              onChange={(value) => updateEducation(index, 'institution', value)}\n              required\n            />\n            <Input\n              label=\"Degree\"\n              placeholder=\"Bachelor of Science\"\n              value={edu.degree}\n              onChange={(value) => updateEducation(index, 'degree', value)}\n              required\n            />\n            <Input\n              label=\"Field of Study\"\n              placeholder=\"Computer Science\"\n              value={edu.field_of_study}\n              onChange={(value) => updateEducation(index, 'field_of_study', value)}\n              required\n            />\n            <Input\n              label=\"GPA (Optional)\"\n              placeholder=\"3.8\"\n              value={edu.gpa || ''}\n              onChange={(value) => updateEducation(index, 'gpa', value)}\n            />\n            <Input\n              label=\"Start Date\"\n              type=\"date\"\n              value={edu.start_date}\n              onChange={(value) => updateEducation(index, 'start_date', value)}\n              required\n            />\n            <Input\n              label=\"End Date\"\n              type=\"date\"\n              value={edu.end_date || ''}\n              onChange={(value) => updateEducation(index, 'end_date', value)}\n              disabled={edu.current}\n            />\n          </div>\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              id={`current-education-${index}`}\n              checked={edu.current}\n              onChange={(e) => updateEducation(index, 'current', e.target.checked)}\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label htmlFor={`current-education-${index}`} className=\"ml-2 text-sm text-gray-700\">\n              Currently studying here\n            </label>\n          </div>\n          <Textarea\n            label=\"Description (Optional)\"\n            placeholder=\"Relevant coursework, achievements, activities...\"\n            value={edu.description || ''}\n            onChange={(value) => updateEducation(index, 'description', value)}\n            rows={3}\n          />\n        </div>\n      ))}\n      {cvData.education.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <p>No education entries yet. Click \"Add Education\" to get started.</p>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderExperience = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Work Experience</h3>\n        <Button onClick={addExperience} size=\"sm\">Add Experience</Button>\n      </div>\n      {cvData.experience.map((exp, index) => (\n        <div key={exp.id} className=\"border border-gray-200 rounded-lg p-6 space-y-4\">\n          <div className=\"flex justify-between items-start\">\n            <h4 className=\"text-md font-medium text-gray-800\">Experience #{index + 1}</h4>\n            <Button\n              onClick={() => removeExperience(index)}\n              variant=\"danger\"\n              size=\"sm\"\n            >\n              Remove\n            </Button>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <Input\n              label=\"Company\"\n              placeholder=\"Tech Company Inc.\"\n              value={exp.company}\n              onChange={(value) => updateExperience(index, 'company', value)}\n              required\n            />\n            <Input\n              label=\"Position\"\n              placeholder=\"Software Engineer\"\n              value={exp.position}\n              onChange={(value) => updateExperience(index, 'position', value)}\n              required\n            />\n            <Input\n              label=\"Start Date\"\n              type=\"date\"\n              value={exp.start_date}\n              onChange={(value) => updateExperience(index, 'start_date', value)}\n              required\n            />\n            <Input\n              label=\"End Date\"\n              type=\"date\"\n              value={exp.end_date || ''}\n              onChange={(value) => updateExperience(index, 'end_date', value)}\n              disabled={exp.current}\n            />\n          </div>\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              id={`current-experience-${index}`}\n              checked={exp.current}\n              onChange={(e) => updateExperience(index, 'current', e.target.checked)}\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label htmlFor={`current-experience-${index}`} className=\"ml-2 text-sm text-gray-700\">\n              Currently working here\n            </label>\n          </div>\n          <Textarea\n            label=\"Job Description\"\n            placeholder=\"Describe your responsibilities and achievements...\"\n            value={exp.description || ''}\n            onChange={(value) => updateExperience(index, 'description', value)}\n            rows={4}\n          />\n        </div>\n      ))}\n      {cvData.experience.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <p>No work experience entries yet. Click \"Add Experience\" to get started.</p>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderSkills = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Skills</h3>\n        <Button onClick={addSkill} size=\"sm\">Add Skill</Button>\n      </div>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        {cvData.skills.map((skill, index) => (\n          <div key={skill.id} className=\"border border-gray-200 rounded-lg p-4 space-y-3\">\n            <div className=\"flex justify-between items-start\">\n              <h4 className=\"text-sm font-medium text-gray-800\">Skill #{index + 1}</h4>\n              <Button\n                onClick={() => removeSkill(index)}\n                variant=\"danger\"\n                size=\"sm\"\n              >\n                Remove\n              </Button>\n            </div>\n            <Input\n              label=\"Skill Name\"\n              placeholder=\"JavaScript\"\n              value={skill.name}\n              onChange={(value) => updateSkill(index, 'name', value)}\n              required\n            />\n            <Select\n              label=\"Proficiency Level\"\n              value={skill.level}\n              onChange={(value) => updateSkill(index, 'level', value)}\n              options={[\n                { value: 'Beginner', label: 'Beginner' },\n                { value: 'Intermediate', label: 'Intermediate' },\n                { value: 'Advanced', label: 'Advanced' },\n                { value: 'Expert', label: 'Expert' },\n              ]}\n              required\n            />\n            <Input\n              label=\"Category (Optional)\"\n              placeholder=\"Programming Languages\"\n              value={skill.category || ''}\n              onChange={(value) => updateSkill(index, 'category', value)}\n            />\n          </div>\n        ))}\n      </div>\n      {cvData.skills.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <p>No skills added yet. Click \"Add Skill\" to get started.</p>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderProjects = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Projects</h3>\n        <Button onClick={() => {\n          const newProject: Project = {\n            id: Date.now().toString(),\n            name: '',\n            description: '',\n            technologies: [],\n            start_date: '',\n            end_date: '',\n            url: '',\n            github_url: '',\n          };\n          setCvData(prev => ({\n            ...prev,\n            projects: [...prev.projects, newProject],\n          }));\n        }} size=\"sm\">Add Project</Button>\n      </div>\n      {cvData.projects.map((project, index) => (\n        <div key={project.id} className=\"border border-gray-200 rounded-lg p-6 space-y-4\">\n          <div className=\"flex justify-between items-start\">\n            <h4 className=\"text-md font-medium text-gray-800\">Project #{index + 1}</h4>\n            <Button\n              onClick={() => setCvData(prev => ({\n                ...prev,\n                projects: prev.projects.filter((_, i) => i !== index),\n              }))}\n              variant=\"danger\"\n              size=\"sm\"\n            >\n              Remove\n            </Button>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <Input\n              label=\"Project Name\"\n              placeholder=\"My Awesome Project\"\n              value={project.name}\n              onChange={(value) => setCvData(prev => ({\n                ...prev,\n                projects: prev.projects.map((p, i) =>\n                  i === index ? { ...p, name: value } : p\n                ),\n              }))}\n              required\n            />\n            <Input\n              label=\"Technologies\"\n              placeholder=\"React, Node.js, MongoDB\"\n              value={project.technologies.join(', ')}\n              onChange={(value) => setCvData(prev => ({\n                ...prev,\n                projects: prev.projects.map((p, i) =>\n                  i === index ? { ...p, technologies: value.split(',').map(t => t.trim()) } : p\n                ),\n              }))}\n            />\n            <Input\n              label=\"Project URL (Optional)\"\n              type=\"url\"\n              placeholder=\"https://myproject.com\"\n              value={project.url || ''}\n              onChange={(value) => setCvData(prev => ({\n                ...prev,\n                projects: prev.projects.map((p, i) =>\n                  i === index ? { ...p, url: value } : p\n                ),\n              }))}\n            />\n            <Input\n              label=\"GitHub URL (Optional)\"\n              type=\"url\"\n              placeholder=\"https://github.com/user/project\"\n              value={project.github_url || ''}\n              onChange={(value) => setCvData(prev => ({\n                ...prev,\n                projects: prev.projects.map((p, i) =>\n                  i === index ? { ...p, github_url: value } : p\n                ),\n              }))}\n            />\n          </div>\n          <Textarea\n            label=\"Project Description\"\n            placeholder=\"Describe what the project does and your role...\"\n            value={project.description}\n            onChange={(value) => setCvData(prev => ({\n              ...prev,\n              projects: prev.projects.map((p, i) =>\n                i === index ? { ...p, description: value } : p\n              ),\n            }))}\n            rows={3}\n          />\n        </div>\n      ))}\n      {cvData.projects.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <p>No projects added yet. Click \"Add Project\" to get started.</p>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderLanguages = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Languages</h3>\n        <Button onClick={() => {\n          const newLanguage: Language = {\n            id: Date.now().toString(),\n            name: '',\n            proficiency: 'Conversational',\n          };\n          setCvData(prev => ({\n            ...prev,\n            languages: [...prev.languages, newLanguage],\n          }));\n        }} size=\"sm\">Add Language</Button>\n      </div>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        {cvData.languages.map((language, index) => (\n          <div key={language.id} className=\"border border-gray-200 rounded-lg p-4 space-y-3\">\n            <div className=\"flex justify-between items-start\">\n              <h4 className=\"text-sm font-medium text-gray-800\">Language #{index + 1}</h4>\n              <Button\n                onClick={() => setCvData(prev => ({\n                  ...prev,\n                  languages: prev.languages.filter((_, i) => i !== index),\n                }))}\n                variant=\"danger\"\n                size=\"sm\"\n              >\n                Remove\n              </Button>\n            </div>\n            <Input\n              label=\"Language\"\n              placeholder=\"English\"\n              value={language.name}\n              onChange={(value) => setCvData(prev => ({\n                ...prev,\n                languages: prev.languages.map((l, i) =>\n                  i === index ? { ...l, name: value } : l\n                ),\n              }))}\n              required\n            />\n            <Select\n              label=\"Proficiency Level\"\n              value={language.proficiency}\n              onChange={(value) => setCvData(prev => ({\n                ...prev,\n                languages: prev.languages.map((l, i) =>\n                  i === index ? { ...l, proficiency: value as any } : l\n                ),\n              }))}\n              options={[\n                { value: 'Basic', label: 'Basic' },\n                { value: 'Conversational', label: 'Conversational' },\n                { value: 'Fluent', label: 'Fluent' },\n                { value: 'Native', label: 'Native' },\n              ]}\n              required\n            />\n          </div>\n        ))}\n      </div>\n      {cvData.languages.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <p>No languages added yet. Click \"Add Language\" to get started.</p>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderCurrentStep = () => {\n    switch (steps[currentStep].component) {\n      case 'basic':\n        return renderBasicInfo();\n      case 'personal':\n        return renderPersonalInfo();\n      case 'education':\n        return renderEducation();\n      case 'experience':\n        return renderExperience();\n      case 'skills':\n        return renderSkills();\n      case 'projects':\n        return renderProjects();\n      case 'languages':\n        return renderLanguages();\n      default:\n        return renderBasicInfo();\n    }\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900\">\n          {id && id !== 'new' ? 'Edit CV' : 'Create New CV'}\n        </h1>\n        <p className=\"mt-2 text-gray-600\">\n          Fill out the information below to create your professional CV\n        </p>\n      </div>\n\n      {/* Progress Steps */}\n      <div className=\"mb-8\">\n        <nav aria-label=\"Progress\">\n          <ol className=\"flex items-center\">\n            {steps.map((step, index) => (\n              <li key={step.title} className={`relative ${index !== steps.length - 1 ? 'pr-8 sm:pr-20' : ''}`}>\n                <div className=\"flex items-center\">\n                  <div\n                    className={`relative flex h-8 w-8 items-center justify-center rounded-full ${\n                      index < currentStep\n                        ? 'bg-primary-600'\n                        : index === currentStep\n                        ? 'border-2 border-primary-600 bg-white'\n                        : 'border-2 border-gray-300 bg-white'\n                    }`}\n                  >\n                    {index < currentStep ? (\n                      <svg className=\"h-5 w-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    ) : (\n                      <span className={`text-sm font-medium ${index === currentStep ? 'text-primary-600' : 'text-gray-500'}`}>\n                        {index + 1}\n                      </span>\n                    )}\n                  </div>\n                  <span className={`ml-4 text-sm font-medium ${index === currentStep ? 'text-primary-600' : 'text-gray-500'}`}>\n                    {step.title}\n                  </span>\n                </div>\n                {index !== steps.length - 1 && (\n                  <div className=\"absolute top-4 left-4 -ml-px mt-0.5 h-full w-0.5 bg-gray-300\" aria-hidden=\"true\" />\n                )}\n              </li>\n            ))}\n          </ol>\n        </nav>\n      </div>\n\n      {/* Form Content */}\n      <div className=\"bg-white shadow-sm rounded-lg p-6 mb-8\">\n        {renderCurrentStep()}\n      </div>\n\n      {/* Navigation Buttons */}\n      <div className=\"flex justify-between\">\n        <div>\n          {currentStep > 0 && (\n            <Button\n              onClick={handlePrevious}\n              variant=\"outline\"\n            >\n              Previous\n            </Button>\n          )}\n        </div>\n        <div className=\"flex space-x-4\">\n          <Button\n            onClick={handleSave}\n            variant=\"outline\"\n          >\n            Save Draft\n          </Button>\n          {currentStep < steps.length - 1 ? (\n            <Button onClick={handleNext}>\n              Next\n            </Button>\n          ) : (\n            <Button onClick={() => navigate(`/cv-preview/${currentCV?.id || 'new'}`)}>\n              Preview CV\n            </Button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CVBuilder;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,QAAQ,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGjD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAG,CAAC,GAAGX,SAAS,CAAiB,CAAC;EAC1C,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,SAAS;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC;EAAa,CAAC,GAAGf,KAAK,CAAC,CAAC;EAExE,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAgD,QAAQ,CAAC;EACzG,MAAM,CAAC0B,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAS;IAC3C4B,aAAa,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE;IACX,CAAC;IACDC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,YAAY;IAAEC,SAAS,EAAE;EAAQ,CAAC,EAC3C;IAAED,KAAK,EAAE,eAAe;IAAEC,SAAS,EAAE;EAAW,CAAC,EACjD;IAAED,KAAK,EAAE,WAAW;IAAEC,SAAS,EAAE;EAAY,CAAC,EAC9C;IAAED,KAAK,EAAE,YAAY;IAAEC,SAAS,EAAE;EAAa,CAAC,EAChD;IAAED,KAAK,EAAE,QAAQ;IAAEC,SAAS,EAAE;EAAS,CAAC,EACxC;IAAED,KAAK,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAW,CAAC,EAC5C;IAAED,KAAK,EAAE,WAAW;IAAEC,SAAS,EAAE;EAAY,CAAC,CAC/C;EAED5C,SAAS,CAAC,MAAM;IACd,IAAIY,EAAE,IAAIA,EAAE,KAAK,KAAK,EAAE;MACtBG,OAAO,CAAC8B,QAAQ,CAACjC,EAAE,CAAC,CAAC;IACvB,CAAC,MAAM;MACLM,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC,EAAE,CAACN,EAAE,EAAEG,OAAO,EAAEG,YAAY,CAAC,CAAC;EAE/BlB,SAAS,CAAC,MAAM;IACd,IAAIc,SAAS,EAAE;MACbQ,UAAU,CAACR,SAAS,CAAC6B,KAAK,CAAC;MAC3BnB,eAAe,CAACV,SAAS,CAACgC,aAAa,CAAC;MACxCpB,SAAS,CAACZ,SAAS,CAACiC,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACjC,SAAS,CAAC,CAAC;EAEf,MAAMkC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,SAAS,GAAG;QAChBN,KAAK,EAAEtB,OAAO;QACdyB,aAAa,EAAEvB,YAAY;QAC3BwB,OAAO,EAAEtB;MACX,CAAC;MAED,IAAIb,EAAE,IAAIA,EAAE,KAAK,KAAK,EAAE;QACtB,MAAMK,QAAQ,CAAC4B,QAAQ,CAACjC,EAAE,CAAC,EAAEqC,SAAS,CAAC;MACzC,CAAC,MAAM;QACL,MAAMC,KAAK,GAAG,MAAMlC,QAAQ,CAACiC,SAAS,CAAC;QACvCpC,QAAQ,CAAC,eAAeqC,KAAK,CAACtC,EAAE,EAAE,EAAE;UAAEuC,OAAO,EAAE;QAAK,CAAC,CAAC;MACxD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C;EACF,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAInC,WAAW,GAAGuB,KAAK,CAACa,MAAM,GAAG,CAAC,EAAE;MAClCnC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMqC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIrC,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMsC,kBAAkB,GAAGA,CAACC,KAAyB,EAAEC,KAAa,KAAK;IACvEjC,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPjC,aAAa,EAAE;QACb,GAAGiC,IAAI,CAACjC,aAAa;QACrB,CAAC+B,KAAK,GAAGC;MACX;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,YAAuB,GAAG;MAC9BlD,EAAE,EAAEmD,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,EAAE;MACVC,cAAc,EAAE,EAAE;MAClBC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,KAAK;MACdC,WAAW,EAAE,EAAE;MACfC,GAAG,EAAE;IACP,CAAC;IACD/C,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPvB,SAAS,EAAE,CAAC,GAAGuB,IAAI,CAACvB,SAAS,EAAEyB,YAAY;IAC7C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMY,eAAe,GAAGA,CAACC,KAAa,EAAEjB,KAAsB,EAAEC,KAAuB,KAAK;IAC1FjC,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPvB,SAAS,EAAEuB,IAAI,CAACvB,SAAS,CAACuC,GAAG,CAAC,CAACC,GAAG,EAAEC,CAAC,KACnCA,CAAC,KAAKH,KAAK,GAAG;QAAE,GAAGE,GAAG;QAAE,CAACnB,KAAK,GAAGC;MAAM,CAAC,GAAGkB,GAC7C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,eAAe,GAAIJ,KAAa,IAAK;IACzCjD,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPvB,SAAS,EAAEuB,IAAI,CAACvB,SAAS,CAAC2C,MAAM,CAAC,CAACC,CAAC,EAAEH,CAAC,KAAKA,CAAC,KAAKH,KAAK;IACxD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMO,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,aAAyB,GAAG;MAChCvE,EAAE,EAAEmD,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBmB,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZhB,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,KAAK;MACdC,WAAW,EAAE,EAAE;MACfc,YAAY,EAAE;IAChB,CAAC;IACD5D,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPtB,UAAU,EAAE,CAAC,GAAGsB,IAAI,CAACtB,UAAU,EAAE6C,aAAa;IAChD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAACZ,KAAa,EAAEjB,KAAuB,EAAEC,KAAkC,KAAK;IACvGjC,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPtB,UAAU,EAAEsB,IAAI,CAACtB,UAAU,CAACsC,GAAG,CAAC,CAACY,GAAG,EAAEV,CAAC,KACrCA,CAAC,KAAKH,KAAK,GAAG;QAAE,GAAGa,GAAG;QAAE,CAAC9B,KAAK,GAAGC;MAAM,CAAC,GAAG6B,GAC7C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,gBAAgB,GAAId,KAAa,IAAK;IAC1CjD,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPtB,UAAU,EAAEsB,IAAI,CAACtB,UAAU,CAAC0C,MAAM,CAAC,CAACC,CAAC,EAAEH,CAAC,KAAKA,CAAC,KAAKH,KAAK;IAC1D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMe,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAMC,QAAe,GAAG;MACtB/E,EAAE,EAAEmD,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzB2B,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,cAAc;MACrBC,QAAQ,EAAE;IACZ,CAAC;IACDpE,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPrB,MAAM,EAAE,CAAC,GAAGqB,IAAI,CAACrB,MAAM,EAAEoD,QAAQ;IACnC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,WAAW,GAAGA,CAACpB,KAAa,EAAEjB,KAAkB,EAAEC,KAAa,KAAK;IACxEjC,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPrB,MAAM,EAAEqB,IAAI,CAACrB,MAAM,CAACqC,GAAG,CAAC,CAACoB,KAAK,EAAElB,CAAC,KAC/BA,CAAC,KAAKH,KAAK,GAAG;QAAE,GAAGqB,KAAK;QAAE,CAACtC,KAAK,GAAGC;MAAM,CAAC,GAAGqC,KAC/C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,WAAW,GAAItB,KAAa,IAAK;IACrCjD,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPrB,MAAM,EAAEqB,IAAI,CAACrB,MAAM,CAACyC,MAAM,CAAC,CAACC,CAAC,EAAEH,CAAC,KAAKA,CAAC,KAAKH,KAAK;IAClD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMuB,eAAe,GAAGA,CAAA,kBACtBzF,OAAA;IAAK0F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3F,OAAA;MAAI0F,SAAS,EAAC,mCAAmC;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxE/F,OAAA;MAAK0F,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD3F,OAAA,CAACJ,KAAK;QACJoG,KAAK,EAAC,UAAU;QAChBC,WAAW,EAAC,gCAAgC;QAC5C/C,KAAK,EAAEtC,OAAQ;QACfsF,QAAQ,EAAErF,UAAW;QACrBsF,QAAQ;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF/F,OAAA,CAACH,MAAM;QACLmG,KAAK,EAAC,UAAU;QAChB9C,KAAK,EAAEpC,YAAa;QACpBoF,QAAQ,EAAGhD,KAAK,IAAKnC,eAAe,CAACmC,KAAY,CAAE;QACnDkD,OAAO,EAAE,CACP;UAAElD,KAAK,EAAE,QAAQ;UAAE8C,KAAK,EAAE;QAAS,CAAC,EACpC;UAAE9C,KAAK,EAAE,SAAS;UAAE8C,KAAK,EAAE;QAAU,CAAC,EACtC;UAAE9C,KAAK,EAAE,UAAU;UAAE8C,KAAK,EAAE;QAAW,CAAC,EACxC;UAAE9C,KAAK,EAAE,SAAS;UAAE8C,KAAK,EAAE;QAAU,CAAC,CACtC;QACFG,QAAQ;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMM,kBAAkB,GAAGA,CAAA,kBACzBrG,OAAA;IAAK0F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3F,OAAA;MAAI0F,SAAS,EAAC,mCAAmC;MAAAC,QAAA,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3E/F,OAAA;MAAK0F,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD3F,OAAA,CAACJ,KAAK;QACJoG,KAAK,EAAC,YAAY;QAClBC,WAAW,EAAC,MAAM;QAClB/C,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACC,UAAW;QACvC+E,QAAQ,EAAGhD,KAAK,IAAKF,kBAAkB,CAAC,YAAY,EAAEE,KAAK,CAAE;QAC7DiD,QAAQ;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF/F,OAAA,CAACJ,KAAK;QACJoG,KAAK,EAAC,WAAW;QACjBC,WAAW,EAAC,KAAK;QACjB/C,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACE,SAAU;QACtC8E,QAAQ,EAAGhD,KAAK,IAAKF,kBAAkB,CAAC,WAAW,EAAEE,KAAK,CAAE;QAC5DiD,QAAQ;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF/F,OAAA,CAACJ,KAAK;QACJoG,KAAK,EAAC,OAAO;QACbM,IAAI,EAAC,OAAO;QACZL,WAAW,EAAC,sBAAsB;QAClC/C,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACG,KAAM;QAClC6E,QAAQ,EAAGhD,KAAK,IAAKF,kBAAkB,CAAC,OAAO,EAAEE,KAAK,CAAE;QACxDiD,QAAQ;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF/F,OAAA,CAACJ,KAAK;QACJoG,KAAK,EAAC,OAAO;QACbM,IAAI,EAAC,KAAK;QACVL,WAAW,EAAC,mBAAmB;QAC/B/C,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACI,KAAM;QAClC4E,QAAQ,EAAGhD,KAAK,IAAKF,kBAAkB,CAAC,OAAO,EAAEE,KAAK;MAAE;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACF/F,OAAA,CAACJ,KAAK;QACJoG,KAAK,EAAC,SAAS;QACfC,WAAW,EAAC,gCAAgC;QAC5C/C,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACK,OAAQ;QACpC2E,QAAQ,EAAGhD,KAAK,IAAKF,kBAAkB,CAAC,SAAS,EAAEE,KAAK,CAAE;QAC1DwC,SAAS,EAAC;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACF/F,OAAA,CAACJ,KAAK;QACJoG,KAAK,EAAC,SAAS;QACfM,IAAI,EAAC,KAAK;QACVL,WAAW,EAAC,qBAAqB;QACjC/C,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACM,OAAQ;QACpC0E,QAAQ,EAAGhD,KAAK,IAAKF,kBAAkB,CAAC,SAAS,EAAEE,KAAK;MAAE;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACF/F,OAAA,CAACJ,KAAK;QACJoG,KAAK,EAAC,UAAU;QAChBM,IAAI,EAAC,KAAK;QACVL,WAAW,EAAC,iCAAiC;QAC7C/C,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACO,QAAS;QACrCyE,QAAQ,EAAGhD,KAAK,IAAKF,kBAAkB,CAAC,UAAU,EAAEE,KAAK;MAAE;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACF/F,OAAA,CAACJ,KAAK;QACJoG,KAAK,EAAC,QAAQ;QACdM,IAAI,EAAC,KAAK;QACVL,WAAW,EAAC,4BAA4B;QACxC/C,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACQ,MAAO;QACnCwE,QAAQ,EAAGhD,KAAK,IAAKF,kBAAkB,CAAC,QAAQ,EAAEE,KAAK,CAAE;QACzDwC,SAAS,EAAC;MAAe;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACN/F,OAAA,CAACF,QAAQ;MACPkG,KAAK,EAAC,sBAAsB;MAC5BC,WAAW,EAAC,gEAAgE;MAC5E/C,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACS,OAAQ;MACpCuE,QAAQ,EAAGhD,KAAK,IAAKF,kBAAkB,CAAC,SAAS,EAAEE,KAAK,CAAE;MAC1DqD,IAAI,EAAE;IAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMS,eAAe,GAAGA,CAAA,kBACtBxG,OAAA;IAAK0F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3F,OAAA;MAAK0F,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD3F,OAAA;QAAI0F,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChE/F,OAAA,CAACL,MAAM;QAAC8G,OAAO,EAAErD,YAAa;QAACsD,IAAI,EAAC,IAAI;QAAAf,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC,EACL/E,MAAM,CAACY,SAAS,CAACuC,GAAG,CAAC,CAACC,GAAG,EAAEF,KAAK,kBAC/BlE,OAAA;MAAkB0F,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAC3E3F,OAAA;QAAK0F,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C3F,OAAA;UAAI0F,SAAS,EAAC,mCAAmC;UAAAC,QAAA,GAAC,aAAW,EAACzB,KAAK,GAAG,CAAC;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7E/F,OAAA,CAACL,MAAM;UACL8G,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACJ,KAAK,CAAE;UACtCyC,OAAO,EAAC,QAAQ;UAChBD,IAAI,EAAC,IAAI;UAAAf,QAAA,EACV;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN/F,OAAA;QAAK0F,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD3F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,aAAa;UACnBC,WAAW,EAAC,uBAAuB;UACnC/C,KAAK,EAAEkB,GAAG,CAACX,WAAY;UACvByC,QAAQ,EAAGhD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,aAAa,EAAEhB,KAAK,CAAE;UAClEiD,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,QAAQ;UACdC,WAAW,EAAC,qBAAqB;UACjC/C,KAAK,EAAEkB,GAAG,CAACV,MAAO;UAClBwC,QAAQ,EAAGhD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,QAAQ,EAAEhB,KAAK,CAAE;UAC7DiD,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,gBAAgB;UACtBC,WAAW,EAAC,kBAAkB;UAC9B/C,KAAK,EAAEkB,GAAG,CAACT,cAAe;UAC1BuC,QAAQ,EAAGhD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,gBAAgB,EAAEhB,KAAK,CAAE;UACrEiD,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,gBAAgB;UACtBC,WAAW,EAAC,KAAK;UACjB/C,KAAK,EAAEkB,GAAG,CAACJ,GAAG,IAAI,EAAG;UACrBkC,QAAQ,EAAGhD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,KAAK,EAAEhB,KAAK;QAAE;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACF/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,YAAY;UAClBM,IAAI,EAAC,MAAM;UACXpD,KAAK,EAAEkB,GAAG,CAACR,UAAW;UACtBsC,QAAQ,EAAGhD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,YAAY,EAAEhB,KAAK,CAAE;UACjEiD,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,UAAU;UAChBM,IAAI,EAAC,MAAM;UACXpD,KAAK,EAAEkB,GAAG,CAACP,QAAQ,IAAI,EAAG;UAC1BqC,QAAQ,EAAGhD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,UAAU,EAAEhB,KAAK,CAAE;UAC/D0D,QAAQ,EAAExC,GAAG,CAACN;QAAQ;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN/F,OAAA;QAAK0F,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC3F,OAAA;UACEsG,IAAI,EAAC,UAAU;UACfnG,EAAE,EAAE,qBAAqB+D,KAAK,EAAG;UACjC2C,OAAO,EAAEzC,GAAG,CAACN,OAAQ;UACrBoC,QAAQ,EAAGY,CAAC,IAAK7C,eAAe,CAACC,KAAK,EAAE,SAAS,EAAE4C,CAAC,CAACC,MAAM,CAACF,OAAO,CAAE;UACrEnB,SAAS,EAAC;QAAyE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eACF/F,OAAA;UAAOgH,OAAO,EAAE,qBAAqB9C,KAAK,EAAG;UAACwB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAErF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN/F,OAAA,CAACF,QAAQ;QACPkG,KAAK,EAAC,wBAAwB;QAC9BC,WAAW,EAAC,kDAAkD;QAC9D/C,KAAK,EAAEkB,GAAG,CAACL,WAAW,IAAI,EAAG;QAC7BmC,QAAQ,EAAGhD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,aAAa,EAAEhB,KAAK,CAAE;QAClEqD,IAAI,EAAE;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA,GAxEM3B,GAAG,CAACjE,EAAE;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAyEX,CACN,CAAC,EACD/E,MAAM,CAACY,SAAS,CAACkB,MAAM,KAAK,CAAC,iBAC5B9C,OAAA;MAAK0F,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7C3F,OAAA;QAAA2F,QAAA,EAAG;MAA+D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMkB,gBAAgB,GAAGA,CAAA,kBACvBjH,OAAA;IAAK0F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3F,OAAA;MAAK0F,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD3F,OAAA;QAAI0F,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtE/F,OAAA,CAACL,MAAM;QAAC8G,OAAO,EAAEhC,aAAc;QAACiC,IAAI,EAAC,IAAI;QAAAf,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,EACL/E,MAAM,CAACa,UAAU,CAACsC,GAAG,CAAC,CAACY,GAAG,EAAEb,KAAK,kBAChClE,OAAA;MAAkB0F,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAC3E3F,OAAA;QAAK0F,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C3F,OAAA;UAAI0F,SAAS,EAAC,mCAAmC;UAAAC,QAAA,GAAC,cAAY,EAACzB,KAAK,GAAG,CAAC;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9E/F,OAAA,CAACL,MAAM;UACL8G,OAAO,EAAEA,CAAA,KAAMzB,gBAAgB,CAACd,KAAK,CAAE;UACvCyC,OAAO,EAAC,QAAQ;UAChBD,IAAI,EAAC,IAAI;UAAAf,QAAA,EACV;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN/F,OAAA;QAAK0F,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD3F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,SAAS;UACfC,WAAW,EAAC,mBAAmB;UAC/B/C,KAAK,EAAE6B,GAAG,CAACJ,OAAQ;UACnBuB,QAAQ,EAAGhD,KAAK,IAAK4B,gBAAgB,CAACZ,KAAK,EAAE,SAAS,EAAEhB,KAAK,CAAE;UAC/DiD,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,UAAU;UAChBC,WAAW,EAAC,mBAAmB;UAC/B/C,KAAK,EAAE6B,GAAG,CAACH,QAAS;UACpBsB,QAAQ,EAAGhD,KAAK,IAAK4B,gBAAgB,CAACZ,KAAK,EAAE,UAAU,EAAEhB,KAAK,CAAE;UAChEiD,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,YAAY;UAClBM,IAAI,EAAC,MAAM;UACXpD,KAAK,EAAE6B,GAAG,CAACnB,UAAW;UACtBsC,QAAQ,EAAGhD,KAAK,IAAK4B,gBAAgB,CAACZ,KAAK,EAAE,YAAY,EAAEhB,KAAK,CAAE;UAClEiD,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,UAAU;UAChBM,IAAI,EAAC,MAAM;UACXpD,KAAK,EAAE6B,GAAG,CAAClB,QAAQ,IAAI,EAAG;UAC1BqC,QAAQ,EAAGhD,KAAK,IAAK4B,gBAAgB,CAACZ,KAAK,EAAE,UAAU,EAAEhB,KAAK,CAAE;UAChE0D,QAAQ,EAAE7B,GAAG,CAACjB;QAAQ;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN/F,OAAA;QAAK0F,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC3F,OAAA;UACEsG,IAAI,EAAC,UAAU;UACfnG,EAAE,EAAE,sBAAsB+D,KAAK,EAAG;UAClC2C,OAAO,EAAE9B,GAAG,CAACjB,OAAQ;UACrBoC,QAAQ,EAAGY,CAAC,IAAKhC,gBAAgB,CAACZ,KAAK,EAAE,SAAS,EAAE4C,CAAC,CAACC,MAAM,CAACF,OAAO,CAAE;UACtEnB,SAAS,EAAC;QAAyE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eACF/F,OAAA;UAAOgH,OAAO,EAAE,sBAAsB9C,KAAK,EAAG;UAACwB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAEtF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN/F,OAAA,CAACF,QAAQ;QACPkG,KAAK,EAAC,iBAAiB;QACvBC,WAAW,EAAC,oDAAoD;QAChE/C,KAAK,EAAE6B,GAAG,CAAChB,WAAW,IAAI,EAAG;QAC7BmC,QAAQ,EAAGhD,KAAK,IAAK4B,gBAAgB,CAACZ,KAAK,EAAE,aAAa,EAAEhB,KAAK,CAAE;QACnEqD,IAAI,EAAE;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA,GA3DMhB,GAAG,CAAC5E,EAAE;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA4DX,CACN,CAAC,EACD/E,MAAM,CAACa,UAAU,CAACiB,MAAM,KAAK,CAAC,iBAC7B9C,OAAA;MAAK0F,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7C3F,OAAA;QAAA2F,QAAA,EAAG;MAAsE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1E,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMmB,YAAY,GAAGA,CAAA,kBACnBlH,OAAA;IAAK0F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3F,OAAA;MAAK0F,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD3F,OAAA;QAAI0F,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7D/F,OAAA,CAACL,MAAM;QAAC8G,OAAO,EAAExB,QAAS;QAACyB,IAAI,EAAC,IAAI;QAAAf,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC,eACN/F,OAAA;MAAK0F,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EACnD3E,MAAM,CAACc,MAAM,CAACqC,GAAG,CAAC,CAACoB,KAAK,EAAErB,KAAK,kBAC9BlE,OAAA;QAAoB0F,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAC7E3F,OAAA;UAAK0F,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/C3F,OAAA;YAAI0F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,GAAC,SAAO,EAACzB,KAAK,GAAG,CAAC;UAAA;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzE/F,OAAA,CAACL,MAAM;YACL8G,OAAO,EAAEA,CAAA,KAAMjB,WAAW,CAACtB,KAAK,CAAE;YAClCyC,OAAO,EAAC,QAAQ;YAChBD,IAAI,EAAC,IAAI;YAAAf,QAAA,EACV;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,YAAY;UAClBC,WAAW,EAAC,YAAY;UACxB/C,KAAK,EAAEqC,KAAK,CAACJ,IAAK;UAClBe,QAAQ,EAAGhD,KAAK,IAAKoC,WAAW,CAACpB,KAAK,EAAE,MAAM,EAAEhB,KAAK,CAAE;UACvDiD,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAACH,MAAM;UACLmG,KAAK,EAAC,mBAAmB;UACzB9C,KAAK,EAAEqC,KAAK,CAACH,KAAM;UACnBc,QAAQ,EAAGhD,KAAK,IAAKoC,WAAW,CAACpB,KAAK,EAAE,OAAO,EAAEhB,KAAK,CAAE;UACxDkD,OAAO,EAAE,CACP;YAAElD,KAAK,EAAE,UAAU;YAAE8C,KAAK,EAAE;UAAW,CAAC,EACxC;YAAE9C,KAAK,EAAE,cAAc;YAAE8C,KAAK,EAAE;UAAe,CAAC,EAChD;YAAE9C,KAAK,EAAE,UAAU;YAAE8C,KAAK,EAAE;UAAW,CAAC,EACxC;YAAE9C,KAAK,EAAE,QAAQ;YAAE8C,KAAK,EAAE;UAAS,CAAC,CACpC;UACFG,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,qBAAqB;UAC3BC,WAAW,EAAC,uBAAuB;UACnC/C,KAAK,EAAEqC,KAAK,CAACF,QAAQ,IAAI,EAAG;UAC5Ba,QAAQ,EAAGhD,KAAK,IAAKoC,WAAW,CAACpB,KAAK,EAAE,UAAU,EAAEhB,KAAK;QAAE;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA,GAnCMR,KAAK,CAACpF,EAAE;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoCb,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EACL/E,MAAM,CAACc,MAAM,CAACgB,MAAM,KAAK,CAAC,iBACzB9C,OAAA;MAAK0F,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7C3F,OAAA;QAAA2F,QAAA,EAAG;MAAsD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMoB,cAAc,GAAGA,CAAA,kBACrBnH,OAAA;IAAK0F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3F,OAAA;MAAK0F,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD3F,OAAA;QAAI0F,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/D/F,OAAA,CAACL,MAAM;QAAC8G,OAAO,EAAEA,CAAA,KAAM;UACrB,MAAMW,UAAmB,GAAG;YAC1BjH,EAAE,EAAEmD,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;YACzB2B,IAAI,EAAE,EAAE;YACRpB,WAAW,EAAE,EAAE;YACfsD,YAAY,EAAE,EAAE;YAChBzD,UAAU,EAAE,EAAE;YACdC,QAAQ,EAAE,EAAE;YACZyD,GAAG,EAAE,EAAE;YACPC,UAAU,EAAE;UACd,CAAC;UACDtG,SAAS,CAACkC,IAAI,KAAK;YACjB,GAAGA,IAAI;YACPpB,QAAQ,EAAE,CAAC,GAAGoB,IAAI,CAACpB,QAAQ,EAAEqF,UAAU;UACzC,CAAC,CAAC,CAAC;QACL,CAAE;QAACV,IAAI,EAAC,IAAI;QAAAf,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,EACL/E,MAAM,CAACe,QAAQ,CAACoC,GAAG,CAAC,CAACqD,OAAO,EAAEtD,KAAK,kBAClClE,OAAA;MAAsB0F,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAC/E3F,OAAA;QAAK0F,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/C3F,OAAA;UAAI0F,SAAS,EAAC,mCAAmC;UAAAC,QAAA,GAAC,WAAS,EAACzB,KAAK,GAAG,CAAC;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3E/F,OAAA,CAACL,MAAM;UACL8G,OAAO,EAAEA,CAAA,KAAMxF,SAAS,CAACkC,IAAI,KAAK;YAChC,GAAGA,IAAI;YACPpB,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,CAACwC,MAAM,CAAC,CAACC,CAAC,EAAEH,CAAC,KAAKA,CAAC,KAAKH,KAAK;UACtD,CAAC,CAAC,CAAE;UACJyC,OAAO,EAAC,QAAQ;UAChBD,IAAI,EAAC,IAAI;UAAAf,QAAA,EACV;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN/F,OAAA;QAAK0F,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD3F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,cAAc;UACpBC,WAAW,EAAC,oBAAoB;UAChC/C,KAAK,EAAEsE,OAAO,CAACrC,IAAK;UACpBe,QAAQ,EAAGhD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;YACtC,GAAGA,IAAI;YACPpB,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,CAACoC,GAAG,CAAC,CAACsD,CAAC,EAAEpD,CAAC,KAC/BA,CAAC,KAAKH,KAAK,GAAG;cAAE,GAAGuD,CAAC;cAAEtC,IAAI,EAAEjC;YAAM,CAAC,GAAGuE,CACxC;UACF,CAAC,CAAC,CAAE;UACJtB,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,cAAc;UACpBC,WAAW,EAAC,yBAAyB;UACrC/C,KAAK,EAAEsE,OAAO,CAACH,YAAY,CAACK,IAAI,CAAC,IAAI,CAAE;UACvCxB,QAAQ,EAAGhD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;YACtC,GAAGA,IAAI;YACPpB,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,CAACoC,GAAG,CAAC,CAACsD,CAAC,EAAEpD,CAAC,KAC/BA,CAAC,KAAKH,KAAK,GAAG;cAAE,GAAGuD,CAAC;cAAEJ,YAAY,EAAEnE,KAAK,CAACyE,KAAK,CAAC,GAAG,CAAC,CAACxD,GAAG,CAACyD,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC;YAAE,CAAC,GAAGJ,CAC9E;UACF,CAAC,CAAC;QAAE;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACF/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,wBAAwB;UAC9BM,IAAI,EAAC,KAAK;UACVL,WAAW,EAAC,uBAAuB;UACnC/C,KAAK,EAAEsE,OAAO,CAACF,GAAG,IAAI,EAAG;UACzBpB,QAAQ,EAAGhD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;YACtC,GAAGA,IAAI;YACPpB,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,CAACoC,GAAG,CAAC,CAACsD,CAAC,EAAEpD,CAAC,KAC/BA,CAAC,KAAKH,KAAK,GAAG;cAAE,GAAGuD,CAAC;cAAEH,GAAG,EAAEpE;YAAM,CAAC,GAAGuE,CACvC;UACF,CAAC,CAAC;QAAE;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACF/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,uBAAuB;UAC7BM,IAAI,EAAC,KAAK;UACVL,WAAW,EAAC,iCAAiC;UAC7C/C,KAAK,EAAEsE,OAAO,CAACD,UAAU,IAAI,EAAG;UAChCrB,QAAQ,EAAGhD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;YACtC,GAAGA,IAAI;YACPpB,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,CAACoC,GAAG,CAAC,CAACsD,CAAC,EAAEpD,CAAC,KAC/BA,CAAC,KAAKH,KAAK,GAAG;cAAE,GAAGuD,CAAC;cAAEF,UAAU,EAAErE;YAAM,CAAC,GAAGuE,CAC9C;UACF,CAAC,CAAC;QAAE;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN/F,OAAA,CAACF,QAAQ;QACPkG,KAAK,EAAC,qBAAqB;QAC3BC,WAAW,EAAC,iDAAiD;QAC7D/C,KAAK,EAAEsE,OAAO,CAACzD,WAAY;QAC3BmC,QAAQ,EAAGhD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;UACtC,GAAGA,IAAI;UACPpB,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,CAACoC,GAAG,CAAC,CAACsD,CAAC,EAAEpD,CAAC,KAC/BA,CAAC,KAAKH,KAAK,GAAG;YAAE,GAAGuD,CAAC;YAAE1D,WAAW,EAAEb;UAAM,CAAC,GAAGuE,CAC/C;QACF,CAAC,CAAC,CAAE;QACJlB,IAAI,EAAE;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA,GA1EMyB,OAAO,CAACrH,EAAE;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA2Ef,CACN,CAAC,EACD/E,MAAM,CAACe,QAAQ,CAACe,MAAM,KAAK,CAAC,iBAC3B9C,OAAA;MAAK0F,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7C3F,OAAA;QAAA2F,QAAA,EAAG;MAA0D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAM+B,eAAe,GAAGA,CAAA,kBACtB9H,OAAA;IAAK0F,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB3F,OAAA;MAAK0F,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD3F,OAAA;QAAI0F,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChE/F,OAAA,CAACL,MAAM;QAAC8G,OAAO,EAAEA,CAAA,KAAM;UACrB,MAAMsB,WAAqB,GAAG;YAC5B5H,EAAE,EAAEmD,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;YACzB2B,IAAI,EAAE,EAAE;YACR6C,WAAW,EAAE;UACf,CAAC;UACD/G,SAAS,CAACkC,IAAI,KAAK;YACjB,GAAGA,IAAI;YACPnB,SAAS,EAAE,CAAC,GAAGmB,IAAI,CAACnB,SAAS,EAAE+F,WAAW;UAC5C,CAAC,CAAC,CAAC;QACL,CAAE;QAACrB,IAAI,EAAC,IAAI;QAAAf,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eACN/F,OAAA;MAAK0F,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EACnD3E,MAAM,CAACgB,SAAS,CAACmC,GAAG,CAAC,CAAC8D,QAAQ,EAAE/D,KAAK,kBACpClE,OAAA;QAAuB0F,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAChF3F,OAAA;UAAK0F,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/C3F,OAAA;YAAI0F,SAAS,EAAC,mCAAmC;YAAAC,QAAA,GAAC,YAAU,EAACzB,KAAK,GAAG,CAAC;UAAA;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5E/F,OAAA,CAACL,MAAM;YACL8G,OAAO,EAAEA,CAAA,KAAMxF,SAAS,CAACkC,IAAI,KAAK;cAChC,GAAGA,IAAI;cACPnB,SAAS,EAAEmB,IAAI,CAACnB,SAAS,CAACuC,MAAM,CAAC,CAACC,CAAC,EAAEH,CAAC,KAAKA,CAAC,KAAKH,KAAK;YACxD,CAAC,CAAC,CAAE;YACJyC,OAAO,EAAC,QAAQ;YAChBD,IAAI,EAAC,IAAI;YAAAf,QAAA,EACV;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN/F,OAAA,CAACJ,KAAK;UACJoG,KAAK,EAAC,UAAU;UAChBC,WAAW,EAAC,SAAS;UACrB/C,KAAK,EAAE+E,QAAQ,CAAC9C,IAAK;UACrBe,QAAQ,EAAGhD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;YACtC,GAAGA,IAAI;YACPnB,SAAS,EAAEmB,IAAI,CAACnB,SAAS,CAACmC,GAAG,CAAC,CAAC+D,CAAC,EAAE7D,CAAC,KACjCA,CAAC,KAAKH,KAAK,GAAG;cAAE,GAAGgE,CAAC;cAAE/C,IAAI,EAAEjC;YAAM,CAAC,GAAGgF,CACxC;UACF,CAAC,CAAC,CAAE;UACJ/B,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAACH,MAAM;UACLmG,KAAK,EAAC,mBAAmB;UACzB9C,KAAK,EAAE+E,QAAQ,CAACD,WAAY;UAC5B9B,QAAQ,EAAGhD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;YACtC,GAAGA,IAAI;YACPnB,SAAS,EAAEmB,IAAI,CAACnB,SAAS,CAACmC,GAAG,CAAC,CAAC+D,CAAC,EAAE7D,CAAC,KACjCA,CAAC,KAAKH,KAAK,GAAG;cAAE,GAAGgE,CAAC;cAAEF,WAAW,EAAE9E;YAAa,CAAC,GAAGgF,CACtD;UACF,CAAC,CAAC,CAAE;UACJ9B,OAAO,EAAE,CACP;YAAElD,KAAK,EAAE,OAAO;YAAE8C,KAAK,EAAE;UAAQ,CAAC,EAClC;YAAE9C,KAAK,EAAE,gBAAgB;YAAE8C,KAAK,EAAE;UAAiB,CAAC,EACpD;YAAE9C,KAAK,EAAE,QAAQ;YAAE8C,KAAK,EAAE;UAAS,CAAC,EACpC;YAAE9C,KAAK,EAAE,QAAQ;YAAE8C,KAAK,EAAE;UAAS,CAAC,CACpC;UACFG,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA,GA1CMkC,QAAQ,CAAC9H,EAAE;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2ChB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EACL/E,MAAM,CAACgB,SAAS,CAACc,MAAM,KAAK,CAAC,iBAC5B9C,OAAA;MAAK0F,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7C3F,OAAA;QAAA2F,QAAA,EAAG;MAA4D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMoC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQlG,KAAK,CAACvB,WAAW,CAAC,CAACyB,SAAS;MAClC,KAAK,OAAO;QACV,OAAOsD,eAAe,CAAC,CAAC;MAC1B,KAAK,UAAU;QACb,OAAOY,kBAAkB,CAAC,CAAC;MAC7B,KAAK,WAAW;QACd,OAAOG,eAAe,CAAC,CAAC;MAC1B,KAAK,YAAY;QACf,OAAOS,gBAAgB,CAAC,CAAC;MAC3B,KAAK,QAAQ;QACX,OAAOC,YAAY,CAAC,CAAC;MACvB,KAAK,UAAU;QACb,OAAOC,cAAc,CAAC,CAAC;MACzB,KAAK,WAAW;QACd,OAAOW,eAAe,CAAC,CAAC;MAC1B;QACE,OAAOrC,eAAe,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,oBACEzF,OAAA;IAAK0F,SAAS,EAAC,6CAA6C;IAAAC,QAAA,gBAE1D3F,OAAA;MAAK0F,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB3F,OAAA;QAAI0F,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAC7CxF,EAAE,IAAIA,EAAE,KAAK,KAAK,GAAG,SAAS,GAAG;MAAe;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACL/F,OAAA;QAAG0F,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN/F,OAAA;MAAK0F,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnB3F,OAAA;QAAK,cAAW,UAAU;QAAA2F,QAAA,eACxB3F,OAAA;UAAI0F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC9B1D,KAAK,CAACkC,GAAG,CAAC,CAACiE,IAAI,EAAElE,KAAK,kBACrBlE,OAAA;YAAqB0F,SAAS,EAAE,YAAYxB,KAAK,KAAKjC,KAAK,CAACa,MAAM,GAAG,CAAC,GAAG,eAAe,GAAG,EAAE,EAAG;YAAA6C,QAAA,gBAC9F3F,OAAA;cAAK0F,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC3F,OAAA;gBACE0F,SAAS,EAAE,kEACTxB,KAAK,GAAGxD,WAAW,GACf,gBAAgB,GAChBwD,KAAK,KAAKxD,WAAW,GACrB,sCAAsC,GACtC,mCAAmC,EACtC;gBAAAiF,QAAA,EAEFzB,KAAK,GAAGxD,WAAW,gBAClBV,OAAA;kBAAK0F,SAAS,EAAC,oBAAoB;kBAAC2C,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAA3C,QAAA,eACzE3F,OAAA;oBAAMuI,QAAQ,EAAC,SAAS;oBAACC,CAAC,EAAC,oHAAoH;oBAACC,QAAQ,EAAC;kBAAS;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClK,CAAC,gBAEN/F,OAAA;kBAAM0F,SAAS,EAAE,uBAAuBxB,KAAK,KAAKxD,WAAW,GAAG,kBAAkB,GAAG,eAAe,EAAG;kBAAAiF,QAAA,EACpGzB,KAAK,GAAG;gBAAC;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cACP;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN/F,OAAA;gBAAM0F,SAAS,EAAE,4BAA4BxB,KAAK,KAAKxD,WAAW,GAAG,kBAAkB,GAAG,eAAe,EAAG;gBAAAiF,QAAA,EACzGyC,IAAI,CAAClG;cAAK;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACL7B,KAAK,KAAKjC,KAAK,CAACa,MAAM,GAAG,CAAC,iBACzB9C,OAAA;cAAK0F,SAAS,EAAC,8DAA8D;cAAC,eAAY;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACnG;UAAA,GA3BMqC,IAAI,CAAClG,KAAK;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4Bf,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/F,OAAA;MAAK0F,SAAS,EAAC,wCAAwC;MAAAC,QAAA,EACpDwC,iBAAiB,CAAC;IAAC;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGN/F,OAAA;MAAK0F,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnC3F,OAAA;QAAA2F,QAAA,EACGjF,WAAW,GAAG,CAAC,iBACdV,OAAA,CAACL,MAAM;UACL8G,OAAO,EAAE1D,cAAe;UACxB4D,OAAO,EAAC,SAAS;UAAAhB,QAAA,EAClB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACN/F,OAAA;QAAK0F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B3F,OAAA,CAACL,MAAM;UACL8G,OAAO,EAAElE,UAAW;UACpBoE,OAAO,EAAC,SAAS;UAAAhB,QAAA,EAClB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRrF,WAAW,GAAGuB,KAAK,CAACa,MAAM,GAAG,CAAC,gBAC7B9C,OAAA,CAACL,MAAM;UAAC8G,OAAO,EAAE5D,UAAW;UAAA8C,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAET/F,OAAA,CAACL,MAAM;UAAC8G,OAAO,EAAEA,CAAA,KAAMrG,QAAQ,CAAC,eAAe,CAAAC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEF,EAAE,KAAI,KAAK,EAAE,CAAE;UAAAwF,QAAA,EAAC;QAE1E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7F,EAAA,CAhyBID,SAAmB;EAAA,QACRT,SAAS,EACPC,WAAW,EACqCC,KAAK;AAAA;AAAAgJ,EAAA,GAHlEzI,SAAmB;AAkyBzB,eAAeA,SAAS;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}