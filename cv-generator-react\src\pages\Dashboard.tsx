import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useCV } from '../contexts/CVContext';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/ui/Button';
import { CV } from '../types';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { cvs, fetchCVs, deleteCV, duplicateCV, isLoading } = useCV();
  const [deletingId, setDeletingId] = useState<number | null>(null);

  useEffect(() => {
    fetchCVs();
  }, [fetchCVs]);

  const handleDeleteCV = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this CV?')) {
      try {
        setDeletingId(id);
        await deleteCV(id);
      } catch (error) {
        console.error('Failed to delete CV:', error);
      } finally {
        setDeletingId(null);
      }
    }
  };

  const handleDuplicateCV = async (id: number) => {
    try {
      const newCV = await duplicateCV(id);
      navigate(`/cv-builder/${newCV.id}`);
    } catch (error) {
      console.error('Failed to duplicate CV:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (isLoading && cvs.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.name}!
        </h1>
        <p className="mt-2 text-gray-600">
          Manage your CVs and create new ones
        </p>
      </div>

      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Your CVs</h2>
        <Link to="/cv-builder">
          <Button>Create New CV</Button>
        </Link>
      </div>

      {cvs.length === 0 ? (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No CVs yet</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first CV
          </p>
          <div className="mt-6">
            <Link to="/cv-builder">
              <Button>Create Your First CV</Button>
            </Link>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {cvs.map((cv) => (
            <div
              key={cv.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {cv.title}
                  </h3>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                      {cv.template_type}
                    </span>
                    {cv.is_public && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Public
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-500">
                    Updated {formatDate(cv.updated_at)}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <button
                    onClick={() => handleDeleteCV(cv.id)}
                    disabled={deletingId === cv.id}
                    className="text-gray-400 hover:text-red-500 transition-colors"
                  >
                    {deletingId === cv.id ? (
                      <div className="animate-spin h-4 w-4 border-2 border-red-500 border-t-transparent rounded-full"></div>
                    ) : (
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>
              
              <div className="flex space-x-2">
                <Link to={`/cv-builder/${cv.id}`} className="flex-1">
                  <Button size="sm" variant="outline" className="w-full">
                    Edit
                  </Button>
                </Link>
                <Link to={`/cv-preview/${cv.id}`} className="flex-1">
                  <Button size="sm" variant="outline" className="w-full">
                    Preview
                  </Button>
                </Link>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleDuplicateCV(cv.id)}
                  className="px-2"
                >
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Dashboard;
