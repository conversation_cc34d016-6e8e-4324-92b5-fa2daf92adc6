import { CVData, PersonalInfo, Experience, Education } from '../types';

// Date formatting utilities
export const formatDate = (dateString: string, format: 'short' | 'long' = 'short'): string => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  
  if (format === 'long') {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }
  
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
  });
};

// Format date range for experience/education
export const formatDateRange = (startDate: string, endDate?: string, current?: boolean): string => {
  const start = formatDate(startDate);
  
  if (current) {
    return `${start} - Present`;
  }
  
  if (endDate) {
    return `${start} - ${formatDate(endDate)}`;
  }
  
  return start;
};

// Generate unique ID
export const generateId = (): string => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
};

// Validate email
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate URL
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Truncate text
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
};

// Capitalize first letter
export const capitalize = (text: string): string => {
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};

// Format file size
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Deep clone object
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

// Check if object is empty
export const isEmpty = (obj: any): boolean => {
  if (obj === null || obj === undefined) return true;
  if (typeof obj === 'string') return obj.trim().length === 0;
  if (Array.isArray(obj)) return obj.length === 0;
  if (typeof obj === 'object') return Object.keys(obj).length === 0;
  return false;
};

// Debounce function
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// CV validation utilities
export const validatePersonalInfo = (personalInfo: PersonalInfo): string[] => {
  const errors: string[] = [];
  
  if (!personalInfo.firstName?.trim()) {
    errors.push('First name is required');
  }
  
  if (!personalInfo.lastName?.trim()) {
    errors.push('Last name is required');
  }
  
  if (!personalInfo.email?.trim()) {
    errors.push('Email is required');
  } else if (!isValidEmail(personalInfo.email)) {
    errors.push('Please enter a valid email address');
  }
  
  if (personalInfo.website && !isValidUrl(personalInfo.website)) {
    errors.push('Please enter a valid website URL');
  }
  
  if (personalInfo.linkedin && !isValidUrl(personalInfo.linkedin)) {
    errors.push('Please enter a valid LinkedIn URL');
  }
  
  if (personalInfo.github && !isValidUrl(personalInfo.github)) {
    errors.push('Please enter a valid GitHub URL');
  }
  
  return errors;
};

export const validateExperience = (experience: Experience): string[] => {
  const errors: string[] = [];
  
  if (!experience.company?.trim()) {
    errors.push('Company name is required');
  }
  
  if (!experience.position?.trim()) {
    errors.push('Position is required');
  }
  
  if (!experience.startDate?.trim()) {
    errors.push('Start date is required');
  }
  
  if (!experience.current && !experience.endDate?.trim()) {
    errors.push('End date is required when not current position');
  }
  
  return errors;
};

export const validateEducation = (education: Education): string[] => {
  const errors: string[] = [];
  
  if (!education.institution?.trim()) {
    errors.push('Institution name is required');
  }
  
  if (!education.degree?.trim()) {
    errors.push('Degree is required');
  }
  
  if (!education.startDate?.trim()) {
    errors.push('Start date is required');
  }
  
  return errors;
};

// CV completeness checker
export const calculateCVCompleteness = (cvData: CVData): number => {
  let totalFields = 0;
  let completedFields = 0;
  
  // Personal info (required fields)
  const requiredPersonalFields = ['firstName', 'lastName', 'email'];
  const optionalPersonalFields = ['phone', 'address', 'website', 'linkedin', 'github', 'summary'];
  
  requiredPersonalFields.forEach(field => {
    totalFields++;
    if (cvData.personalInfo[field as keyof PersonalInfo]) {
      completedFields++;
    }
  });
  
  optionalPersonalFields.forEach(field => {
    totalFields++;
    if (cvData.personalInfo[field as keyof PersonalInfo]) {
      completedFields++;
    }
  });
  
  // Experience
  totalFields++;
  if (cvData.experience.length > 0) {
    completedFields++;
  }
  
  // Education
  totalFields++;
  if (cvData.education.length > 0) {
    completedFields++;
  }
  
  // Skills
  totalFields++;
  if (cvData.skills.length > 0) {
    completedFields++;
  }
  
  return Math.round((completedFields / totalFields) * 100);
};

// Local storage utilities
export const saveToLocalStorage = (key: string, data: any): void => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

export const getFromLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error('Error reading from localStorage:', error);
    return defaultValue;
  }
};

export const removeFromLocalStorage = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error('Error removing from localStorage:', error);
  }
};

// Export utilities
export const downloadFile = (blob: Blob, filename: string): void => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};
