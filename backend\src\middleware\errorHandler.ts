import { Request, Response, NextFunction } from 'express';
import { ValidationError } from 'sequelize';
import { AppError, ErrorType, HttpStatus } from '../types';
import { sendError, sendValidationError } from '../utils/response';

/**
 * Global error handling middleware
 */
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  console.error('Error:', error);

  // Handle custom AppError
  if (error instanceof AppError) {
    sendError(res, error.message, error.statusCode, error.type);
    return;
  }

  // Handle Sequelize validation errors
  if (error instanceof ValidationError) {
    const validationErrors: Record<string, string[]> = {};
    
    error.errors.forEach((err) => {
      const field = err.path || 'unknown';
      if (!validationErrors[field]) {
        validationErrors[field] = [];
      }
      validationErrors[field].push(err.message);
    });

    sendValidationError(res, validationErrors);
    return;
  }

  // Handle JWT errors
  if (error.name === 'JsonWebTokenError') {
    sendError(res, 'Invalid token', HttpStatus.UNAUTHORIZED, ErrorType.AUTHENTICATION_ERROR);
    return;
  }

  if (error.name === 'TokenExpiredError') {
    sendError(res, 'Token expired', HttpStatus.UNAUTHORIZED, ErrorType.AUTHENTICATION_ERROR);
    return;
  }

  // Handle Sequelize database errors
  if (error.name === 'SequelizeUniqueConstraintError') {
    sendError(res, 'Resource already exists', HttpStatus.CONFLICT, ErrorType.CONFLICT_ERROR);
    return;
  }

  if (error.name === 'SequelizeForeignKeyConstraintError') {
    sendError(res, 'Invalid reference', HttpStatus.BAD_REQUEST, ErrorType.VALIDATION_ERROR);
    return;
  }

  if (error.name === 'SequelizeConnectionError') {
    sendError(res, 'Database connection error', HttpStatus.INTERNAL_SERVER_ERROR, ErrorType.INTERNAL_ERROR);
    return;
  }

  // Handle multer file upload errors
  if (error.name === 'MulterError') {
    let message = 'File upload error';
    
    switch ((error as any).code) {
      case 'LIMIT_FILE_SIZE':
        message = 'File too large';
        break;
      case 'LIMIT_FILE_COUNT':
        message = 'Too many files';
        break;
      case 'LIMIT_UNEXPECTED_FILE':
        message = 'Unexpected file field';
        break;
    }

    sendError(res, message, HttpStatus.BAD_REQUEST, ErrorType.VALIDATION_ERROR);
    return;
  }

  // Handle syntax errors (malformed JSON, etc.)
  if (error instanceof SyntaxError && 'body' in error) {
    sendError(res, 'Invalid JSON format', HttpStatus.BAD_REQUEST, ErrorType.VALIDATION_ERROR);
    return;
  }

  // Default error response
  const isDevelopment = process.env.NODE_ENV === 'development';
  const message = isDevelopment ? error.message : 'Internal server error';
  const errorDetails = isDevelopment ? error.stack : undefined;

  sendError(
    res,
    message,
    HttpStatus.INTERNAL_SERVER_ERROR,
    errorDetails,
    undefined
  );
};

/**
 * Async error wrapper
 * Wraps async route handlers to catch errors and pass them to error middleware
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 404 Not Found middleware
 */
export const notFound = (req: Request, res: Response, next: NextFunction): void => {
  const error = new AppError(
    `Route ${req.originalUrl} not found`,
    HttpStatus.NOT_FOUND,
    ErrorType.NOT_FOUND_ERROR
  );
  next(error);
};

export default {
  errorHandler,
  asyncHandler,
  notFound,
};
