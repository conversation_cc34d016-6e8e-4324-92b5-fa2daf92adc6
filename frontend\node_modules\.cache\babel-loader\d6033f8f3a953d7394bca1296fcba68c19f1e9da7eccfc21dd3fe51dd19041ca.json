{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\frontend\\\\src\\\\components\\\\ui\\\\Input.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Input = ({\n  label,\n  placeholder,\n  value,\n  onChange,\n  error,\n  disabled = false,\n  required = false,\n  type = 'text',\n  className = ''\n}) => {\n  const getInputClasses = () => {\n    let classes = 'input';\n    if (error) {\n      classes += ' error';\n    }\n    return `${classes} ${className}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginBottom: '1rem'\n    },\n    children: [label && /*#__PURE__*/_jsxDEV(\"label\", {\n      style: {\n        display: 'block',\n        fontSize: '0.875rem',\n        fontWeight: '500',\n        color: '#374151',\n        marginBottom: '0.25rem'\n      },\n      children: [label, required && /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: '#ef4444',\n          marginLeft: '0.25rem'\n        },\n        children: \"*\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 24\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: type,\n      className: getInputClasses(),\n      placeholder: placeholder,\n      value: value,\n      onChange: e => onChange(e.target.value),\n      disabled: disabled,\n      required: required\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n      style: {\n        fontSize: '0.875rem',\n        color: '#dc2626',\n        marginTop: '0.25rem'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_c = Input;\nexport default Input;\nvar _c;\n$RefreshReg$(_c, \"Input\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Input", "label", "placeholder", "value", "onChange", "error", "disabled", "required", "type", "className", "getInputClasses", "classes", "style", "marginBottom", "children", "display", "fontSize", "fontWeight", "color", "marginLeft", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "e", "target", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/frontend/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react';\nimport { InputProps } from '../../types';\n\nconst Input: React.FC<InputProps> = ({\n  label,\n  placeholder,\n  value,\n  onChange,\n  error,\n  disabled = false,\n  required = false,\n  type = 'text',\n  className = '',\n}) => {\n  const getInputClasses = () => {\n    let classes = 'input';\n\n    if (error) {\n      classes += ' error';\n    }\n\n    return `${classes} ${className}`;\n  };\n\n  return (\n    <div style={{ marginBottom: '1rem' }}>\n      {label && (\n        <label\n          style={{\n            display: 'block',\n            fontSize: '0.875rem',\n            fontWeight: '500',\n            color: '#374151',\n            marginBottom: '0.25rem'\n          }}\n        >\n          {label}\n          {required && <span style={{ color: '#ef4444', marginLeft: '0.25rem' }}>*</span>}\n        </label>\n      )}\n      <input\n        type={type}\n        className={getInputClasses()}\n        placeholder={placeholder}\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        disabled={disabled}\n        required={required}\n      />\n      {error && (\n        <p style={{ fontSize: '0.875rem', color: '#dc2626', marginTop: '0.25rem' }}>\n          {error}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default Input;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1B,MAAMC,KAA2B,GAAGA,CAAC;EACnCC,KAAK;EACLC,WAAW;EACXC,KAAK;EACLC,QAAQ;EACRC,KAAK;EACLC,QAAQ,GAAG,KAAK;EAChBC,QAAQ,GAAG,KAAK;EAChBC,IAAI,GAAG,MAAM;EACbC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIC,OAAO,GAAG,OAAO;IAErB,IAAIN,KAAK,EAAE;MACTM,OAAO,IAAI,QAAQ;IACrB;IAEA,OAAO,GAAGA,OAAO,IAAIF,SAAS,EAAE;EAClC,CAAC;EAED,oBACEV,OAAA;IAAKa,KAAK,EAAE;MAAEC,YAAY,EAAE;IAAO,CAAE;IAAAC,QAAA,GAClCb,KAAK,iBACJF,OAAA;MACEa,KAAK,EAAE;QACLG,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE,KAAK;QACjBC,KAAK,EAAE,SAAS;QAChBL,YAAY,EAAE;MAChB,CAAE;MAAAC,QAAA,GAEDb,KAAK,EACLM,QAAQ,iBAAIR,OAAA;QAAMa,KAAK,EAAE;UAAEM,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE;QAAU,CAAE;QAAAL,QAAA,EAAC;MAAC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1E,CACR,eACDxB,OAAA;MACES,IAAI,EAAEA,IAAK;MACXC,SAAS,EAAEC,eAAe,CAAC,CAAE;MAC7BR,WAAW,EAAEA,WAAY;MACzBC,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAGoB,CAAC,IAAKpB,QAAQ,CAACoB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;MAC1CG,QAAQ,EAAEA,QAAS;MACnBC,QAAQ,EAAEA;IAAS;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,EACDlB,KAAK,iBACJN,OAAA;MAAGa,KAAK,EAAE;QAAEI,QAAQ,EAAE,UAAU;QAAEE,KAAK,EAAE,SAAS;QAAEQ,SAAS,EAAE;MAAU,CAAE;MAAAZ,QAAA,EACxET;IAAK;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACI,EAAA,GArDI3B,KAA2B;AAuDjC,eAAeA,KAAK;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}