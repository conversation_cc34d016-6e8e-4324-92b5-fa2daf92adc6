[{"C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\Layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\contexts\\AuthContext.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ProtectedRoute.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\contexts\\CVContext.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\Register.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\Dashboard.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\Login.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\CVPreview.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\services\\api.ts": "12", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Input.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Button.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Select.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Textarea.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\templates\\ModernTemplate.tsx": "17", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\templates\\ClassicTemplate.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\SimpleCVBuilder.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\SimpleCVPreview.tsx": "20"}, {"size": 554, "mtime": 1751285378021, "results": "21", "hashOfConfig": "22"}, {"size": 425, "mtime": 1751285377590, "results": "23", "hashOfConfig": "22"}, {"size": 1537, "mtime": 1751308957654, "results": "24", "hashOfConfig": "22"}, {"size": 8686, "mtime": 1751308482490, "results": "25", "hashOfConfig": "22"}, {"size": 6446, "mtime": 1751285605639, "results": "26", "hashOfConfig": "22"}, {"size": 797, "mtime": 1751294371426, "results": "27", "hashOfConfig": "22"}, {"size": 6550, "mtime": 1751285642789, "results": "28", "hashOfConfig": "22"}, {"size": 7272, "mtime": 1751308253688, "results": "29", "hashOfConfig": "22"}, {"size": 8330, "mtime": 1751309275208, "results": "30", "hashOfConfig": "22"}, {"size": 5344, "mtime": 1751296969702, "results": "31", "hashOfConfig": "22"}, {"size": 6932, "mtime": 1751294798646, "results": "32", "hashOfConfig": "22"}, {"size": 4436, "mtime": 1751309923332, "results": "33", "hashOfConfig": "22"}, {"size": 1970, "mtime": 1751308053587, "results": "34", "hashOfConfig": "22"}, {"size": 2606, "mtime": 1751308743366, "results": "35", "hashOfConfig": "22"}, {"size": 2082, "mtime": 1751308088201, "results": "36", "hashOfConfig": "22"}, {"size": 1971, "mtime": 1751308142875, "results": "37", "hashOfConfig": "22"}, {"size": 10325, "mtime": 1751294242477, "results": "38", "hashOfConfig": "22"}, {"size": 9747, "mtime": 1751294298256, "results": "39", "hashOfConfig": "22"}, {"size": 9334, "mtime": 1751309256028, "results": "40", "hashOfConfig": "22"}, {"size": 9910, "mtime": 1751309182112, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "60olbe", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\contexts\\CVContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\Register.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\CVPreview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\templates\\ModernTemplate.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\templates\\ClassicTemplate.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\SimpleCVBuilder.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\SimpleCVPreview.tsx", [], []]