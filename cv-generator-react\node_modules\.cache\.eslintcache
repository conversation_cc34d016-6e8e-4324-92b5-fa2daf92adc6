[{"C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\Layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\contexts\\AuthContext.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ProtectedRoute.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\contexts\\CVContext.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\Register.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\Dashboard.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\Login.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\CVBuilder.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\CVPreview.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\services\\api.ts": "13", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Input.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Button.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Select.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Textarea.tsx": "17", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\templates\\ModernTemplate.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\templates\\ClassicTemplate.tsx": "19"}, {"size": 554, "mtime": 1751285378021, "results": "20", "hashOfConfig": "21"}, {"size": 425, "mtime": 1751285377590, "results": "22", "hashOfConfig": "21"}, {"size": 1501, "mtime": 1751294355929, "results": "23", "hashOfConfig": "21"}, {"size": 6363, "mtime": 1751294403885, "results": "24", "hashOfConfig": "21"}, {"size": 6446, "mtime": 1751285605639, "results": "25", "hashOfConfig": "21"}, {"size": 797, "mtime": 1751294371426, "results": "26", "hashOfConfig": "21"}, {"size": 6550, "mtime": 1751285642789, "results": "27", "hashOfConfig": "21"}, {"size": 7264, "mtime": 1751293918201, "results": "28", "hashOfConfig": "21"}, {"size": 6439, "mtime": 1751293961175, "results": "29", "hashOfConfig": "21"}, {"size": 5344, "mtime": 1751296969702, "results": "30", "hashOfConfig": "21"}, {"size": 26965, "mtime": 1751294779836, "results": "31", "hashOfConfig": "21"}, {"size": 6932, "mtime": 1751294798646, "results": "32", "hashOfConfig": "21"}, {"size": 4457, "mtime": 1751285556690, "results": "33", "hashOfConfig": "21"}, {"size": 1245, "mtime": 1751293833658, "results": "34", "hashOfConfig": "21"}, {"size": 1986, "mtime": 1751285659262, "results": "35", "hashOfConfig": "21"}, {"size": 1375, "mtime": 1751293848627, "results": "36", "hashOfConfig": "21"}, {"size": 1261, "mtime": 1751293863308, "results": "37", "hashOfConfig": "21"}, {"size": 10325, "mtime": 1751294242477, "results": "38", "hashOfConfig": "21"}, {"size": 9747, "mtime": 1751294298256, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "60olbe", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\contexts\\CVContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\Register.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\Dashboard.tsx", ["97"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\CVBuilder.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\pages\\CVPreview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\services\\api.ts", ["98"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\ui\\Textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\templates\\ModernTemplate.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-react\\src\\components\\templates\\ClassicTemplate.tsx", [], [], {"ruleId": "99", "severity": 1, "message": "100", "line": 6, "column": 10, "nodeType": "101", "messageId": "102", "endLine": 6, "endColumn": 12}, {"ruleId": "99", "severity": 1, "message": "103", "line": 8, "column": 3, "nodeType": "101", "messageId": "102", "endLine": 8, "endColumn": 13}, "@typescript-eslint/no-unused-vars", "'CV' is defined but never used.", "Identifier", "unusedVar", "'CVResponse' is defined but never used."]