import { sequelize } from '../config/database';
import { User } from './User';
import { CV } from './CV';

// Export all models
export { User, CV };

// Export sequelize instance
export { sequelize };

// Initialize all models and their associations
const initializeModels = async (): Promise<void> => {
  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Sync all models
    await sequelize.sync({ alter: process.env.NODE_ENV === 'development' });
    console.log('✅ All models synchronized successfully.');
  } catch (error) {
    console.error('❌ Error initializing models:', error);
    throw error;
  }
};

export { initializeModels };

export default {
  sequelize,
  User,
  CV,
  initializeModels,
};
