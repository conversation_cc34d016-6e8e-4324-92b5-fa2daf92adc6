{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useCV } from '../contexts/CVContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport Button from '../components/ui/Button';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    cvs,\n    fetchCVs,\n    isLoading\n  } = useCV();\n  useEffect(() => {\n    fetchCVs();\n  }, [fetchCVs]);\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-900\",\n        children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name, \"!\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-2 text-gray-600\",\n        children: \"Manage your CVs and create new ones\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-900\",\n        children: \"Your CVs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/cv-builder\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          children: \"Create New CV\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), cvs.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"mx-auto h-12 w-12 text-gray-400\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: 2,\n          d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"mt-2 text-sm font-medium text-gray-900\",\n        children: \"No CVs yet\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mt-1 text-sm text-gray-500\",\n        children: \"Get started by creating your first CV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-6\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/cv-builder\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            children: \"Create Your First CV\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: cvs.map(cv => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: cv.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500 mb-4\",\n          children: [\"Updated \", new Date(cv.updatedAt).toLocaleDateString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: `/cv-builder/${cv.id}`,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"sm\",\n              variant: \"outline\",\n              children: \"Edit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: `/cv-preview/${cv.id}`,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"sm\",\n              variant: \"outline\",\n              children: \"Preview\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 15\n        }, this)]\n      }, cv.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"g98lOCo4S7WLj50aXqWQYH3wdOg=\", false, function () {\n  return [useAuth, useCV];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "Link", "useCV", "useAuth", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "cvs", "fetchCVs", "isLoading", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "to", "length", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "map", "cv", "title", "Date", "updatedAt", "toLocaleDateString", "id", "size", "variant", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useCV } from '../contexts/CVContext';\nimport { useAuth } from '../contexts/AuthContext';\nimport Button from '../components/ui/Button';\n\nconst Dashboard: React.FC = () => {\n  const { user } = useAuth();\n  const { cvs, fetchCVs, isLoading } = useCV();\n\n  useEffect(() => {\n    fetchCVs();\n  }, [fetchCVs]);\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900\">\n          Welcome back, {user?.name}!\n        </h1>\n        <p className=\"mt-2 text-gray-600\">\n          Manage your CVs and create new ones\n        </p>\n      </div>\n\n      <div className=\"mb-6 flex justify-between items-center\">\n        <h2 className=\"text-xl font-semibold text-gray-900\">Your CVs</h2>\n        <Link to=\"/cv-builder\">\n          <Button>Create New CV</Button>\n        </Link>\n      </div>\n\n      {cvs.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <svg\n            className=\"mx-auto h-12 w-12 text-gray-400\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            />\n          </svg>\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No CVs yet</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">\n            Get started by creating your first CV\n          </p>\n          <div className=\"mt-6\">\n            <Link to=\"/cv-builder\">\n              <Button>Create Your First CV</Button>\n            </Link>\n          </div>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {cvs.map((cv) => (\n            <div\n              key={cv.id}\n              className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\"\n            >\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                {cv.title}\n              </h3>\n              <p className=\"text-sm text-gray-500 mb-4\">\n                Updated {new Date(cv.updatedAt).toLocaleDateString()}\n              </p>\n              <div className=\"flex space-x-2\">\n                <Link to={`/cv-builder/${cv.id}`}>\n                  <Button size=\"sm\" variant=\"outline\">\n                    Edit\n                  </Button>\n                </Link>\n                <Link to={`/cv-preview/${cv.id}`}>\n                  <Button size=\"sm\" variant=\"outline\">\n                    Preview\n                  </Button>\n                </Link>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,MAAM,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAK,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEO,GAAG;IAAEC,QAAQ;IAAEC;EAAU,CAAC,GAAGV,KAAK,CAAC,CAAC;EAE5CF,SAAS,CAAC,MAAM;IACdW,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,IAAIC,SAAS,EAAE;IACb,oBACEN,OAAA;MAAKO,SAAS,EAAC,2CAA2C;MAAAC,QAAA,eACxDR,OAAA;QAAKO,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,oBACEZ,OAAA;IAAKO,SAAS,EAAC,6CAA6C;IAAAC,QAAA,gBAC1DR,OAAA;MAAKO,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBR,OAAA;QAAIO,SAAS,EAAC,kCAAkC;QAAAC,QAAA,GAAC,gBACjC,EAACL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,EAAC,GAC5B;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLZ,OAAA;QAAGO,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAAC;MAElC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENZ,OAAA;MAAKO,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDR,OAAA;QAAIO,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjEZ,OAAA,CAACL,IAAI;QAACmB,EAAE,EAAC,aAAa;QAAAN,QAAA,eACpBR,OAAA,CAACF,MAAM;UAAAU,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAELR,GAAG,CAACW,MAAM,KAAK,CAAC,gBACff,OAAA;MAAKO,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCR,OAAA;QACEO,SAAS,EAAC,iCAAiC;QAC3CS,IAAI,EAAC,MAAM;QACXC,MAAM,EAAC,cAAc;QACrBC,OAAO,EAAC,WAAW;QAAAV,QAAA,eAEnBR,OAAA;UACEmB,aAAa,EAAC,OAAO;UACrBC,cAAc,EAAC,OAAO;UACtBC,WAAW,EAAE,CAAE;UACfC,CAAC,EAAC;QAAsH;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNZ,OAAA;QAAIO,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtEZ,OAAA;QAAGO,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJZ,OAAA;QAAKO,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBR,OAAA,CAACL,IAAI;UAACmB,EAAE,EAAC,aAAa;UAAAN,QAAA,eACpBR,OAAA,CAACF,MAAM;YAAAU,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENZ,OAAA;MAAKO,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEJ,GAAG,CAACmB,GAAG,CAAEC,EAAE,iBACVxB,OAAA;QAEEO,SAAS,EAAC,4FAA4F;QAAAC,QAAA,gBAEtGR,OAAA;UAAIO,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EACnDgB,EAAE,CAACC;QAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLZ,OAAA;UAAGO,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,UAChC,EAAC,IAAIkB,IAAI,CAACF,EAAE,CAACG,SAAS,CAAC,CAACC,kBAAkB,CAAC,CAAC;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACJZ,OAAA;UAAKO,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BR,OAAA,CAACL,IAAI;YAACmB,EAAE,EAAE,eAAeU,EAAE,CAACK,EAAE,EAAG;YAAArB,QAAA,eAC/BR,OAAA,CAACF,MAAM;cAACgC,IAAI,EAAC,IAAI;cAACC,OAAO,EAAC,SAAS;cAAAvB,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACPZ,OAAA,CAACL,IAAI;YAACmB,EAAE,EAAE,eAAeU,EAAE,CAACK,EAAE,EAAG;YAAArB,QAAA,eAC/BR,OAAA,CAACF,MAAM;cAACgC,IAAI,EAAC,IAAI;cAACC,OAAO,EAAC,SAAS;cAAAvB,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA,GApBDY,EAAE,CAACK,EAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqBP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACV,EAAA,CA1FID,SAAmB;EAAA,QACNJ,OAAO,EACaD,KAAK;AAAA;AAAAoC,EAAA,GAFtC/B,SAAmB;AA4FzB,eAAeA,SAAS;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}