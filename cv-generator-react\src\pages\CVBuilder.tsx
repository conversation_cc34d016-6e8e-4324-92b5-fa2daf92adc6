import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useCV } from '../contexts/CVContext';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import Select from '../components/ui/Select';
import Textarea from '../components/ui/Textarea';

interface PersonalInfo {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  website?: string;
  linkedin?: string;
  github?: string;
  summary?: string;
}

interface CVData {
  personal_info: PersonalInfo;
  education: any[];
  experience: any[];
  skills: any[];
  projects: any[];
  languages: any[];
}

const CVBuilder: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentCV, fetchCV, createCV, updateCV, setCurrentCV } = useCV();

  const [currentStep, setCurrentStep] = useState(0);
  const [cvTitle, setCvTitle] = useState('My CV');
  const [templateType, setTemplateType] = useState<'modern' | 'classic' | 'creative' | 'minimal'>('modern');
  const [cvData, setCvData] = useState<CVData>({
    personal_info: {
      first_name: '',
      last_name: '',
      email: '',
      phone: '',
      address: '',
      website: '',
      linkedin: '',
      github: '',
      summary: '',
    },
    education: [],
    experience: [],
    skills: [],
    projects: [],
    languages: [],
  });

  const steps = [
    { title: 'Basic Info', component: 'basic' },
    { title: 'Personal Info', component: 'personal' },
  ];

  useEffect(() => {
    if (id && id !== 'new') {
      fetchCV(parseInt(id));
    } else {
      setCurrentCV(null);
    }
  }, [id, fetchCV, setCurrentCV]);

  useEffect(() => {
    if (currentCV) {
      setCvTitle(currentCV.title);
      setTemplateType(currentCV.template_type);
      setCvData(currentCV.cv_data);
    }
  }, [currentCV]);

  const handleSave = async () => {
    try {
      const cvPayload = {
        title: cvTitle,
        template_type: templateType,
        cv_data: cvData,
      };

      if (id && id !== 'new') {
        await updateCV(parseInt(id), cvPayload);
      } else {
        const newCV = await createCV(cvPayload);
        navigate(`/cv-builder/${newCV.id}`, { replace: true });
      }
    } catch (error) {
      console.error('Failed to save CV:', error);
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updatePersonalInfo = (field: keyof PersonalInfo, value: string) => {
    setCvData(prev => ({
      ...prev,
      personal_info: {
        ...prev.personal_info,
        [field]: value,
      },
    }));
  };

  const addEducation = () => {
    const newEducation: Education = {
      id: Date.now().toString(),
      institution: '',
      degree: '',
      field_of_study: '',
      start_date: '',
      end_date: '',
      current: false,
      description: '',
      gpa: '',
    };
    setCvData(prev => ({
      ...prev,
      education: [...prev.education, newEducation],
    }));
  };

  const updateEducation = (index: number, field: keyof Education, value: string | boolean) => {
    setCvData(prev => ({
      ...prev,
      education: prev.education.map((edu, i) => 
        i === index ? { ...edu, [field]: value } : edu
      ),
    }));
  };

  const removeEducation = (index: number) => {
    setCvData(prev => ({
      ...prev,
      education: prev.education.filter((_, i) => i !== index),
    }));
  };

  const addExperience = () => {
    const newExperience: Experience = {
      id: Date.now().toString(),
      company: '',
      position: '',
      start_date: '',
      end_date: '',
      current: false,
      description: '',
      achievements: [],
    };
    setCvData(prev => ({
      ...prev,
      experience: [...prev.experience, newExperience],
    }));
  };

  const updateExperience = (index: number, field: keyof Experience, value: string | boolean | string[]) => {
    setCvData(prev => ({
      ...prev,
      experience: prev.experience.map((exp, i) => 
        i === index ? { ...exp, [field]: value } : exp
      ),
    }));
  };

  const removeExperience = (index: number) => {
    setCvData(prev => ({
      ...prev,
      experience: prev.experience.filter((_, i) => i !== index),
    }));
  };

  const addSkill = () => {
    const newSkill: Skill = {
      id: Date.now().toString(),
      name: '',
      level: 'Intermediate',
      category: '',
    };
    setCvData(prev => ({
      ...prev,
      skills: [...prev.skills, newSkill],
    }));
  };

  const updateSkill = (index: number, field: keyof Skill, value: string) => {
    setCvData(prev => ({
      ...prev,
      skills: prev.skills.map((skill, i) => 
        i === index ? { ...skill, [field]: value } : skill
      ),
    }));
  };

  const removeSkill = (index: number) => {
    setCvData(prev => ({
      ...prev,
      skills: prev.skills.filter((_, i) => i !== index),
    }));
  };

  const renderBasicInfo = () => (
    <div>
      <h3 style={{ fontSize: '1.125rem', fontWeight: '500', color: '#111827', marginBottom: '1.5rem' }}>
        Basic Information
      </h3>
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>
        <Input
          label="CV Title"
          placeholder="e.g., Software Engineer Resume"
          value={cvTitle}
          onChange={setCvTitle}
          required
        />
        <Select
          label="Template"
          value={templateType}
          onChange={(value) => setTemplateType(value as any)}
          options={[
            { value: 'modern', label: 'Modern' },
            { value: 'classic', label: 'Classic' },
            { value: 'creative', label: 'Creative' },
            { value: 'minimal', label: 'Minimal' },
          ]}
          required
        />
      </div>
    </div>
  );

  const renderPersonalInfo = () => (
    <div>
      <h3 style={{ fontSize: '1.125rem', fontWeight: '500', color: '#111827', marginBottom: '1.5rem' }}>
        Personal Information
      </h3>
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginBottom: '1rem' }}>
        <Input
          label="First Name"
          placeholder="John"
          value={cvData.personal_info.first_name}
          onChange={(value) => updatePersonalInfo('first_name', value)}
          required
        />
        <Input
          label="Last Name"
          placeholder="Doe"
          value={cvData.personal_info.last_name}
          onChange={(value) => updatePersonalInfo('last_name', value)}
          required
        />
        <Input
          label="Email"
          type="email"
          placeholder="<EMAIL>"
          value={cvData.personal_info.email}
          onChange={(value) => updatePersonalInfo('email', value)}
          required
        />
        <Input
          label="Phone"
          type="tel"
          placeholder="+****************"
          value={cvData.personal_info.phone || ''}
          onChange={(value) => updatePersonalInfo('phone', value)}
        />
      </div>
      <div style={{ marginBottom: '1rem' }}>
        <Input
          label="Address"
          placeholder="123 Main St, City, State 12345"
          value={cvData.personal_info.address || ''}
          onChange={(value) => updatePersonalInfo('address', value)}
        />
      </div>
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginBottom: '1rem' }}>
        <Input
          label="Website"
          type="url"
          placeholder="https://johndoe.com"
          value={cvData.personal_info.website || ''}
          onChange={(value) => updatePersonalInfo('website', value)}
        />
        <Input
          label="LinkedIn"
          type="url"
          placeholder="https://linkedin.com/in/johndoe"
          value={cvData.personal_info.linkedin || ''}
          onChange={(value) => updatePersonalInfo('linkedin', value)}
        />
      </div>
      <div style={{ marginBottom: '1rem' }}>
        <Input
          label="GitHub"
          type="url"
          placeholder="https://github.com/johndoe"
          value={cvData.personal_info.github || ''}
          onChange={(value) => updatePersonalInfo('github', value)}
        />
      </div>
      <Textarea
        label="Professional Summary"
        placeholder="Brief description of your professional background and goals..."
        value={cvData.personal_info.summary || ''}
        onChange={(value) => updatePersonalInfo('summary', value)}
        rows={4}
      />
    </div>
  );

  const renderEducation = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Education</h3>
        <Button onClick={addEducation} size="sm">Add Education</Button>
      </div>
      {cvData.education.map((edu, index) => (
        <div key={edu.id} className="border border-gray-200 rounded-lg p-6 space-y-4">
          <div className="flex justify-between items-start">
            <h4 className="text-md font-medium text-gray-800">Education #{index + 1}</h4>
            <Button
              onClick={() => removeEducation(index)}
              variant="danger"
              size="sm"
            >
              Remove
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Institution"
              placeholder="University of Example"
              value={edu.institution}
              onChange={(value) => updateEducation(index, 'institution', value)}
              required
            />
            <Input
              label="Degree"
              placeholder="Bachelor of Science"
              value={edu.degree}
              onChange={(value) => updateEducation(index, 'degree', value)}
              required
            />
            <Input
              label="Field of Study"
              placeholder="Computer Science"
              value={edu.field_of_study}
              onChange={(value) => updateEducation(index, 'field_of_study', value)}
              required
            />
            <Input
              label="GPA (Optional)"
              placeholder="3.8"
              value={edu.gpa || ''}
              onChange={(value) => updateEducation(index, 'gpa', value)}
            />
            <Input
              label="Start Date"
              type="date"
              value={edu.start_date}
              onChange={(value) => updateEducation(index, 'start_date', value)}
              required
            />
            <Input
              label="End Date"
              type="date"
              value={edu.end_date || ''}
              onChange={(value) => updateEducation(index, 'end_date', value)}
              disabled={edu.current}
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id={`current-education-${index}`}
              checked={edu.current}
              onChange={(e) => updateEducation(index, 'current', e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor={`current-education-${index}`} className="ml-2 text-sm text-gray-700">
              Currently studying here
            </label>
          </div>
          <Textarea
            label="Description (Optional)"
            placeholder="Relevant coursework, achievements, activities..."
            value={edu.description || ''}
            onChange={(value) => updateEducation(index, 'description', value)}
            rows={3}
          />
        </div>
      ))}
      {cvData.education.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No education entries yet. Click "Add Education" to get started.</p>
        </div>
      )}
    </div>
  );

  const renderExperience = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Work Experience</h3>
        <Button onClick={addExperience} size="sm">Add Experience</Button>
      </div>
      {cvData.experience.map((exp, index) => (
        <div key={exp.id} className="border border-gray-200 rounded-lg p-6 space-y-4">
          <div className="flex justify-between items-start">
            <h4 className="text-md font-medium text-gray-800">Experience #{index + 1}</h4>
            <Button
              onClick={() => removeExperience(index)}
              variant="danger"
              size="sm"
            >
              Remove
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Company"
              placeholder="Tech Company Inc."
              value={exp.company}
              onChange={(value) => updateExperience(index, 'company', value)}
              required
            />
            <Input
              label="Position"
              placeholder="Software Engineer"
              value={exp.position}
              onChange={(value) => updateExperience(index, 'position', value)}
              required
            />
            <Input
              label="Start Date"
              type="date"
              value={exp.start_date}
              onChange={(value) => updateExperience(index, 'start_date', value)}
              required
            />
            <Input
              label="End Date"
              type="date"
              value={exp.end_date || ''}
              onChange={(value) => updateExperience(index, 'end_date', value)}
              disabled={exp.current}
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id={`current-experience-${index}`}
              checked={exp.current}
              onChange={(e) => updateExperience(index, 'current', e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor={`current-experience-${index}`} className="ml-2 text-sm text-gray-700">
              Currently working here
            </label>
          </div>
          <Textarea
            label="Job Description"
            placeholder="Describe your responsibilities and achievements..."
            value={exp.description || ''}
            onChange={(value) => updateExperience(index, 'description', value)}
            rows={4}
          />
        </div>
      ))}
      {cvData.experience.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No work experience entries yet. Click "Add Experience" to get started.</p>
        </div>
      )}
    </div>
  );

  const renderSkills = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Skills</h3>
        <Button onClick={addSkill} size="sm">Add Skill</Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {cvData.skills.map((skill, index) => (
          <div key={skill.id} className="border border-gray-200 rounded-lg p-4 space-y-3">
            <div className="flex justify-between items-start">
              <h4 className="text-sm font-medium text-gray-800">Skill #{index + 1}</h4>
              <Button
                onClick={() => removeSkill(index)}
                variant="danger"
                size="sm"
              >
                Remove
              </Button>
            </div>
            <Input
              label="Skill Name"
              placeholder="JavaScript"
              value={skill.name}
              onChange={(value) => updateSkill(index, 'name', value)}
              required
            />
            <Select
              label="Proficiency Level"
              value={skill.level}
              onChange={(value) => updateSkill(index, 'level', value)}
              options={[
                { value: 'Beginner', label: 'Beginner' },
                { value: 'Intermediate', label: 'Intermediate' },
                { value: 'Advanced', label: 'Advanced' },
                { value: 'Expert', label: 'Expert' },
              ]}
              required
            />
            <Input
              label="Category (Optional)"
              placeholder="Programming Languages"
              value={skill.category || ''}
              onChange={(value) => updateSkill(index, 'category', value)}
            />
          </div>
        ))}
      </div>
      {cvData.skills.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No skills added yet. Click "Add Skill" to get started.</p>
        </div>
      )}
    </div>
  );

  const renderProjects = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Projects</h3>
        <Button onClick={() => {
          const newProject: Project = {
            id: Date.now().toString(),
            name: '',
            description: '',
            technologies: [],
            start_date: '',
            end_date: '',
            url: '',
            github_url: '',
          };
          setCvData(prev => ({
            ...prev,
            projects: [...prev.projects, newProject],
          }));
        }} size="sm">Add Project</Button>
      </div>
      {cvData.projects.map((project, index) => (
        <div key={project.id} className="border border-gray-200 rounded-lg p-6 space-y-4">
          <div className="flex justify-between items-start">
            <h4 className="text-md font-medium text-gray-800">Project #{index + 1}</h4>
            <Button
              onClick={() => setCvData(prev => ({
                ...prev,
                projects: prev.projects.filter((_, i) => i !== index),
              }))}
              variant="danger"
              size="sm"
            >
              Remove
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Project Name"
              placeholder="My Awesome Project"
              value={project.name}
              onChange={(value) => setCvData(prev => ({
                ...prev,
                projects: prev.projects.map((p, i) =>
                  i === index ? { ...p, name: value } : p
                ),
              }))}
              required
            />
            <Input
              label="Technologies"
              placeholder="React, Node.js, MongoDB"
              value={project.technologies.join(', ')}
              onChange={(value) => setCvData(prev => ({
                ...prev,
                projects: prev.projects.map((p, i) =>
                  i === index ? { ...p, technologies: value.split(',').map(t => t.trim()) } : p
                ),
              }))}
            />
            <Input
              label="Project URL (Optional)"
              type="url"
              placeholder="https://myproject.com"
              value={project.url || ''}
              onChange={(value) => setCvData(prev => ({
                ...prev,
                projects: prev.projects.map((p, i) =>
                  i === index ? { ...p, url: value } : p
                ),
              }))}
            />
            <Input
              label="GitHub URL (Optional)"
              type="url"
              placeholder="https://github.com/user/project"
              value={project.github_url || ''}
              onChange={(value) => setCvData(prev => ({
                ...prev,
                projects: prev.projects.map((p, i) =>
                  i === index ? { ...p, github_url: value } : p
                ),
              }))}
            />
          </div>
          <Textarea
            label="Project Description"
            placeholder="Describe what the project does and your role..."
            value={project.description}
            onChange={(value) => setCvData(prev => ({
              ...prev,
              projects: prev.projects.map((p, i) =>
                i === index ? { ...p, description: value } : p
              ),
            }))}
            rows={3}
          />
        </div>
      ))}
      {cvData.projects.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No projects added yet. Click "Add Project" to get started.</p>
        </div>
      )}
    </div>
  );

  const renderLanguages = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Languages</h3>
        <Button onClick={() => {
          const newLanguage: Language = {
            id: Date.now().toString(),
            name: '',
            proficiency: 'Conversational',
          };
          setCvData(prev => ({
            ...prev,
            languages: [...prev.languages, newLanguage],
          }));
        }} size="sm">Add Language</Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {cvData.languages.map((language, index) => (
          <div key={language.id} className="border border-gray-200 rounded-lg p-4 space-y-3">
            <div className="flex justify-between items-start">
              <h4 className="text-sm font-medium text-gray-800">Language #{index + 1}</h4>
              <Button
                onClick={() => setCvData(prev => ({
                  ...prev,
                  languages: prev.languages.filter((_, i) => i !== index),
                }))}
                variant="danger"
                size="sm"
              >
                Remove
              </Button>
            </div>
            <Input
              label="Language"
              placeholder="English"
              value={language.name}
              onChange={(value) => setCvData(prev => ({
                ...prev,
                languages: prev.languages.map((l, i) =>
                  i === index ? { ...l, name: value } : l
                ),
              }))}
              required
            />
            <Select
              label="Proficiency Level"
              value={language.proficiency}
              onChange={(value) => setCvData(prev => ({
                ...prev,
                languages: prev.languages.map((l, i) =>
                  i === index ? { ...l, proficiency: value as any } : l
                ),
              }))}
              options={[
                { value: 'Basic', label: 'Basic' },
                { value: 'Conversational', label: 'Conversational' },
                { value: 'Fluent', label: 'Fluent' },
                { value: 'Native', label: 'Native' },
              ]}
              required
            />
          </div>
        ))}
      </div>
      {cvData.languages.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No languages added yet. Click "Add Language" to get started.</p>
        </div>
      )}
    </div>
  );

  const renderCurrentStep = () => {
    switch (steps[currentStep].component) {
      case 'basic':
        return renderBasicInfo();
      case 'personal':
        return renderPersonalInfo();
      default:
        return renderBasicInfo();
    }
  };

  return (
    <div style={{ maxWidth: '64rem', margin: '0 auto', padding: '2rem 1rem' }}>
      {/* Header */}
      <div style={{ marginBottom: '2rem' }}>
        <h1 style={{ fontSize: '1.875rem', fontWeight: 'bold', color: '#111827', marginBottom: '0.5rem' }}>
          {id && id !== 'new' ? 'Edit CV' : 'Create New CV'}
        </h1>
        <p style={{ color: '#6b7280' }}>
          Fill out the information below to create your professional CV
        </p>
      </div>

      {/* Progress Steps */}
      <div style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {steps.map((step, index) => (
            <div key={step.title} style={{ display: 'flex', alignItems: 'center', marginRight: index !== steps.length - 1 ? '2rem' : '0' }}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '2rem',
                  height: '2rem',
                  borderRadius: '50%',
                  backgroundColor: index < currentStep ? '#3b82f6' : index === currentStep ? 'white' : 'white',
                  border: index === currentStep ? '2px solid #3b82f6' : '2px solid #d1d5db',
                  color: index < currentStep ? 'white' : index === currentStep ? '#3b82f6' : '#6b7280',
                  fontSize: '0.875rem',
                  fontWeight: '500'
                }}
              >
                {index < currentStep ? (
                  <svg style={{ width: '1.25rem', height: '1.25rem' }} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  index + 1
                )}
              </div>
              <span style={{
                marginLeft: '1rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: index === currentStep ? '#3b82f6' : '#6b7280'
              }}>
                {step.title}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Form Content */}
      <div style={{ backgroundColor: 'white', boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)', borderRadius: '0.5rem', padding: '1.5rem', marginBottom: '2rem' }}>
        {renderCurrentStep()}
      </div>

      {/* Navigation Buttons */}
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div>
          {currentStep > 0 && (
            <Button
              onClick={handlePrevious}
              variant="outline"
            >
              Previous
            </Button>
          )}
        </div>
        <div style={{ display: 'flex', gap: '1rem' }}>
          <Button
            onClick={handleSave}
            variant="outline"
          >
            Save Draft
          </Button>
          {currentStep < steps.length - 1 ? (
            <Button onClick={handleNext}>
              Next
            </Button>
          ) : (
            <Button onClick={() => navigate(`/dashboard`)}>
              Finish
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CVBuilder;
