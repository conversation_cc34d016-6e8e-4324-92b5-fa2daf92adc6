[{"C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\components\\ProtectedRoute.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\contexts\\CVContext.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\contexts\\AuthContext.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\components\\PublicRoute.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\layouts\\Layout.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\Home.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\Login.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\Register.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\CVBuilder.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\Dashboard.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\PublicCV.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\CVPreview.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\Profile.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\NotFound.tsx": "17", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\utils\\helpers.ts": "18", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\services\\api.ts": "19", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\components\\ui\\Button.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\components\\ui\\Input.tsx": "21"}, {"size": 554, "mtime": 1751269314796, "results": "22", "hashOfConfig": "23"}, {"size": 425, "mtime": 1751269314400, "results": "24", "hashOfConfig": "23"}, {"size": 3862, "mtime": 1751270832037, "results": "25", "hashOfConfig": "23"}, {"size": 851, "mtime": 1751270884485, "results": "26", "hashOfConfig": "23"}, {"size": 6149, "mtime": 1751270771254, "results": "27", "hashOfConfig": "23"}, {"size": 6058, "mtime": 1751282786594, "results": "28", "hashOfConfig": "23"}, {"size": 776, "mtime": 1751270893957, "results": "29", "hashOfConfig": "23"}, {"size": 6486, "mtime": 1751284001428, "results": "30", "hashOfConfig": "23"}, {"size": 5800, "mtime": 1751271204824, "results": "31", "hashOfConfig": "23"}, {"size": 5763, "mtime": 1751282815201, "results": "32", "hashOfConfig": "23"}, {"size": 7887, "mtime": 1751283916997, "results": "33", "hashOfConfig": "23"}, {"size": 713, "mtime": 1751271241329, "results": "34", "hashOfConfig": "23"}, {"size": 3215, "mtime": 1751284017227, "results": "35", "hashOfConfig": "23"}, {"size": 697, "mtime": 1751271273461, "results": "36", "hashOfConfig": "23"}, {"size": 700, "mtime": 1751271251282, "results": "37", "hashOfConfig": "23"}, {"size": 697, "mtime": 1751271262426, "results": "38", "hashOfConfig": "23"}, {"size": 780, "mtime": 1751271286313, "results": "39", "hashOfConfig": "23"}, {"size": 6700, "mtime": 1751270809105, "results": "40", "hashOfConfig": "23"}, {"size": 5489, "mtime": 1751270687713, "results": "41", "hashOfConfig": "23"}, {"size": 2007, "mtime": 1751282683163, "results": "42", "hashOfConfig": "23"}, {"size": 1256, "mtime": 1751282705692, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jb299i", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\contexts\\CVContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\components\\PublicRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\layouts\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\Home.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\Register.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\CVBuilder.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\PublicCV.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\CVPreview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\Profile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\pages\\NotFound.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\utils\\helpers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\frontend\\src\\components\\ui\\Input.tsx", [], []]