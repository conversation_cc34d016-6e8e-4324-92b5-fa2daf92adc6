{"ast": null, "code": "import axios from 'axios';\nclass ApiService {\n  constructor() {\n    this.api = void 0;\n    this.api = axios.create({\n      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json'\n      }\n    });\n\n    // Request interceptor to add auth token\n    this.api.interceptors.request.use(config => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // Response interceptor to handle errors\n    this.api.interceptors.response.use(response => response, error => {\n      var _error$response;\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  // Authentication methods\n  async register(data) {\n    const response = await this.api.post('/auth/register', data);\n    return response.data;\n  }\n  async login(credentials) {\n    const response = await this.api.post('/auth/login', credentials);\n    return response.data;\n  }\n  async logout() {\n    const response = await this.api.post('/auth/logout');\n    return response.data;\n  }\n  async getProfile() {\n    const response = await this.api.get('/auth/profile');\n    return response.data.user;\n  }\n  async updateProfile(data) {\n    const response = await this.api.put('/auth/profile', data);\n    return response.data.user;\n  }\n  async changePassword(data) {\n    const response = await this.api.put('/auth/password', data);\n    return response.data;\n  }\n\n  // CV methods\n  async getCVs() {\n    const response = await this.api.get('/cvs');\n    return response.data.cvs;\n  }\n  async getCV(id) {\n    const response = await this.api.get(`/cvs/${id}`);\n    return response.data.cv;\n  }\n  async createCV(data) {\n    const response = await this.api.post('/cvs', data);\n    return response.data.cv;\n  }\n  async updateCV(id, data) {\n    const response = await this.api.put(`/cvs/${id}`, data);\n    return response.data.cv;\n  }\n  async deleteCV(id) {\n    const response = await this.api.delete(`/cvs/${id}`);\n    return response.data;\n  }\n  async duplicateCV(id) {\n    const response = await this.api.post(`/cvs/${id}/duplicate`);\n    return response.data.cv;\n  }\n  async getCVStatistics() {\n    const response = await this.api.get('/cvs/statistics');\n    return response.data.statistics;\n  }\n\n  // Public CV methods\n  async getPublicCV(slug) {\n    const response = await this.api.get(`/cv/public/${slug}`);\n    return response.data.cv;\n  }\n\n  // PDF generation\n  async generatePDF(id) {\n    const response = await this.api.get(`/cvs/${id}/pdf`, {\n      responseType: 'blob'\n    });\n    return response.data;\n  }\n  async generatePublicPDF(slug) {\n    const response = await this.api.get(`/cv/public/${slug}/pdf`, {\n      responseType: 'blob'\n    });\n    return response.data;\n  }\n}\nexport const apiService = new ApiService();\nexport default apiService;", "map": {"version": 3, "names": ["axios", "ApiService", "constructor", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "register", "data", "post", "login", "credentials", "logout", "getProfile", "get", "user", "updateProfile", "put", "changePassword", "getCVs", "cvs", "getCV", "id", "cv", "createCV", "updateCV", "deleteCV", "delete", "duplicateCV", "getCVStatistics", "statistics", "getPublicCV", "slug", "generatePDF", "responseType", "generatePublicPDF", "apiService"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/cv-generator-react/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse } from 'axios';\nimport {\n  AuthResponse,\n  LoginCredentials,\n  RegisterData,\n  User,\n  CV,\n  ApiResponse\n} from '../types';\n\nclass ApiService {\n  private api: AxiosInstance;\n\n  constructor() {\n    this.api = axios.create({\n      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api',\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n    });\n\n    // Request interceptor to add auth token\n    this.api.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem('token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => {\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor to handle errors\n    this.api.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        if (error.response?.status === 401) {\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          window.location.href = '/login';\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // Authentication methods\n  async register(data: RegisterData): Promise<AuthResponse> {\n    const response: AxiosResponse<AuthResponse> = await this.api.post('/auth/register', data);\n    return response.data;\n  }\n\n  async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    const response: AxiosResponse<AuthResponse> = await this.api.post('/auth/login', credentials);\n    return response.data;\n  }\n\n  async logout(): Promise<ApiResponse> {\n    const response: AxiosResponse<ApiResponse> = await this.api.post('/auth/logout');\n    return response.data;\n  }\n\n  async getProfile(): Promise<User> {\n    const response: AxiosResponse<{ success: boolean; user: User }> = await this.api.get('/auth/profile');\n    return response.data.user;\n  }\n\n  async updateProfile(data: Partial<User>): Promise<User> {\n    const response: AxiosResponse<{ success: boolean; user: User }> = await this.api.put('/auth/profile', data);\n    return response.data.user;\n  }\n\n  async changePassword(data: { current_password: string; password: string; password_confirmation: string }): Promise<ApiResponse> {\n    const response: AxiosResponse<ApiResponse> = await this.api.put('/auth/password', data);\n    return response.data;\n  }\n\n  // CV methods\n  async getCVs(): Promise<CV[]> {\n    const response: AxiosResponse<{ success: boolean; cvs: CV[] }> = await this.api.get('/cvs');\n    return response.data.cvs;\n  }\n\n  async getCV(id: number): Promise<CV> {\n    const response: AxiosResponse<{ success: boolean; cv: CV }> = await this.api.get(`/cvs/${id}`);\n    return response.data.cv;\n  }\n\n  async createCV(data: Partial<CV>): Promise<CV> {\n    const response: AxiosResponse<{ success: boolean; cv: CV }> = await this.api.post('/cvs', data);\n    return response.data.cv;\n  }\n\n  async updateCV(id: number, data: Partial<CV>): Promise<CV> {\n    const response: AxiosResponse<{ success: boolean; cv: CV }> = await this.api.put(`/cvs/${id}`, data);\n    return response.data.cv;\n  }\n\n  async deleteCV(id: number): Promise<ApiResponse> {\n    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/cvs/${id}`);\n    return response.data;\n  }\n\n  async duplicateCV(id: number): Promise<CV> {\n    const response: AxiosResponse<{ success: boolean; cv: CV }> = await this.api.post(`/cvs/${id}/duplicate`);\n    return response.data.cv;\n  }\n\n  async getCVStatistics(): Promise<any> {\n    const response: AxiosResponse<{ success: boolean; statistics: any }> = await this.api.get('/cvs/statistics');\n    return response.data.statistics;\n  }\n\n  // Public CV methods\n  async getPublicCV(slug: string): Promise<CV> {\n    const response: AxiosResponse<{ success: boolean; cv: CV }> = await this.api.get(`/cv/public/${slug}`);\n    return response.data.cv;\n  }\n\n  // PDF generation\n  async generatePDF(id: number): Promise<Blob> {\n    const response: AxiosResponse<Blob> = await this.api.get(`/cvs/${id}/pdf`, {\n      responseType: 'blob',\n    });\n    return response.data;\n  }\n\n  async generatePublicPDF(slug: string): Promise<Blob> {\n    const response: AxiosResponse<Blob> = await this.api.get(`/cv/public/${slug}/pdf`, {\n      responseType: 'blob',\n    });\n    return response.data;\n  }\n}\n\nexport const apiService = new ApiService();\nexport default apiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAwC,OAAO;AAU3D,MAAMC,UAAU,CAAC;EAGfC,WAAWA,CAAA,EAAG;IAAA,KAFNC,GAAG;IAGT,IAAI,CAACA,GAAG,GAAGH,KAAK,CAACI,MAAM,CAAC;MACtBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;MACrEC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClC,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC;;IAEF;IACA,IAAI,CAACN,GAAG,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAK;MACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;MAClD;MACA,OAAOD,MAAM;IACf,CAAC,EACAK,KAAK,IAAK;MACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACf,GAAG,CAACO,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC/BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;MAAA,IAAAI,eAAA;MACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClCR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;QAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;QAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MACjC;MACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;EACA,MAAMU,QAAQA,CAACC,IAAkB,EAAyB;IACxD,MAAMR,QAAqC,GAAG,MAAM,IAAI,CAAClB,GAAG,CAAC2B,IAAI,CAAC,gBAAgB,EAAED,IAAI,CAAC;IACzF,OAAOR,QAAQ,CAACQ,IAAI;EACtB;EAEA,MAAME,KAAKA,CAACC,WAA6B,EAAyB;IAChE,MAAMX,QAAqC,GAAG,MAAM,IAAI,CAAClB,GAAG,CAAC2B,IAAI,CAAC,aAAa,EAAEE,WAAW,CAAC;IAC7F,OAAOX,QAAQ,CAACQ,IAAI;EACtB;EAEA,MAAMI,MAAMA,CAAA,EAAyB;IACnC,MAAMZ,QAAoC,GAAG,MAAM,IAAI,CAAClB,GAAG,CAAC2B,IAAI,CAAC,cAAc,CAAC;IAChF,OAAOT,QAAQ,CAACQ,IAAI;EACtB;EAEA,MAAMK,UAAUA,CAAA,EAAkB;IAChC,MAAMb,QAAyD,GAAG,MAAM,IAAI,CAAClB,GAAG,CAACgC,GAAG,CAAC,eAAe,CAAC;IACrG,OAAOd,QAAQ,CAACQ,IAAI,CAACO,IAAI;EAC3B;EAEA,MAAMC,aAAaA,CAACR,IAAmB,EAAiB;IACtD,MAAMR,QAAyD,GAAG,MAAM,IAAI,CAAClB,GAAG,CAACmC,GAAG,CAAC,eAAe,EAAET,IAAI,CAAC;IAC3G,OAAOR,QAAQ,CAACQ,IAAI,CAACO,IAAI;EAC3B;EAEA,MAAMG,cAAcA,CAACV,IAAmF,EAAwB;IAC9H,MAAMR,QAAoC,GAAG,MAAM,IAAI,CAAClB,GAAG,CAACmC,GAAG,CAAC,gBAAgB,EAAET,IAAI,CAAC;IACvF,OAAOR,QAAQ,CAACQ,IAAI;EACtB;;EAEA;EACA,MAAMW,MAAMA,CAAA,EAAkB;IAC5B,MAAMnB,QAAwD,GAAG,MAAM,IAAI,CAAClB,GAAG,CAACgC,GAAG,CAAC,MAAM,CAAC;IAC3F,OAAOd,QAAQ,CAACQ,IAAI,CAACY,GAAG;EAC1B;EAEA,MAAMC,KAAKA,CAACC,EAAU,EAAe;IACnC,MAAMtB,QAAqD,GAAG,MAAM,IAAI,CAAClB,GAAG,CAACgC,GAAG,CAAC,QAAQQ,EAAE,EAAE,CAAC;IAC9F,OAAOtB,QAAQ,CAACQ,IAAI,CAACe,EAAE;EACzB;EAEA,MAAMC,QAAQA,CAAChB,IAAiB,EAAe;IAC7C,MAAMR,QAAqD,GAAG,MAAM,IAAI,CAAClB,GAAG,CAAC2B,IAAI,CAAC,MAAM,EAAED,IAAI,CAAC;IAC/F,OAAOR,QAAQ,CAACQ,IAAI,CAACe,EAAE;EACzB;EAEA,MAAME,QAAQA,CAACH,EAAU,EAAEd,IAAiB,EAAe;IACzD,MAAMR,QAAqD,GAAG,MAAM,IAAI,CAAClB,GAAG,CAACmC,GAAG,CAAC,QAAQK,EAAE,EAAE,EAAEd,IAAI,CAAC;IACpG,OAAOR,QAAQ,CAACQ,IAAI,CAACe,EAAE;EACzB;EAEA,MAAMG,QAAQA,CAACJ,EAAU,EAAwB;IAC/C,MAAMtB,QAAoC,GAAG,MAAM,IAAI,CAAClB,GAAG,CAAC6C,MAAM,CAAC,QAAQL,EAAE,EAAE,CAAC;IAChF,OAAOtB,QAAQ,CAACQ,IAAI;EACtB;EAEA,MAAMoB,WAAWA,CAACN,EAAU,EAAe;IACzC,MAAMtB,QAAqD,GAAG,MAAM,IAAI,CAAClB,GAAG,CAAC2B,IAAI,CAAC,QAAQa,EAAE,YAAY,CAAC;IACzG,OAAOtB,QAAQ,CAACQ,IAAI,CAACe,EAAE;EACzB;EAEA,MAAMM,eAAeA,CAAA,EAAiB;IACpC,MAAM7B,QAA8D,GAAG,MAAM,IAAI,CAAClB,GAAG,CAACgC,GAAG,CAAC,iBAAiB,CAAC;IAC5G,OAAOd,QAAQ,CAACQ,IAAI,CAACsB,UAAU;EACjC;;EAEA;EACA,MAAMC,WAAWA,CAACC,IAAY,EAAe;IAC3C,MAAMhC,QAAqD,GAAG,MAAM,IAAI,CAAClB,GAAG,CAACgC,GAAG,CAAC,cAAckB,IAAI,EAAE,CAAC;IACtG,OAAOhC,QAAQ,CAACQ,IAAI,CAACe,EAAE;EACzB;;EAEA;EACA,MAAMU,WAAWA,CAACX,EAAU,EAAiB;IAC3C,MAAMtB,QAA6B,GAAG,MAAM,IAAI,CAAClB,GAAG,CAACgC,GAAG,CAAC,QAAQQ,EAAE,MAAM,EAAE;MACzEY,YAAY,EAAE;IAChB,CAAC,CAAC;IACF,OAAOlC,QAAQ,CAACQ,IAAI;EACtB;EAEA,MAAM2B,iBAAiBA,CAACH,IAAY,EAAiB;IACnD,MAAMhC,QAA6B,GAAG,MAAM,IAAI,CAAClB,GAAG,CAACgC,GAAG,CAAC,cAAckB,IAAI,MAAM,EAAE;MACjFE,YAAY,EAAE;IAChB,CAAC,CAAC;IACF,OAAOlC,QAAQ,CAACQ,IAAI;EACtB;AACF;AAEA,OAAO,MAAM4B,UAAU,GAAG,IAAIxD,UAAU,CAAC,CAAC;AAC1C,eAAewD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}