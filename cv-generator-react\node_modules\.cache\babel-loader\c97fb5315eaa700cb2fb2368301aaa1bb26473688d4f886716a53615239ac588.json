{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\cv-generator-react\\\\src\\\\pages\\\\Register.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport Button from '../components/ui/Button';\nimport Input from '../components/ui/Input';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    register,\n    isLoading,\n    error,\n    clearError,\n    isAuthenticated\n  } = useAuth();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    password_confirmation: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [agreeTerms, setAgreeTerms] = useState(false);\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/dashboard', {\n        replace: true\n      });\n    }\n  }, [isAuthenticated, navigate]);\n\n  // Clear errors when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.trim().length < 2) {\n      newErrors.name = 'Name must be at least 2 characters long';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 8) {\n      newErrors.password = 'Password must be at least 8 characters long';\n    }\n    if (!formData.password_confirmation) {\n      newErrors.password_confirmation = 'Password confirmation is required';\n    } else if (formData.password !== formData.password_confirmation) {\n      newErrors.password_confirmation = 'Passwords do not match';\n    }\n    if (!agreeTerms) {\n      newErrors.terms = 'You must agree to the terms and conditions';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleInputChange = field => value => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    // Clear field error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: undefined\n      }));\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      await register(formData);\n      navigate('/dashboard', {\n        replace: true\n      });\n    } catch (error) {\n      // Error is handled by the auth context\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-6 w-6 text-primary-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n          children: \"Create your account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-center text-sm text-gray-600\",\n          children: [\"Or\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"font-medium text-primary-600 hover:text-primary-500\",\n            children: \"sign in to your existing account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \"mt-8 space-y-6\",\n        onSubmit: handleSubmit,\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-md bg-red-50 p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-red-700\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            label: \"Full Name\",\n            type: \"text\",\n            placeholder: \"Enter your full name\",\n            value: formData.name,\n            onChange: handleInputChange('name'),\n            error: errors.name,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Email address\",\n            type: \"email\",\n            placeholder: \"Enter your email\",\n            value: formData.email,\n            onChange: handleInputChange('email'),\n            error: errors.email,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Password\",\n            type: \"password\",\n            placeholder: \"Enter your password\",\n            value: formData.password,\n            onChange: handleInputChange('password'),\n            error: errors.password,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Confirm Password\",\n            type: \"password\",\n            placeholder: \"Confirm your password\",\n            value: formData.password_confirmation,\n            onChange: handleInputChange('password_confirmation'),\n            error: errors.password_confirmation,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"agree-terms\",\n            name: \"agree-terms\",\n            type: \"checkbox\",\n            className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\",\n            checked: agreeTerms,\n            onChange: e => {\n              setAgreeTerms(e.target.checked);\n              if (errors.terms) {\n                setErrors(prev => ({\n                  ...prev,\n                  terms: undefined\n                }));\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"agree-terms\",\n            className: \"ml-2 block text-sm text-gray-900\",\n            children: [\"I agree to the\", ' ', /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"text-primary-600 hover:text-primary-500 bg-transparent border-none cursor-pointer p-0\",\n              onClick: () => alert('Terms of Service coming soon!'),\n              children: \"Terms of Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), ' ', \"and\", ' ', /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"text-primary-600 hover:text-primary-500 bg-transparent border-none cursor-pointer p-0\",\n              onClick: () => alert('Privacy Policy coming soon!'),\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), errors.terms && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-red-600\",\n          children: errors.terms\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            className: \"w-full\",\n            loading: isLoading,\n            disabled: isLoading,\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"hTsZ7oQEMX7vosnomV0ANUhI5S0=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useNavigate", "useAuth", "<PERSON><PERSON>", "Input", "jsxDEV", "_jsxDEV", "Register", "_s", "navigate", "register", "isLoading", "error", "clearError", "isAuthenticated", "formData", "setFormData", "name", "email", "password", "password_confirmation", "errors", "setErrors", "agreeTerms", "setAgreeTerms", "replace", "validateForm", "newErrors", "trim", "length", "test", "terms", "Object", "keys", "handleInputChange", "field", "value", "prev", "undefined", "handleSubmit", "e", "preventDefault", "className", "children", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onSubmit", "label", "type", "placeholder", "onChange", "required", "id", "checked", "target", "htmlFor", "onClick", "alert", "loading", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/cv-generator-react/src/pages/Register.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport Button from '../components/ui/Button';\nimport Input from '../components/ui/Input';\nimport { RegisterData, FormErrors } from '../types';\n\nconst Register: React.FC = () => {\n  const navigate = useNavigate();\n  const { register, isLoading, error, clearError, isAuthenticated } = useAuth();\n  \n  const [formData, setFormData] = useState<RegisterData>({\n    name: '',\n    email: '',\n    password: '',\n    password_confirmation: '',\n  });\n  \n  const [errors, setErrors] = useState<FormErrors>({});\n  const [agreeTerms, setAgreeTerms] = useState(false);\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      navigate('/dashboard', { replace: true });\n    }\n  }, [isAuthenticated, navigate]);\n\n  // Clear errors when component mounts\n  useEffect(() => {\n    clearError();\n  }, [clearError]);\n\n  const validateForm = (): boolean => {\n    const newErrors: FormErrors = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.trim().length < 2) {\n      newErrors.name = 'Name must be at least 2 characters long';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = 'Email is invalid';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 8) {\n      newErrors.password = 'Password must be at least 8 characters long';\n    }\n\n    if (!formData.password_confirmation) {\n      newErrors.password_confirmation = 'Password confirmation is required';\n    } else if (formData.password !== formData.password_confirmation) {\n      newErrors.password_confirmation = 'Passwords do not match';\n    }\n\n    if (!agreeTerms) {\n      newErrors.terms = 'You must agree to the terms and conditions';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleInputChange = (field: keyof RegisterData) => (value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    // Clear field error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: undefined }));\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      await register(formData);\n      navigate('/dashboard', { replace: true });\n    } catch (error) {\n      // Error is handled by the auth context\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <div className=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100\">\n            <svg\n              className=\"h-6 w-6 text-primary-600\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n              />\n            </svg>\n          </div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Or{' '}\n            <Link\n              to=\"/login\"\n              className=\"font-medium text-primary-600 hover:text-primary-500\"\n            >\n              sign in to your existing account\n            </Link>\n          </p>\n        </div>\n        \n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          {error && (\n            <div className=\"rounded-md bg-red-50 p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n          \n          <div className=\"space-y-4\">\n            <Input\n              label=\"Full Name\"\n              type=\"text\"\n              placeholder=\"Enter your full name\"\n              value={formData.name}\n              onChange={handleInputChange('name')}\n              error={errors.name}\n              required\n            />\n            \n            <Input\n              label=\"Email address\"\n              type=\"email\"\n              placeholder=\"Enter your email\"\n              value={formData.email}\n              onChange={handleInputChange('email')}\n              error={errors.email}\n              required\n            />\n            \n            <Input\n              label=\"Password\"\n              type=\"password\"\n              placeholder=\"Enter your password\"\n              value={formData.password}\n              onChange={handleInputChange('password')}\n              error={errors.password}\n              required\n            />\n            \n            <Input\n              label=\"Confirm Password\"\n              type=\"password\"\n              placeholder=\"Confirm your password\"\n              value={formData.password_confirmation}\n              onChange={handleInputChange('password_confirmation')}\n              error={errors.password_confirmation}\n              required\n            />\n          </div>\n\n          <div className=\"flex items-center\">\n            <input\n              id=\"agree-terms\"\n              name=\"agree-terms\"\n              type=\"checkbox\"\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n              checked={agreeTerms}\n              onChange={(e) => {\n                setAgreeTerms(e.target.checked);\n                if (errors.terms) {\n                  setErrors(prev => ({ ...prev, terms: undefined }));\n                }\n              }}\n            />\n            <label htmlFor=\"agree-terms\" className=\"ml-2 block text-sm text-gray-900\">\n              I agree to the{' '}\n              <button\n                type=\"button\"\n                className=\"text-primary-600 hover:text-primary-500 bg-transparent border-none cursor-pointer p-0\"\n                onClick={() => alert('Terms of Service coming soon!')}\n              >\n                Terms of Service\n              </button>{' '}\n              and{' '}\n              <button\n                type=\"button\"\n                className=\"text-primary-600 hover:text-primary-500 bg-transparent border-none cursor-pointer p-0\"\n                onClick={() => alert('Privacy Policy coming soon!')}\n              >\n                Privacy Policy\n              </button>\n            </label>\n          </div>\n          {errors.terms && (\n            <p className=\"text-sm text-red-600\">{errors.terms}</p>\n          )}\n\n          <div>\n            <Button\n              type=\"submit\"\n              className=\"w-full\"\n              loading={isLoading}\n              disabled={isLoading}\n            >\n              Create Account\n            </Button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3C,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,QAAQ;IAAEC,SAAS;IAAEC,KAAK;IAAEC,UAAU;IAAEC;EAAgB,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAE7E,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAe;IACrDmB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,qBAAqB,EAAE;EACzB,CAAC,CAAC;EAEF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAa,CAAC,CAAC,CAAC;EACpD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIe,eAAe,EAAE;MACnBL,QAAQ,CAAC,YAAY,EAAE;QAAEgB,OAAO,EAAE;MAAK,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE,CAACX,eAAe,EAAEL,QAAQ,CAAC,CAAC;;EAE/B;EACAV,SAAS,CAAC,MAAM;IACdc,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAMa,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,SAAqB,GAAG,CAAC,CAAC;IAEhC,IAAI,CAACZ,QAAQ,CAACE,IAAI,CAACW,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACV,IAAI,GAAG,kBAAkB;IACrC,CAAC,MAAM,IAAIF,QAAQ,CAACE,IAAI,CAACW,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1CF,SAAS,CAACV,IAAI,GAAG,yCAAyC;IAC5D;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,CAACU,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACT,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAAC,cAAc,CAACY,IAAI,CAACf,QAAQ,CAACG,KAAK,CAAC,EAAE;MAC/CS,SAAS,CAACT,KAAK,GAAG,kBAAkB;IACtC;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtBQ,SAAS,CAACR,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,CAACU,MAAM,GAAG,CAAC,EAAE;MACvCF,SAAS,CAACR,QAAQ,GAAG,6CAA6C;IACpE;IAEA,IAAI,CAACJ,QAAQ,CAACK,qBAAqB,EAAE;MACnCO,SAAS,CAACP,qBAAqB,GAAG,mCAAmC;IACvE,CAAC,MAAM,IAAIL,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,qBAAqB,EAAE;MAC/DO,SAAS,CAACP,qBAAqB,GAAG,wBAAwB;IAC5D;IAEA,IAAI,CAACG,UAAU,EAAE;MACfI,SAAS,CAACI,KAAK,GAAG,4CAA4C;IAChE;IAEAT,SAAS,CAACK,SAAS,CAAC;IACpB,OAAOK,MAAM,CAACC,IAAI,CAACN,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMK,iBAAiB,GAAIC,KAAyB,IAAMC,KAAa,IAAK;IAC1EpB,WAAW,CAACqB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;IAClD;IACA,IAAIf,MAAM,CAACc,KAAK,CAAC,EAAE;MACjBb,SAAS,CAACe,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAGG;MAAU,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACf,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACF,MAAMhB,QAAQ,CAACK,QAAQ,CAAC;MACxBN,QAAQ,CAAC,YAAY,EAAE;QAAEgB,OAAO,EAAE;MAAK,CAAC,CAAC;IAC3C,CAAC,CAAC,OAAOb,KAAK,EAAE;MACd;IAAA;EAEJ,CAAC;EAED,oBACEN,OAAA;IAAKoC,SAAS,EAAC,qFAAqF;IAAAC,QAAA,eAClGrC,OAAA;MAAKoC,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBACxCrC,OAAA;QAAAqC,QAAA,gBACErC,OAAA;UAAKoC,SAAS,EAAC,gFAAgF;UAAAC,QAAA,eAC7FrC,OAAA;YACEoC,SAAS,EAAC,0BAA0B;YACpCE,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,OAAO,EAAC,WAAW;YAAAH,QAAA,eAEnBrC,OAAA;cACEyC,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAAsH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhD,OAAA;UAAIoC,SAAS,EAAC,wDAAwD;UAAAC,QAAA,EAAC;QAEvE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhD,OAAA;UAAGoC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,GAAC,IAClD,EAAC,GAAG,eACNrC,OAAA,CAACN,IAAI;YACHuD,EAAE,EAAC,QAAQ;YACXb,SAAS,EAAC,qDAAqD;YAAAC,QAAA,EAChE;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENhD,OAAA;QAAMoC,SAAS,EAAC,gBAAgB;QAACc,QAAQ,EAAEjB,YAAa;QAAAI,QAAA,GACrD/B,KAAK,iBACJN,OAAA;UAAKoC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,eACvCrC,OAAA;YAAKoC,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAE/B;UAAK;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CACN,eAEDhD,OAAA;UAAKoC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBrC,OAAA,CAACF,KAAK;YACJqD,KAAK,EAAC,WAAW;YACjBC,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,sBAAsB;YAClCvB,KAAK,EAAErB,QAAQ,CAACE,IAAK;YACrB2C,QAAQ,EAAE1B,iBAAiB,CAAC,MAAM,CAAE;YACpCtB,KAAK,EAAES,MAAM,CAACJ,IAAK;YACnB4C,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFhD,OAAA,CAACF,KAAK;YACJqD,KAAK,EAAC,eAAe;YACrBC,IAAI,EAAC,OAAO;YACZC,WAAW,EAAC,kBAAkB;YAC9BvB,KAAK,EAAErB,QAAQ,CAACG,KAAM;YACtB0C,QAAQ,EAAE1B,iBAAiB,CAAC,OAAO,CAAE;YACrCtB,KAAK,EAAES,MAAM,CAACH,KAAM;YACpB2C,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFhD,OAAA,CAACF,KAAK;YACJqD,KAAK,EAAC,UAAU;YAChBC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,qBAAqB;YACjCvB,KAAK,EAAErB,QAAQ,CAACI,QAAS;YACzByC,QAAQ,EAAE1B,iBAAiB,CAAC,UAAU,CAAE;YACxCtB,KAAK,EAAES,MAAM,CAACF,QAAS;YACvB0C,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFhD,OAAA,CAACF,KAAK;YACJqD,KAAK,EAAC,kBAAkB;YACxBC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,uBAAuB;YACnCvB,KAAK,EAAErB,QAAQ,CAACK,qBAAsB;YACtCwC,QAAQ,EAAE1B,iBAAiB,CAAC,uBAAuB,CAAE;YACrDtB,KAAK,EAAES,MAAM,CAACD,qBAAsB;YACpCyC,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENhD,OAAA;UAAKoC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCrC,OAAA;YACEwD,EAAE,EAAC,aAAa;YAChB7C,IAAI,EAAC,aAAa;YAClByC,IAAI,EAAC,UAAU;YACfhB,SAAS,EAAC,yEAAyE;YACnFqB,OAAO,EAAExC,UAAW;YACpBqC,QAAQ,EAAGpB,CAAC,IAAK;cACfhB,aAAa,CAACgB,CAAC,CAACwB,MAAM,CAACD,OAAO,CAAC;cAC/B,IAAI1C,MAAM,CAACU,KAAK,EAAE;gBAChBT,SAAS,CAACe,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEN,KAAK,EAAEO;gBAAU,CAAC,CAAC,CAAC;cACpD;YACF;UAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFhD,OAAA;YAAO2D,OAAO,EAAC,aAAa;YAACvB,SAAS,EAAC,kCAAkC;YAAAC,QAAA,GAAC,gBAC1D,EAAC,GAAG,eAClBrC,OAAA;cACEoD,IAAI,EAAC,QAAQ;cACbhB,SAAS,EAAC,uFAAuF;cACjGwB,OAAO,EAAEA,CAAA,KAAMC,KAAK,CAAC,+BAA+B,CAAE;cAAAxB,QAAA,EACvD;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAAC,GAAG,EAAC,KACX,EAAC,GAAG,eACPhD,OAAA;cACEoD,IAAI,EAAC,QAAQ;cACbhB,SAAS,EAAC,uFAAuF;cACjGwB,OAAO,EAAEA,CAAA,KAAMC,KAAK,CAAC,6BAA6B,CAAE;cAAAxB,QAAA,EACrD;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACLjC,MAAM,CAACU,KAAK,iBACXzB,OAAA;UAAGoC,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAEtB,MAAM,CAACU;QAAK;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CACtD,eAEDhD,OAAA;UAAAqC,QAAA,eACErC,OAAA,CAACH,MAAM;YACLuD,IAAI,EAAC,QAAQ;YACbhB,SAAS,EAAC,QAAQ;YAClB0B,OAAO,EAAEzD,SAAU;YACnB0D,QAAQ,EAAE1D,SAAU;YAAAgC,QAAA,EACrB;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9C,EAAA,CAzNID,QAAkB;EAAA,QACLN,WAAW,EACwCC,OAAO;AAAA;AAAAoE,EAAA,GAFvE/D,QAAkB;AA2NxB,eAAeA,QAAQ;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}