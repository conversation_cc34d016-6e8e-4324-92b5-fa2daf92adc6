import { Request, Response } from 'express';
import { User } from '../models/User';
import { AuthenticatedRequest, AppError, ErrorType, HttpStatus } from '../types';
import { generateToken } from '../utils/jwt';
import { sendSuccess, sendError, sendCreated, sendConflict, sendNotFound } from '../utils/response';
import { asyncHandler } from '../middleware/errorHandler';

/**
 * Register a new user
 */
export const register = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { name, email, password } = req.body;

  // Check if user already exists
  const existingUser = await User.findByEmail(email);
  if (existingUser) {
    sendConflict(res, 'User with this email already exists');
    return;
  }

  // Create new user
  const user = await User.create({
    name,
    email,
    password,
  });

  // Generate JWT token
  const token = generateToken({
    userId: user.id,
    email: user.email,
  });

  sendCreated(res, 'User registered successfully', {
    user: user.toJSON(),
    token,
  });
});

/**
 * Login user
 */
export const login = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { email, password } = req.body;

  // Find user by email
  const user = await User.findByEmail(email);
  if (!user) {
    sendError(res, 'Invalid email or password', HttpStatus.UNAUTHORIZED);
    return;
  }

  // Check password
  const isPasswordValid = await user.comparePassword(password);
  if (!isPasswordValid) {
    sendError(res, 'Invalid email or password', HttpStatus.UNAUTHORIZED);
    return;
  }

  // Generate JWT token
  const token = generateToken({
    userId: user.id,
    email: user.email,
  });

  sendSuccess(res, 'Login successful', {
    user: user.toJSON(),
    token,
  });
});

/**
 * Get current user profile
 */
export const getProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  sendSuccess(res, 'Profile retrieved successfully', {
    user: req.user.toJSON(),
  });
});

/**
 * Update user profile
 */
export const updateProfile = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  const { name, profilePicture } = req.body;
  const updateData: Partial<{ name: string; profilePicture: string }> = {};

  if (name !== undefined) updateData.name = name;
  if (profilePicture !== undefined) updateData.profilePicture = profilePicture;

  await req.user.update(updateData);

  sendSuccess(res, 'Profile updated successfully', {
    user: req.user.toJSON(),
  });
});

/**
 * Change password
 */
export const changePassword = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  const { currentPassword, newPassword } = req.body;

  // Verify current password
  const isCurrentPasswordValid = await req.user.comparePassword(currentPassword);
  if (!isCurrentPasswordValid) {
    sendError(res, 'Current password is incorrect', HttpStatus.BAD_REQUEST);
    return;
  }

  // Update password
  await req.user.update({ password: newPassword });

  sendSuccess(res, 'Password changed successfully');
});

/**
 * Delete user account
 */
export const deleteAccount = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  const { password } = req.body;

  // Verify password before deletion
  const isPasswordValid = await req.user.comparePassword(password);
  if (!isPasswordValid) {
    sendError(res, 'Password is incorrect', HttpStatus.BAD_REQUEST);
    return;
  }

  // Delete user account (this will cascade delete all CVs)
  await req.user.destroy();

  sendSuccess(res, 'Account deleted successfully');
});

/**
 * Refresh token
 */
export const refreshToken = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  // Generate new JWT token
  const token = generateToken({
    userId: req.user.id,
    email: req.user.email,
  });

  sendSuccess(res, 'Token refreshed successfully', {
    token,
  });
});

/**
 * Logout user (client-side token removal)
 */
export const logout = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  // Since we're using stateless JWT tokens, logout is handled client-side
  // This endpoint exists for consistency and potential future token blacklisting
  sendSuccess(res, 'Logout successful');
});

export default {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword,
  deleteAccount,
  refreshToken,
  logout,
};
