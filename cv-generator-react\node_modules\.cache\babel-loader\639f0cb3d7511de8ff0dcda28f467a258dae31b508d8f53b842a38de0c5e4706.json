{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\cv-generator-react\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport apiService from '../services/api';\n\n// Auth state interface\n\n// Auth actions\n\n// Auth context interface\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Initial state\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null\n};\n\n// Auth reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case 'AUTH_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: action.payload\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n// Auth provider component\n\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialize auth state from localStorage\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n      const userStr = localStorage.getItem('user');\n      if (token && userStr) {\n        try {\n          // Verify token is still valid by fetching profile\n          const currentUser = await apiService.getProfile();\n          dispatch({\n            type: 'AUTH_SUCCESS',\n            payload: {\n              user: currentUser,\n              token\n            }\n          });\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          dispatch({\n            type: 'LOGOUT'\n          });\n        }\n      }\n    };\n    initializeAuth();\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      const response = await apiService.login(credentials);\n\n      // Store in localStorage\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: {\n          user: response.user,\n          token: response.token\n        }\n      });\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed';\n      dispatch({\n        type: 'AUTH_FAILURE',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Register function\n  const register = async data => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      const response = await apiService.register(data);\n\n      // Store in localStorage\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: {\n          user: response.user,\n          token: response.token\n        }\n      });\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Registration failed';\n      dispatch({\n        type: 'AUTH_FAILURE',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await apiService.logout();\n    } catch (error) {\n      // Even if logout fails on server, clear local state\n      console.error('Logout error:', error);\n    } finally {\n      // Clear localStorage\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      dispatch({\n        type: 'LOGOUT'\n      });\n    }\n  };\n\n  // Update profile function\n  const updateProfile = async data => {\n    try {\n      const updatedUser = await apiService.updateProfile(data);\n\n      // Update localStorage\n      localStorage.setItem('user', JSON.stringify(updatedUser));\n      dispatch({\n        type: 'UPDATE_USER',\n        payload: updatedUser\n      });\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Profile update failed';\n      dispatch({\n        type: 'AUTH_FAILURE',\n        payload: errorMessage\n      });\n      throw error;\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    dispatch({\n      type: 'CLEAR_ERROR'\n    });\n  };\n\n  // Context value\n  const value = {\n    user: state.user,\n    token: state.token,\n    isAuthenticated: state.isAuthenticated,\n    isLoading: state.isLoading,\n    error: state.error,\n    login,\n    register,\n    logout,\n    updateProfile,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 231,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "apiService", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "isAuthenticated", "isLoading", "error", "authReducer", "state", "action", "type", "payload", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "initializeAuth", "localStorage", "getItem", "userStr", "currentUser", "getProfile", "removeItem", "login", "credentials", "response", "setItem", "JSON", "stringify", "_error$response", "_error$response$data", "errorMessage", "data", "message", "register", "_error$response2", "_error$response2$data", "logout", "console", "updateProfile", "updatedUser", "_error$response3", "_error$response3$data", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/cv-generator-react/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';\nimport { User, LoginCredentials, RegisterData } from '../types';\nimport apiService from '../services/api';\n\n// Auth state interface\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n}\n\n// Auth actions\ntype AuthAction =\n  | { type: 'AUTH_START' }\n  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string } }\n  | { type: 'AUTH_FAILURE'; payload: string }\n  | { type: 'LOGOUT' }\n  | { type: 'UPDATE_USER'; payload: User }\n  | { type: 'CLEAR_ERROR' };\n\n// Auth context interface\ninterface AuthContextType {\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n  login: (credentials: LoginCredentials) => Promise<void>;\n  register: (data: RegisterData) => Promise<void>;\n  logout: () => Promise<void>;\n  updateProfile: (data: Partial<User>) => Promise<void>;\n  clearError: () => void;\n}\n\n// Initial state\nconst initialState: AuthState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n};\n\n// Auth reducer\nconst authReducer = (state: AuthState, action: AuthAction): AuthState => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n    case 'AUTH_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: action.payload,\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null,\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Auth provider component\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialize auth state from localStorage\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n      const userStr = localStorage.getItem('user');\n\n      if (token && userStr) {\n        try {\n          // Verify token is still valid by fetching profile\n          const currentUser = await apiService.getProfile();\n          \n          dispatch({\n            type: 'AUTH_SUCCESS',\n            payload: { user: currentUser, token },\n          });\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          dispatch({ type: 'LOGOUT' });\n        }\n      }\n    };\n\n    initializeAuth();\n  }, []);\n\n  // Login function\n  const login = async (credentials: LoginCredentials): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      const response = await apiService.login(credentials);\n      \n      // Store in localStorage\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      \n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: { user: response.user, token: response.token },\n      });\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.message || 'Login failed';\n      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Register function\n  const register = async (data: RegisterData): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      const response = await apiService.register(data);\n      \n      // Store in localStorage\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      \n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: { user: response.user, token: response.token },\n      });\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.message || 'Registration failed';\n      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async (): Promise<void> => {\n    try {\n      await apiService.logout();\n    } catch (error) {\n      // Even if logout fails on server, clear local state\n      console.error('Logout error:', error);\n    } finally {\n      // Clear localStorage\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      \n      dispatch({ type: 'LOGOUT' });\n    }\n  };\n\n  // Update profile function\n  const updateProfile = async (data: Partial<User>): Promise<void> => {\n    try {\n      const updatedUser = await apiService.updateProfile(data);\n      \n      // Update localStorage\n      localStorage.setItem('user', JSON.stringify(updatedUser));\n      \n      dispatch({ type: 'UPDATE_USER', payload: updatedUser });\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.message || 'Profile update failed';\n      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });\n      throw error;\n    }\n  };\n\n  // Clear error function\n  const clearError = (): void => {\n    dispatch({ type: 'CLEAR_ERROR' });\n  };\n\n  // Context value\n  const value: AuthContextType = {\n    user: state.user,\n    token: state.token,\n    isAuthenticated: state.isAuthenticated,\n    isLoading: state.isLoading,\n    error: state.error,\n    login,\n    register,\n    logout,\n    updateProfile,\n    clearError,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAmB,OAAO;AAE1F,OAAOC,UAAU,MAAM,iBAAiB;;AAExC;;AASA;;AASA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAcA;AACA,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAgB,EAAEC,MAAkB,KAAgB;EACvE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,YAAY;MACf,OAAO;QACL,GAAGF,KAAK;QACRH,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAEO,MAAM,CAACE,OAAO,CAACT,IAAI;QACzBC,KAAK,EAAEM,MAAM,CAACE,OAAO,CAACR,KAAK;QAC3BC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEG,MAAM,CAACE;MAChB,CAAC;IACH,KAAK,QAAQ;MACX,OAAO;QACL,GAAGH,KAAK;QACRN,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAEO,MAAM,CAACE;MACf,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRF,KAAK,EAAE;MACT,CAAC;IACH;MACE,OAAOE,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAGlB,aAAa,CAA8BmB,SAAS,CAAC;;AAEzE;;AAKA,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACR,KAAK,EAAES,QAAQ,CAAC,GAAGrB,UAAU,CAACW,WAAW,EAAEN,YAAY,CAAC;;EAE/D;EACAJ,SAAS,CAAC,MAAM;IACd,MAAMqB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAMf,KAAK,GAAGgB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE5C,IAAIjB,KAAK,IAAIkB,OAAO,EAAE;QACpB,IAAI;UACF;UACA,MAAMC,WAAW,GAAG,MAAMxB,UAAU,CAACyB,UAAU,CAAC,CAAC;UAEjDN,QAAQ,CAAC;YACPP,IAAI,EAAE,cAAc;YACpBC,OAAO,EAAE;cAAET,IAAI,EAAEoB,WAAW;cAAEnB;YAAM;UACtC,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;UACd;UACAa,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;UAChCL,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;UAC/BP,QAAQ,CAAC;YAAEP,IAAI,EAAE;UAAS,CAAC,CAAC;QAC9B;MACF;IACF,CAAC;IAEDQ,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,KAAK,GAAG,MAAOC,WAA6B,IAAoB;IACpE,IAAI;MACFT,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMiB,QAAQ,GAAG,MAAM7B,UAAU,CAAC2B,KAAK,CAACC,WAAW,CAAC;;MAEpD;MACAP,YAAY,CAACS,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACxB,KAAK,CAAC;MAC7CgB,YAAY,CAACS,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACH,QAAQ,CAACzB,IAAI,CAAC,CAAC;MAE3De,QAAQ,CAAC;QACPP,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;UAAET,IAAI,EAAEyB,QAAQ,CAACzB,IAAI;UAAEC,KAAK,EAAEwB,QAAQ,CAACxB;QAAM;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOG,KAAU,EAAE;MAAA,IAAAyB,eAAA,EAAAC,oBAAA;MACnB,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAAzB,KAAK,CAACqB,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBG,OAAO,KAAI,cAAc;MACpElB,QAAQ,CAAC;QAAEP,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAEsB;MAAa,CAAC,CAAC;MACzD,MAAM3B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM8B,QAAQ,GAAG,MAAOF,IAAkB,IAAoB;IAC5D,IAAI;MACFjB,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAMiB,QAAQ,GAAG,MAAM7B,UAAU,CAACsC,QAAQ,CAACF,IAAI,CAAC;;MAEhD;MACAf,YAAY,CAACS,OAAO,CAAC,OAAO,EAAED,QAAQ,CAACxB,KAAK,CAAC;MAC7CgB,YAAY,CAACS,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACH,QAAQ,CAACzB,IAAI,CAAC,CAAC;MAE3De,QAAQ,CAAC;QACPP,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;UAAET,IAAI,EAAEyB,QAAQ,CAACzB,IAAI;UAAEC,KAAK,EAAEwB,QAAQ,CAACxB;QAAM;MACxD,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOG,KAAU,EAAE;MAAA,IAAA+B,gBAAA,EAAAC,qBAAA;MACnB,MAAML,YAAY,GAAG,EAAAI,gBAAA,GAAA/B,KAAK,CAACqB,QAAQ,cAAAU,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBH,IAAI,cAAAI,qBAAA,uBAApBA,qBAAA,CAAsBH,OAAO,KAAI,qBAAqB;MAC3ElB,QAAQ,CAAC;QAAEP,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAEsB;MAAa,CAAC,CAAC;MACzD,MAAM3B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMiC,MAAM,GAAG,MAAAA,CAAA,KAA2B;IACxC,IAAI;MACF,MAAMzC,UAAU,CAACyC,MAAM,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACd;MACAkC,OAAO,CAAClC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR;MACAa,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;MAChCL,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;MAE/BP,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAS,CAAC,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAM+B,aAAa,GAAG,MAAOP,IAAmB,IAAoB;IAClE,IAAI;MACF,MAAMQ,WAAW,GAAG,MAAM5C,UAAU,CAAC2C,aAAa,CAACP,IAAI,CAAC;;MAExD;MACAf,YAAY,CAACS,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACY,WAAW,CAAC,CAAC;MAEzDzB,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE+B;MAAY,CAAC,CAAC;IACzD,CAAC,CAAC,OAAOpC,KAAU,EAAE;MAAA,IAAAqC,gBAAA,EAAAC,qBAAA;MACnB,MAAMX,YAAY,GAAG,EAAAU,gBAAA,GAAArC,KAAK,CAACqB,QAAQ,cAAAgB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBT,IAAI,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBT,OAAO,KAAI,uBAAuB;MAC7ElB,QAAQ,CAAC;QAAEP,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAEsB;MAAa,CAAC,CAAC;MACzD,MAAM3B,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMuC,UAAU,GAAGA,CAAA,KAAY;IAC7B5B,QAAQ,CAAC;MAAEP,IAAI,EAAE;IAAc,CAAC,CAAC;EACnC,CAAC;;EAED;EACA,MAAMoC,KAAsB,GAAG;IAC7B5C,IAAI,EAAEM,KAAK,CAACN,IAAI;IAChBC,KAAK,EAAEK,KAAK,CAACL,KAAK;IAClBC,eAAe,EAAEI,KAAK,CAACJ,eAAe;IACtCC,SAAS,EAAEG,KAAK,CAACH,SAAS;IAC1BC,KAAK,EAAEE,KAAK,CAACF,KAAK;IAClBmB,KAAK;IACLW,QAAQ;IACRG,MAAM;IACNE,aAAa;IACbI;EACF,CAAC;EAED,oBACE7C,OAAA,CAACY,WAAW,CAACmC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA/B,QAAA,EAChCA;EAAQ;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAAnC,EAAA,CApIaF,YAAyC;AAAAsC,EAAA,GAAzCtC,YAAyC;AAqItD,OAAO,MAAMuC,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAG5D,UAAU,CAACiB,WAAW,CAAC;EACvC,IAAI2C,OAAO,KAAK1C,SAAS,EAAE;IACzB,MAAM,IAAI2C,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAezC,WAAW;AAAC,IAAAwC,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}