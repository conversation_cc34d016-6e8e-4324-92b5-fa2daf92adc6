import { Response, NextFunction } from 'express';
import { User } from '../models/User';
import { AuthenticatedRequest, AppError, ErrorType, HttpStatus } from '../types';
import { verifyToken, extractTokenFromHeader } from '../utils/jwt';
import { sendUnauthorized } from '../utils/response';

/**
 * Authentication middleware
 * Verifies JWT token and attaches user to request
 */
export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extract token from Authorization header
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      sendUnauthorized(res, 'Access token is required');
      return;
    }

    // Verify token
    const decoded = verifyToken(token);
    
    // Find user by ID
    const user = await User.findByPk(decoded.userId);
    
    if (!user) {
      sendUnauthorized(res, 'User not found');
      return;
    }

    // Attach user to request
    req.user = user;
    next();
  } catch (error) {
    if (error instanceof Error) {
      sendUnauthorized(res, error.message);
    } else {
      sendUnauthorized(res, 'Invalid token');
    }
  }
};

/**
 * Optional authentication middleware
 * Attaches user to request if token is valid, but doesn't require it
 */
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (token) {
      const decoded = verifyToken(token);
      const user = await User.findByPk(decoded.userId);
      
      if (user) {
        req.user = user;
      }
    }
    
    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};

/**
 * Check if user owns the resource
 */
export const checkOwnership = (resourceUserIdField: string = 'userId') => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      sendUnauthorized(res, 'Authentication required');
      return;
    }

    const resourceUserId = req.params[resourceUserIdField] || req.body[resourceUserIdField];
    
    if (resourceUserId && parseInt(resourceUserId) !== req.user.id) {
      res.status(HttpStatus.FORBIDDEN).json({
        success: false,
        message: 'Access forbidden: You can only access your own resources',
      });
      return;
    }

    next();
  };
};

/**
 * Require email verification
 */
export const requireEmailVerification = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    sendUnauthorized(res, 'Authentication required');
    return;
  }

  if (!req.user.emailVerified) {
    res.status(HttpStatus.FORBIDDEN).json({
      success: false,
      message: 'Email verification required',
      error: 'EMAIL_NOT_VERIFIED',
    });
    return;
  }

  next();
};

export default {
  authenticate,
  optionalAuth,
  checkOwnership,
  requireEmailVerification,
};
