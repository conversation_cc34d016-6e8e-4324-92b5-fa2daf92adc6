{"version": 3, "file": "CallbackRegistry.js", "sourceRoot": "", "sources": ["../../../../src/common/CallbackRegistry.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,qDAA6C;AAC7C,uDAAkD;AAElD,2CAA4D;AAC5D,uCAAqC;AAErC;;;;GAIG;AACH,MAAa,gBAAgB;IAC3B,UAAU,GAAG,IAAI,GAAG,EAAoB,CAAC;IACzC,YAAY,GAAG,4BAA4B,EAAE,CAAC;IAE9C,MAAM,CACJ,KAAa,EACb,OAA2B,EAC3B,OAA6B;QAE7B,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QACnE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAAC;YACH,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sEAAsE;YACtE,YAAY;YACZ,QAAQ,CAAC,OAAO;iBACb,YAAY,EAAE;iBACd,KAAK,CAAC,oBAAU,CAAC;iBACjB,OAAO,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;YACL,QAAQ,CAAC,MAAM,CAAC,KAAc,CAAC,CAAC;YAChC,MAAM,KAAK,CAAC;QACd,CAAC;QACD,0CAA0C;QAC1C,OAAO,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE;YAClD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,EAAU,EAAE,OAAe,EAAE,eAAwB;QAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;IACnD,CAAC;IAED,OAAO,CACL,QAAkB,EAClB,YAAoC,EACpC,eAAwB;QAExB,IAAI,KAAoB,CAAC;QACzB,IAAI,OAAe,CAAC;QACpB,IAAI,YAAY,YAAY,yBAAa,EAAE,CAAC;YAC1C,KAAK,GAAG,YAAY,CAAC;YACrB,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;YAC7B,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;YACvB,OAAO,GAAG,YAAY,CAAC;QACzB,CAAC;QAED,QAAQ,CAAC,MAAM,CACb,IAAA,2BAAY,EACV,KAAK,EACL,mBAAmB,QAAQ,CAAC,KAAK,MAAM,OAAO,EAAE,EAChD,eAAe,CAChB,CACF,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,EAAU,EAAE,KAAc;QAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACzC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QACD,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK;QACH,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YAChD,yDAAyD;YACzD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,4BAAgB,CAAC,eAAe,CAAC,CAAC,CAAC;QAChE,CAAC;QACD,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,MAAM,MAAM,GAAY,EAAE,CAAC;QAC3B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YAChD,MAAM,CAAC,IAAI,CACT,IAAI,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,sBAAsB,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CACzE,CAAC;QACJ,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AA5FD,4CA4FC;AACD;;GAEG;AAEH,MAAa,QAAQ;IACnB,GAAG,CAAS;IACZ,MAAM,GAAG,IAAI,yBAAa,EAAE,CAAC;IAC7B,SAAS,GAAG,sBAAQ,CAAC,MAAM,EAAW,CAAC;IACvC,MAAM,CAAiC;IACvC,MAAM,CAAS;IAEf,YAAY,EAAU,EAAE,KAAa,EAAE,OAAgB;QACrD,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC5B,IAAI,CAAC,SAAS,CAAC,MAAM,CACnB,IAAA,2BAAY,EACV,IAAI,CAAC,MAAM,EACX,GAAG,KAAK,4GAA4G,CACrH,CACF,CAAC;YACJ,CAAC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,CAAC,KAAc;QACpB,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,KAAY;QACjB,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AA/CD,4BA+CC;AAED;;GAEG;AACH,SAAgB,4BAA4B;IAC1C,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,OAAO,GAAW,EAAE;QAClB,OAAO,EAAE,EAAE,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AALD,oEAKC"}