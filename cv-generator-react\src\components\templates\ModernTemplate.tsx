import React from 'react';
import { CVData } from '../../types';

interface ModernTemplateProps {
  data: CVData;
}

const ModernTemplate: React.FC<ModernTemplateProps> = ({ data }) => {
  const { personal_info, education, experience, skills, projects, languages } = data;

  return (
    <div className="max-w-4xl mx-auto bg-white shadow-lg print:shadow-none">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="mb-4 md:mb-0">
            <h1 className="text-3xl md:text-4xl font-bold mb-2">
              {personal_info.first_name} {personal_info.last_name}
            </h1>
            <div className="flex flex-wrap gap-4 text-blue-100">
              {personal_info.email && (
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                  {personal_info.email}
                </div>
              )}
              {personal_info.phone && (
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                  {personal_info.phone}
                </div>
              )}
              {personal_info.address && (
                <div className="flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  {personal_info.address}
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-col space-y-2">
            {personal_info.website && (
              <a href={personal_info.website} className="text-blue-100 hover:text-white transition-colors">
                🌐 Website
              </a>
            )}
            {personal_info.linkedin && (
              <a href={personal_info.linkedin} className="text-blue-100 hover:text-white transition-colors">
                💼 LinkedIn
              </a>
            )}
            {personal_info.github && (
              <a href={personal_info.github} className="text-blue-100 hover:text-white transition-colors">
                💻 GitHub
              </a>
            )}
          </div>
        </div>
      </div>

      <div className="p-8">
        {/* Professional Summary */}
        {personal_info.summary && (
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-2">
              Professional Summary
            </h2>
            <p className="text-gray-700 leading-relaxed">{personal_info.summary}</p>
          </section>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Experience */}
            {experience.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-2">
                  Work Experience
                </h2>
                <div className="space-y-6">
                  {experience.map((exp, index) => (
                    <div key={exp.id} className="relative pl-6 border-l-2 border-blue-200">
                      <div className="absolute w-3 h-3 bg-blue-600 rounded-full -left-2 top-1"></div>
                      <div className="mb-2">
                        <h3 className="text-lg font-semibold text-gray-800">{exp.position}</h3>
                        <p className="text-blue-600 font-medium">{exp.company}</p>
                        <p className="text-sm text-gray-600">
                          {exp.start_date} - {exp.current ? 'Present' : exp.end_date}
                        </p>
                      </div>
                      {exp.description && (
                        <p className="text-gray-700 leading-relaxed">{exp.description}</p>
                      )}
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Education */}
            {education.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-2">
                  Education
                </h2>
                <div className="space-y-4">
                  {education.map((edu, index) => (
                    <div key={edu.id} className="relative pl-6 border-l-2 border-blue-200">
                      <div className="absolute w-3 h-3 bg-blue-600 rounded-full -left-2 top-1"></div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-800">{edu.degree}</h3>
                        <p className="text-blue-600 font-medium">{edu.institution}</p>
                        <p className="text-sm text-gray-600">
                          {edu.field_of_study} • {edu.start_date} - {edu.current ? 'Present' : edu.end_date}
                          {edu.gpa && ` • GPA: ${edu.gpa}`}
                        </p>
                        {edu.description && (
                          <p className="text-gray-700 mt-2">{edu.description}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Projects */}
            {projects.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-2">
                  Projects
                </h2>
                <div className="space-y-4">
                  {projects.map((project, index) => (
                    <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="text-lg font-semibold text-gray-800">{project.name}</h3>
                        <div className="flex space-x-2">
                          {project.url && (
                            <a href={project.url} className="text-blue-600 hover:text-blue-800">
                              🔗
                            </a>
                          )}
                          {project.github_url && (
                            <a href={project.github_url} className="text-blue-600 hover:text-blue-800">
                              💻
                            </a>
                          )}
                        </div>
                      </div>
                      <p className="text-gray-700 mb-2">{project.description}</p>
                      {project.technologies.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {project.technologies.map((tech, techIndex) => (
                            <span
                              key={techIndex}
                              className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                            >
                              {tech}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </section>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Skills */}
            {skills.length > 0 && (
              <section>
                <h2 className="text-xl font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-2">
                  Skills
                </h2>
                <div className="space-y-3">
                  {skills.map((skill, index) => (
                    <div key={skill.id}>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm font-medium text-gray-700">{skill.name}</span>
                        <span className="text-xs text-gray-500">{skill.level}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{
                            width: skill.level === 'Expert' ? '100%' : 
                                   skill.level === 'Advanced' ? '80%' : 
                                   skill.level === 'Intermediate' ? '60%' : '40%'
                          }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Languages */}
            {languages.length > 0 && (
              <section>
                <h2 className="text-xl font-bold text-gray-800 mb-4 border-b-2 border-blue-600 pb-2">
                  Languages
                </h2>
                <div className="space-y-2">
                  {languages.map((language, index) => (
                    <div key={language.id} className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-700">{language.name}</span>
                      <span className="text-xs text-gray-500">{language.proficiency}</span>
                    </div>
                  ))}
                </div>
              </section>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernTemplate;
