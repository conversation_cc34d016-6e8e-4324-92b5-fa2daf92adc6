import React from 'react';
import { ButtonProps } from '../../types';

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  onClick,
  type = 'button',
  className = '',
}) => {
  const getButtonClasses = () => {
    let classes = 'btn';

    // Add variant classes
    switch (variant) {
      case 'primary':
        classes += ' btn-primary';
        break;
      case 'secondary':
        classes += ' btn-secondary';
        break;
      case 'outline':
        classes += ' btn-outline';
        break;
      case 'ghost':
        classes += ' btn-ghost';
        break;
      case 'danger':
        classes += ' btn-danger';
        break;
      default:
        classes += ' btn-primary';
    }

    // Add size classes
    switch (size) {
      case 'sm':
        classes += ' btn-sm';
        break;
      case 'md':
        classes += ' btn-md';
        break;
      case 'lg':
        classes += ' btn-lg';
        break;
      default:
        classes += ' btn-md';
    }

    return `${classes} ${className}`;
  };

  return (
    <button
      type={type}
      className={getButtonClasses()}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading && (
        <svg
          className="loading-spinner mr-2"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          style={{ width: '16px', height: '16px' }}
        >
          <circle
            style={{ opacity: 0.25 }}
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            style={{ opacity: 0.75 }}
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      )}
      {children}
    </button>
  );
};

export default Button;
