<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $data['personal_info']['first_name'] }} {{ $data['personal_info']['last_name'] }} - CV</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            color: #333;
            font-size: 14px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }
        
        .header {
            border-bottom: 4px solid #333;
            padding: 40px 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 30px;
            font-size: 13px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
        }
        
        .contact-label {
            font-weight: bold;
            margin-right: 5px;
        }
        
        .links {
            margin-top: 15px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        
        .links a {
            color: #333;
            text-decoration: underline;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        
        .summary {
            text-align: justify;
            line-height: 1.7;
        }
        
        .experience-item, .education-item {
            margin-bottom: 20px;
            padding-bottom: 15px;
        }
        
        .experience-item:not(:last-child), .education-item:not(:last-child) {
            border-bottom: 1px solid #eee;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        
        .item-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .item-company {
            color: #666;
            font-weight: 600;
            margin-bottom: 3px;
        }
        
        .item-date {
            color: #666;
            font-size: 12px;
            font-weight: 600;
            text-align: right;
        }
        
        .item-description {
            text-align: justify;
            line-height: 1.6;
            margin-top: 8px;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        
        .skill-category {
            margin-bottom: 15px;
        }
        
        .skill-category-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .skill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .skill-name {
            font-size: 13px;
        }
        
        .skill-level {
            background: #f0f0f0;
            color: #666;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
        }
        
        .projects-grid {
            display: grid;
            gap: 15px;
        }
        
        .project-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
        }
        
        .project-item:not(:last-child) {
            border-bottom: 1px solid #eee;
        }
        
        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        
        .project-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .project-links {
            display: flex;
            gap: 10px;
        }
        
        .project-links a {
            color: #666;
            text-decoration: underline;
            font-size: 12px;
        }
        
        .project-description {
            text-align: justify;
            margin-bottom: 8px;
        }
        
        .project-tech {
            font-size: 12px;
        }
        
        .tech-label {
            font-weight: bold;
            color: #666;
        }
        
        .languages-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            text-align: center;
        }
        
        .language-item {
            text-align: center;
        }
        
        .language-name {
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .language-level {
            color: #666;
            font-size: 12px;
        }
        
        @media print {
            body { font-size: 12px; }
            .header { padding: 30px 20px; }
            .content { padding: 20px; }
            .section { margin-bottom: 20px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>{{ $data['personal_info']['first_name'] }} {{ $data['personal_info']['last_name'] }}</h1>
            <div class="contact-info">
                @if(!empty($data['personal_info']['email']))
                    <div class="contact-item">
                        <span class="contact-label">Email:</span>
                        <span>{{ $data['personal_info']['email'] }}</span>
                    </div>
                @endif
                @if(!empty($data['personal_info']['phone']))
                    <div class="contact-item">
                        <span class="contact-label">Phone:</span>
                        <span>{{ $data['personal_info']['phone'] }}</span>
                    </div>
                @endif
                @if(!empty($data['personal_info']['address']))
                    <div class="contact-item">
                        <span class="contact-label">Address:</span>
                        <span>{{ $data['personal_info']['address'] }}</span>
                    </div>
                @endif
            </div>
            @if(!empty($data['personal_info']['website']) || !empty($data['personal_info']['linkedin']) || !empty($data['personal_info']['github']))
                <div class="links">
                    @if(!empty($data['personal_info']['website']))
                        <a href="{{ $data['personal_info']['website'] }}">Website</a>
                    @endif
                    @if(!empty($data['personal_info']['linkedin']))
                        <a href="{{ $data['personal_info']['linkedin'] }}">LinkedIn</a>
                    @endif
                    @if(!empty($data['personal_info']['github']))
                        <a href="{{ $data['personal_info']['github'] }}">GitHub</a>
                    @endif
                </div>
            @endif
        </div>

        <div class="content">
            <!-- Professional Summary -->
            @if(!empty($data['personal_info']['summary']))
                <div class="section">
                    <h2 class="section-title">Professional Summary</h2>
                    <p class="summary">{{ $data['personal_info']['summary'] }}</p>
                </div>
            @endif

            <!-- Experience -->
            @if(!empty($data['experience']) && count($data['experience']) > 0)
                <div class="section">
                    <h2 class="section-title">Professional Experience</h2>
                    @foreach($data['experience'] as $exp)
                        <div class="experience-item">
                            <div class="item-header">
                                <div>
                                    <div class="item-title">{{ $exp['position'] }}</div>
                                    <div class="item-company">{{ $exp['company'] }}</div>
                                </div>
                                <div class="item-date">
                                    {{ $exp['start_date'] }} - {{ $exp['current'] ? 'Present' : $exp['end_date'] }}
                                </div>
                            </div>
                            @if(!empty($exp['description']))
                                <div class="item-description">{{ $exp['description'] }}</div>
                            @endif
                        </div>
                    @endforeach
                </div>
            @endif

            <!-- Education -->
            @if(!empty($data['education']) && count($data['education']) > 0)
                <div class="section">
                    <h2 class="section-title">Education</h2>
                    @foreach($data['education'] as $edu)
                        <div class="education-item">
                            <div class="item-header">
                                <div>
                                    <div class="item-title">{{ $edu['degree'] }}</div>
                                    <div class="item-company">{{ $edu['institution'] }}</div>
                                    <div style="font-size: 12px; color: #666;">
                                        {{ $edu['field_of_study'] }}
                                        @if(!empty($edu['gpa']))
                                            • GPA: {{ $edu['gpa'] }}
                                        @endif
                                    </div>
                                </div>
                                <div class="item-date">
                                    {{ $edu['start_date'] }} - {{ $edu['current'] ? 'Present' : $edu['end_date'] }}
                                </div>
                            </div>
                            @if(!empty($edu['description']))
                                <div class="item-description">{{ $edu['description'] }}</div>
                            @endif
                        </div>
                    @endforeach
                </div>
            @endif

            <!-- Skills -->
            @if(!empty($data['skills']) && count($data['skills']) > 0)
                <div class="section">
                    <h2 class="section-title">Skills</h2>
                    <div class="skills-grid">
                        @php
                            $skillsByCategory = [];
                            foreach($data['skills'] as $skill) {
                                $category = $skill['category'] ?: 'General';
                                $skillsByCategory[$category][] = $skill;
                            }
                        @endphp
                        @foreach($skillsByCategory as $category => $skills)
                            <div class="skill-category">
                                <div class="skill-category-title">{{ $category }}</div>
                                @foreach($skills as $skill)
                                    <div class="skill-item">
                                        <span class="skill-name">{{ $skill['name'] }}</span>
                                        <span class="skill-level">{{ $skill['level'] }}</span>
                                    </div>
                                @endforeach
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Projects -->
            @if(!empty($data['projects']) && count($data['projects']) > 0)
                <div class="section">
                    <h2 class="section-title">Projects</h2>
                    <div class="projects-grid">
                        @foreach($data['projects'] as $project)
                            <div class="project-item">
                                <div class="project-header">
                                    <div class="project-title">{{ $project['name'] }}</div>
                                    <div class="project-links">
                                        @if(!empty($project['url']))
                                            <a href="{{ $project['url'] }}">Live Demo</a>
                                        @endif
                                        @if(!empty($project['github_url']))
                                            <a href="{{ $project['github_url'] }}">Source Code</a>
                                        @endif
                                    </div>
                                </div>
                                <div class="project-description">{{ $project['description'] }}</div>
                                @if(!empty($project['technologies']) && count($project['technologies']) > 0)
                                    <div class="project-tech">
                                        <span class="tech-label">Technologies:</span>
                                        {{ implode(', ', $project['technologies']) }}
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Languages -->
            @if(!empty($data['languages']) && count($data['languages']) > 0)
                <div class="section">
                    <h2 class="section-title">Languages</h2>
                    <div class="languages-grid">
                        @foreach($data['languages'] as $language)
                            <div class="language-item">
                                <div class="language-name">{{ $language['name'] }}</div>
                                <div class="language-level">{{ $language['proficiency'] }}</div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</body>
</html>
