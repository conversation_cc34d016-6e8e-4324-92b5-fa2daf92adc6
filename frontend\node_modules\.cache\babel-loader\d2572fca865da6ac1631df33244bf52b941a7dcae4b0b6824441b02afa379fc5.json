{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI, handleApiError } from '../services/api';\nimport { toast } from 'react-hot-toast';\n\n// Auth state interface\n\n// Auth actions\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Initial state\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\n// Auth reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case 'AUTH_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: action.payload\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext(undefined);\n\n// Auth provider component\n\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialize auth state from localStorage\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n      const userStr = localStorage.getItem('user');\n      if (token && userStr) {\n        try {\n          // Verify token is still valid by fetching profile\n          const currentUser = await authAPI.getProfile();\n          dispatch({\n            type: 'AUTH_SUCCESS',\n            payload: {\n              user: currentUser,\n              token\n            }\n          });\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          dispatch({\n            type: 'LOGOUT'\n          });\n        }\n      } else {\n        dispatch({\n          type: 'LOGOUT'\n        });\n      }\n    };\n    initializeAuth();\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      const {\n        user,\n        token\n      } = await authAPI.login(credentials);\n\n      // Store in localStorage\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: {\n          user,\n          token\n        }\n      });\n      toast.success('Login successful!');\n    } catch (error) {\n      const errorMessage = handleApiError(error);\n      dispatch({\n        type: 'AUTH_FAILURE',\n        payload: errorMessage\n      });\n      toast.error(errorMessage);\n      throw error;\n    }\n  };\n\n  // Register function\n  const register = async data => {\n    try {\n      dispatch({\n        type: 'AUTH_START'\n      });\n      const {\n        user,\n        token\n      } = await authAPI.register(data);\n\n      // Store in localStorage\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: {\n          user,\n          token\n        }\n      });\n      toast.success('Registration successful!');\n    } catch (error) {\n      const errorMessage = handleApiError(error);\n      dispatch({\n        type: 'AUTH_FAILURE',\n        payload: errorMessage\n      });\n      toast.error(errorMessage);\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.error('Logout API error:', error);\n    } finally {\n      // Clear localStorage\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      dispatch({\n        type: 'LOGOUT'\n      });\n      toast.success('Logged out successfully');\n    }\n  };\n\n  // Update profile function\n  const updateProfile = async data => {\n    try {\n      const updatedUser = await authAPI.updateProfile(data);\n\n      // Update localStorage\n      localStorage.setItem('user', JSON.stringify(updatedUser));\n      dispatch({\n        type: 'UPDATE_USER',\n        payload: updatedUser\n      });\n      toast.success('Profile updated successfully!');\n    } catch (error) {\n      const errorMessage = handleApiError(error);\n      toast.error(errorMessage);\n      throw error;\n    }\n  };\n\n  // Context value\n  const value = {\n    user: state.user,\n    token: state.token,\n    isAuthenticated: state.isAuthenticated,\n    isLoading: state.isLoading,\n    login,\n    register,\n    logout,\n    updateProfile\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 221,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "authAPI", "handleApiError", "toast", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "isAuthenticated", "isLoading", "error", "authReducer", "state", "action", "type", "payload", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "initializeAuth", "localStorage", "getItem", "userStr", "currentUser", "getProfile", "removeItem", "login", "credentials", "setItem", "JSON", "stringify", "success", "errorMessage", "register", "data", "logout", "console", "updateProfile", "updatedUser", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';\nimport { User, LoginCredentials, RegisterData, AuthContextType } from '../types';\nimport { authAPI, handleApiError } from '../services/api';\nimport { toast } from 'react-hot-toast';\n\n// Auth state interface\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n}\n\n// Auth actions\ntype AuthAction =\n  | { type: 'AUTH_START' }\n  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string } }\n  | { type: 'AUTH_FAILURE'; payload: string }\n  | { type: 'LOGOUT' }\n  | { type: 'UPDATE_USER'; payload: User }\n  | { type: 'CLEAR_ERROR' };\n\n// Initial state\nconst initialState: AuthState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n};\n\n// Auth reducer\nconst authReducer = (state: AuthState, action: AuthAction): AuthState => {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n    case 'AUTH_FAILURE':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n    case 'UPDATE_USER':\n      return {\n        ...state,\n        user: action.payload,\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null,\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Auth provider component\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialize auth state from localStorage\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = localStorage.getItem('token');\n      const userStr = localStorage.getItem('user');\n\n      if (token && userStr) {\n        try {\n          // Verify token is still valid by fetching profile\n          const currentUser = await authAPI.getProfile();\n          \n          dispatch({\n            type: 'AUTH_SUCCESS',\n            payload: { user: currentUser, token },\n          });\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          dispatch({ type: 'LOGOUT' });\n        }\n      } else {\n        dispatch({ type: 'LOGOUT' });\n      }\n    };\n\n    initializeAuth();\n  }, []);\n\n  // Login function\n  const login = async (credentials: LoginCredentials): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      const { user, token } = await authAPI.login(credentials);\n      \n      // Store in localStorage\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      \n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: { user, token },\n      });\n      \n      toast.success('Login successful!');\n    } catch (error) {\n      const errorMessage = handleApiError(error);\n      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });\n      toast.error(errorMessage);\n      throw error;\n    }\n  };\n\n  // Register function\n  const register = async (data: RegisterData): Promise<void> => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n      \n      const { user, token } = await authAPI.register(data);\n      \n      // Store in localStorage\n      localStorage.setItem('token', token);\n      localStorage.setItem('user', JSON.stringify(user));\n      \n      dispatch({\n        type: 'AUTH_SUCCESS',\n        payload: { user, token },\n      });\n      \n      toast.success('Registration successful!');\n    } catch (error) {\n      const errorMessage = handleApiError(error);\n      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });\n      toast.error(errorMessage);\n      throw error;\n    }\n  };\n\n  // Logout function\n  const logout = async (): Promise<void> => {\n    try {\n      await authAPI.logout();\n    } catch (error) {\n      // Continue with logout even if API call fails\n      console.error('Logout API error:', error);\n    } finally {\n      // Clear localStorage\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      \n      dispatch({ type: 'LOGOUT' });\n      toast.success('Logged out successfully');\n    }\n  };\n\n  // Update profile function\n  const updateProfile = async (data: Partial<User>): Promise<void> => {\n    try {\n      const updatedUser = await authAPI.updateProfile(data);\n      \n      // Update localStorage\n      localStorage.setItem('user', JSON.stringify(updatedUser));\n      \n      dispatch({ type: 'UPDATE_USER', payload: updatedUser });\n      toast.success('Profile updated successfully!');\n    } catch (error) {\n      const errorMessage = handleApiError(error);\n      toast.error(errorMessage);\n      throw error;\n    }\n  };\n\n  // Context value\n  const value: AuthContextType = {\n    user: state.user,\n    token: state.token,\n    isAuthenticated: state.isAuthenticated,\n    isLoading: state.isLoading,\n    login,\n    register,\n    logout,\n    updateProfile,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = (): AuthContextType => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAmB,OAAO;AAE1F,SAASC,OAAO,EAAEC,cAAc,QAAQ,iBAAiB;AACzD,SAASC,KAAK,QAAQ,iBAAiB;;AAEvC;;AASA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AASA;AACA,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAgB,EAAEC,MAAkB,KAAgB;EACvE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,YAAY;MACf,OAAO;QACL,GAAGF,KAAK;QACRH,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAEO,MAAM,CAACE,OAAO,CAACT,IAAI;QACzBC,KAAK,EAAEM,MAAM,CAACE,OAAO,CAACR,KAAK;QAC3BC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,cAAc;MACjB,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAEG,MAAM,CAACE;MAChB,CAAC;IACH,KAAK,QAAQ;MACX,OAAO;QACL,GAAGH,KAAK;QACRN,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGE,KAAK;QACRN,IAAI,EAAEO,MAAM,CAACE;MACf,CAAC;IACH,KAAK,aAAa;MAChB,OAAO;QACL,GAAGH,KAAK;QACRF,KAAK,EAAE;MACT,CAAC;IACH;MACE,OAAOE,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAGpB,aAAa,CAA8BqB,SAAS,CAAC;;AAEzE;;AAKA,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACR,KAAK,EAAES,QAAQ,CAAC,GAAGvB,UAAU,CAACa,WAAW,EAAEN,YAAY,CAAC;;EAE/D;EACAN,SAAS,CAAC,MAAM;IACd,MAAMuB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAMf,KAAK,GAAGgB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,OAAO,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE5C,IAAIjB,KAAK,IAAIkB,OAAO,EAAE;QACpB,IAAI;UACF;UACA,MAAMC,WAAW,GAAG,MAAM1B,OAAO,CAAC2B,UAAU,CAAC,CAAC;UAE9CN,QAAQ,CAAC;YACPP,IAAI,EAAE,cAAc;YACpBC,OAAO,EAAE;cAAET,IAAI,EAAEoB,WAAW;cAAEnB;YAAM;UACtC,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;UACd;UACAa,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;UAChCL,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;UAC/BP,QAAQ,CAAC;YAAEP,IAAI,EAAE;UAAS,CAAC,CAAC;QAC9B;MACF,CAAC,MAAM;QACLO,QAAQ,CAAC;UAAEP,IAAI,EAAE;QAAS,CAAC,CAAC;MAC9B;IACF,CAAC;IAEDQ,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,KAAK,GAAG,MAAOC,WAA6B,IAAoB;IACpE,IAAI;MACFT,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAM;QAAER,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMP,OAAO,CAAC6B,KAAK,CAACC,WAAW,CAAC;;MAExD;MACAP,YAAY,CAACQ,OAAO,CAAC,OAAO,EAAExB,KAAK,CAAC;MACpCgB,YAAY,CAACQ,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC3B,IAAI,CAAC,CAAC;MAElDe,QAAQ,CAAC;QACPP,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;UAAET,IAAI;UAAEC;QAAM;MACzB,CAAC,CAAC;MAEFL,KAAK,CAACgC,OAAO,CAAC,mBAAmB,CAAC;IACpC,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACd,MAAMyB,YAAY,GAAGlC,cAAc,CAACS,KAAK,CAAC;MAC1CW,QAAQ,CAAC;QAAEP,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAEoB;MAAa,CAAC,CAAC;MACzDjC,KAAK,CAACQ,KAAK,CAACyB,YAAY,CAAC;MACzB,MAAMzB,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM0B,QAAQ,GAAG,MAAOC,IAAkB,IAAoB;IAC5D,IAAI;MACFhB,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAa,CAAC,CAAC;MAEhC,MAAM;QAAER,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMP,OAAO,CAACoC,QAAQ,CAACC,IAAI,CAAC;;MAEpD;MACAd,YAAY,CAACQ,OAAO,CAAC,OAAO,EAAExB,KAAK,CAAC;MACpCgB,YAAY,CAACQ,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC3B,IAAI,CAAC,CAAC;MAElDe,QAAQ,CAAC;QACPP,IAAI,EAAE,cAAc;QACpBC,OAAO,EAAE;UAAET,IAAI;UAAEC;QAAM;MACzB,CAAC,CAAC;MAEFL,KAAK,CAACgC,OAAO,CAAC,0BAA0B,CAAC;IAC3C,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACd,MAAMyB,YAAY,GAAGlC,cAAc,CAACS,KAAK,CAAC;MAC1CW,QAAQ,CAAC;QAAEP,IAAI,EAAE,cAAc;QAAEC,OAAO,EAAEoB;MAAa,CAAC,CAAC;MACzDjC,KAAK,CAACQ,KAAK,CAACyB,YAAY,CAAC;MACzB,MAAMzB,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM4B,MAAM,GAAG,MAAAA,CAAA,KAA2B;IACxC,IAAI;MACF,MAAMtC,OAAO,CAACsC,MAAM,CAAC,CAAC;IACxB,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACd;MACA6B,OAAO,CAAC7B,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C,CAAC,SAAS;MACR;MACAa,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;MAChCL,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;MAE/BP,QAAQ,CAAC;QAAEP,IAAI,EAAE;MAAS,CAAC,CAAC;MAC5BZ,KAAK,CAACgC,OAAO,CAAC,yBAAyB,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMM,aAAa,GAAG,MAAOH,IAAmB,IAAoB;IAClE,IAAI;MACF,MAAMI,WAAW,GAAG,MAAMzC,OAAO,CAACwC,aAAa,CAACH,IAAI,CAAC;;MAErD;MACAd,YAAY,CAACQ,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACQ,WAAW,CAAC,CAAC;MAEzDpB,QAAQ,CAAC;QAAEP,IAAI,EAAE,aAAa;QAAEC,OAAO,EAAE0B;MAAY,CAAC,CAAC;MACvDvC,KAAK,CAACgC,OAAO,CAAC,+BAA+B,CAAC;IAChD,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACd,MAAMyB,YAAY,GAAGlC,cAAc,CAACS,KAAK,CAAC;MAC1CR,KAAK,CAACQ,KAAK,CAACyB,YAAY,CAAC;MACzB,MAAMzB,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMgC,KAAsB,GAAG;IAC7BpC,IAAI,EAAEM,KAAK,CAACN,IAAI;IAChBC,KAAK,EAAEK,KAAK,CAACL,KAAK;IAClBC,eAAe,EAAEI,KAAK,CAACJ,eAAe;IACtCC,SAAS,EAAEG,KAAK,CAACH,SAAS;IAC1BoB,KAAK;IACLO,QAAQ;IACRE,MAAM;IACNE;EACF,CAAC;EAED,oBACEpC,OAAA,CAACY,WAAW,CAAC2B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvB,QAAA,EAChCA;EAAQ;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAA3B,EAAA,CAvIaF,YAAyC;AAAA8B,EAAA,GAAzC9B,YAAyC;AAwItD,OAAO,MAAM+B,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAC5C,MAAMC,OAAO,GAAGtD,UAAU,CAACmB,WAAW,CAAC;EACvC,IAAImC,OAAO,KAAKlC,SAAS,EAAE;IACzB,MAAM,IAAImC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAejC,WAAW;AAAC,IAAAgC,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}