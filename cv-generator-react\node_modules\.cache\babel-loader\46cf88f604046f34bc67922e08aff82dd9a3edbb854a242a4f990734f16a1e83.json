{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\cv-generator-react\\\\src\\\\components\\\\ui\\\\Input.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Input = ({\n  label,\n  placeholder,\n  value,\n  onChange,\n  error,\n  disabled = false,\n  required = false,\n  type = 'text',\n  className = ''\n}) => {\n  const inputClasses = `\n    block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 \n    focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm\n    ${error ? 'border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'}\n    ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'}\n    ${className}\n  `;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-1\",\n    children: [label && /*#__PURE__*/_jsxDEV(\"label\", {\n      className: \"block text-sm font-medium text-gray-700\",\n      children: [label, required && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-red-500 ml-1\",\n        children: \"*\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 24\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: type,\n      className: inputClasses,\n      placeholder: placeholder,\n      value: value,\n      onChange: e => onChange(e.target.value),\n      disabled: disabled,\n      required: required\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-sm text-red-600\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n};\n_c = Input;\nexport default Input;\nvar _c;\n$RefreshReg$(_c, \"Input\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Input", "label", "placeholder", "value", "onChange", "error", "disabled", "required", "type", "className", "inputClasses", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "e", "target", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/cv-generator-react/src/components/ui/Input.tsx"], "sourcesContent": ["import React from 'react';\nimport { InputProps } from '../../types';\n\nconst Input: React.FC<InputProps> = ({\n  label,\n  placeholder,\n  value,\n  onChange,\n  error,\n  disabled = false,\n  required = false,\n  type = 'text',\n  className = '',\n}) => {\n  const inputClasses = `\n    block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 \n    focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm\n    ${error \n      ? 'border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500' \n      : 'border-gray-300'\n    }\n    ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'}\n    ${className}\n  `;\n\n  return (\n    <div className=\"space-y-1\">\n      {label && (\n        <label className=\"block text-sm font-medium text-gray-700\">\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      <input\n        type={type}\n        className={inputClasses}\n        placeholder={placeholder}\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        disabled={disabled}\n        required={required}\n      />\n      {error && (\n        <p className=\"text-sm text-red-600\">{error}</p>\n      )}\n    </div>\n  );\n};\n\nexport default Input;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1B,MAAMC,KAA2B,GAAGA,CAAC;EACnCC,KAAK;EACLC,WAAW;EACXC,KAAK;EACLC,QAAQ;EACRC,KAAK;EACLC,QAAQ,GAAG,KAAK;EAChBC,QAAQ,GAAG,KAAK;EAChBC,IAAI,GAAG,MAAM;EACbC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAG;AACvB;AACA;AACA,MAAML,KAAK,GACH,qEAAqE,GACrE,iBAAiB;AACzB,MACMC,QAAQ,GAAG,+BAA+B,GAAG,UAAU;AAC7D,MAAMG,SAAS;AACf,GAAG;EAED,oBACEV,OAAA;IAAKU,SAAS,EAAC,WAAW;IAAAE,QAAA,GACvBV,KAAK,iBACJF,OAAA;MAAOU,SAAS,EAAC,yCAAyC;MAAAE,QAAA,GACvDV,KAAK,EACLM,QAAQ,iBAAIR,OAAA;QAAMU,SAAS,EAAC,mBAAmB;QAAAE,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CACR,eACDhB,OAAA;MACES,IAAI,EAAEA,IAAK;MACXC,SAAS,EAAEC,YAAa;MACxBR,WAAW,EAAEA,WAAY;MACzBC,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAGY,CAAC,IAAKZ,QAAQ,CAACY,CAAC,CAACC,MAAM,CAACd,KAAK,CAAE;MAC1CG,QAAQ,EAAEA,QAAS;MACnBC,QAAQ,EAAEA;IAAS;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,EACDV,KAAK,iBACJN,OAAA;MAAGU,SAAS,EAAC,sBAAsB;MAAAE,QAAA,EAAEN;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAC/C;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACG,EAAA,GA5CIlB,KAA2B;AA8CjC,eAAeA,KAAK;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}