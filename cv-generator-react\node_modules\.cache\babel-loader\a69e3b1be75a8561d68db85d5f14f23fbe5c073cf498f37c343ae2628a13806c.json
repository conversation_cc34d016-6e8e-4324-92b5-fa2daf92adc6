{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\cv-generator-react\\\\src\\\\components\\\\ui\\\\Button.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Button = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  onClick,\n  type = 'button',\n  className = '',\n  style = {}\n}) => {\n  const getButtonStyle = () => {\n    const baseStyle = {\n      display: 'inline-flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      fontWeight: '500',\n      borderRadius: '0.375rem',\n      border: 'none',\n      cursor: disabled || loading ? 'not-allowed' : 'pointer',\n      opacity: disabled || loading ? 0.5 : 1,\n      transition: 'all 0.2s',\n      textDecoration: 'none'\n    };\n    const variantStyles = {\n      primary: {\n        backgroundColor: '#3b82f6',\n        color: 'white'\n      },\n      secondary: {\n        backgroundColor: '#6b7280',\n        color: 'white'\n      },\n      outline: {\n        backgroundColor: 'transparent',\n        border: '1px solid #d1d5db',\n        color: '#374151'\n      },\n      ghost: {\n        backgroundColor: 'transparent',\n        color: '#374151'\n      },\n      danger: {\n        backgroundColor: '#dc2626',\n        color: 'white'\n      }\n    };\n    const sizeStyles = {\n      sm: {\n        padding: '0.375rem 0.75rem',\n        fontSize: '0.875rem'\n      },\n      md: {\n        padding: '0.5rem 1rem',\n        fontSize: '0.875rem'\n      },\n      lg: {\n        padding: '0.75rem 1.5rem',\n        fontSize: '1rem'\n      }\n    };\n    return {\n      ...baseStyle,\n      ...variantStyles[variant],\n      ...sizeStyles[size],\n      ...style\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    type: type,\n    style: getButtonStyle(),\n    disabled: disabled || loading,\n    onClick: onClick,\n    className: className,\n    children: [loading && /*#__PURE__*/_jsxDEV(\"svg\", {\n      style: {\n        marginRight: '0.5rem',\n        width: '1rem',\n        height: '1rem',\n        animation: 'spin 1s linear infinite'\n      },\n      xmlns: \"http://www.w3.org/2000/svg\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n        style: {\n          opacity: 0.25\n        },\n        cx: \"12\",\n        cy: \"12\",\n        r: \"10\",\n        stroke: \"currentColor\",\n        strokeWidth: \"4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        style: {\n          opacity: 0.75\n        },\n        fill: \"currentColor\",\n        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 9\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_c = Button;\nexport default Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "children", "variant", "size", "disabled", "loading", "onClick", "type", "className", "style", "getButtonStyle", "baseStyle", "display", "alignItems", "justifyContent", "fontWeight", "borderRadius", "border", "cursor", "opacity", "transition", "textDecoration", "variantStyles", "primary", "backgroundColor", "color", "secondary", "outline", "ghost", "danger", "sizeStyles", "sm", "padding", "fontSize", "md", "lg", "marginRight", "width", "height", "animation", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/cv-generator-react/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface ButtonProps {\n  children: React.ReactNode;\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';\n  size?: 'sm' | 'md' | 'lg';\n  disabled?: boolean;\n  loading?: boolean;\n  onClick?: () => void;\n  type?: 'button' | 'submit' | 'reset';\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  onClick,\n  type = 'button',\n  className = '',\n  style = {},\n}) => {\n  const getButtonStyle = () => {\n    const baseStyle: React.CSSProperties = {\n      display: 'inline-flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      fontWeight: '500',\n      borderRadius: '0.375rem',\n      border: 'none',\n      cursor: disabled || loading ? 'not-allowed' : 'pointer',\n      opacity: disabled || loading ? 0.5 : 1,\n      transition: 'all 0.2s',\n      textDecoration: 'none',\n    };\n\n    const variantStyles = {\n      primary: { backgroundColor: '#3b82f6', color: 'white' },\n      secondary: { backgroundColor: '#6b7280', color: 'white' },\n      outline: { backgroundColor: 'transparent', border: '1px solid #d1d5db', color: '#374151' },\n      ghost: { backgroundColor: 'transparent', color: '#374151' },\n      danger: { backgroundColor: '#dc2626', color: 'white' },\n    };\n\n    const sizeStyles = {\n      sm: { padding: '0.375rem 0.75rem', fontSize: '0.875rem' },\n      md: { padding: '0.5rem 1rem', fontSize: '0.875rem' },\n      lg: { padding: '0.75rem 1.5rem', fontSize: '1rem' },\n    };\n\n    return { ...baseStyle, ...variantStyles[variant], ...sizeStyles[size], ...style };\n  };\n\n  return (\n    <button\n      type={type}\n      style={getButtonStyle()}\n      disabled={disabled || loading}\n      onClick={onClick}\n      className={className}\n    >\n      {loading && (\n        <svg\n          style={{ marginRight: '0.5rem', width: '1rem', height: '1rem', animation: 'spin 1s linear infinite' }}\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            style={{ opacity: 0.25 }}\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          ></circle>\n          <path\n            style={{ opacity: 0.75 }}\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          ></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc1B,MAAMC,MAA6B,GAAGA,CAAC;EACrCC,QAAQ;EACRC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,IAAI;EACXC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,OAAO;EACPC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC;AACX,CAAC,KAAK;EACJ,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,SAA8B,GAAG;MACrCC,OAAO,EAAE,aAAa;MACtBC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,UAAU;MACxBC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAEd,QAAQ,IAAIC,OAAO,GAAG,aAAa,GAAG,SAAS;MACvDc,OAAO,EAAEf,QAAQ,IAAIC,OAAO,GAAG,GAAG,GAAG,CAAC;MACtCe,UAAU,EAAE,UAAU;MACtBC,cAAc,EAAE;IAClB,CAAC;IAED,MAAMC,aAAa,GAAG;MACpBC,OAAO,EAAE;QAAEC,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAQ,CAAC;MACvDC,SAAS,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAQ,CAAC;MACzDE,OAAO,EAAE;QAAEH,eAAe,EAAE,aAAa;QAAEP,MAAM,EAAE,mBAAmB;QAAEQ,KAAK,EAAE;MAAU,CAAC;MAC1FG,KAAK,EAAE;QAAEJ,eAAe,EAAE,aAAa;QAAEC,KAAK,EAAE;MAAU,CAAC;MAC3DI,MAAM,EAAE;QAAEL,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAQ;IACvD,CAAC;IAED,MAAMK,UAAU,GAAG;MACjBC,EAAE,EAAE;QAAEC,OAAO,EAAE,kBAAkB;QAAEC,QAAQ,EAAE;MAAW,CAAC;MACzDC,EAAE,EAAE;QAAEF,OAAO,EAAE,aAAa;QAAEC,QAAQ,EAAE;MAAW,CAAC;MACpDE,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAO;IACpD,CAAC;IAED,OAAO;MAAE,GAAGtB,SAAS;MAAE,GAAGW,aAAa,CAACpB,OAAO,CAAC;MAAE,GAAG4B,UAAU,CAAC3B,IAAI,CAAC;MAAE,GAAGM;IAAM,CAAC;EACnF,CAAC;EAED,oBACEV,OAAA;IACEQ,IAAI,EAAEA,IAAK;IACXE,KAAK,EAAEC,cAAc,CAAC,CAAE;IACxBN,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;IAC9BC,OAAO,EAAEA,OAAQ;IACjBE,SAAS,EAAEA,SAAU;IAAAP,QAAA,GAEpBI,OAAO,iBACNN,OAAA;MACEU,KAAK,EAAE;QAAE2B,WAAW,EAAE,QAAQ;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE,MAAM;QAAEC,SAAS,EAAE;MAA0B,CAAE;MACtGC,KAAK,EAAC,4BAA4B;MAClCC,IAAI,EAAC,MAAM;MACXC,OAAO,EAAC,WAAW;MAAAzC,QAAA,gBAEnBF,OAAA;QACEU,KAAK,EAAE;UAAEU,OAAO,EAAE;QAAK,CAAE;QACzBwB,EAAE,EAAC,IAAI;QACPC,EAAE,EAAC,IAAI;QACPC,CAAC,EAAC,IAAI;QACNC,MAAM,EAAC,cAAc;QACrBC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACVpD,OAAA;QACEU,KAAK,EAAE;UAAEU,OAAO,EAAE;QAAK,CAAE;QACzBsB,IAAI,EAAC,cAAc;QACnBW,CAAC,EAAC;MAAiH;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9G,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACAlD,QAAQ;EAAA;IAAA+C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACE,EAAA,GA3EIrD,MAA6B;AA6EnC,eAAeA,MAAM;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}