import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { CV } from '../types';
import apiService from '../services/api';

// CV state interface
interface CVState {
  cvs: CV[];
  currentCV: CV | null;
  isLoading: boolean;
  error: string | null;
}

// CV actions
type CVAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CVS'; payload: CV[] }
  | { type: 'SET_CURRENT_CV'; payload: CV | null }
  | { type: 'ADD_CV'; payload: CV }
  | { type: 'UPDATE_CV'; payload: CV }
  | { type: 'DELETE_CV'; payload: number }
  | { type: 'CLEAR_ERROR' };

// CV context interface
interface CVContextType {
  cvs: CV[];
  currentCV: CV | null;
  isLoading: boolean;
  error: string | null;
  fetchCVs: () => Promise<void>;
  fetchCV: (id: number) => Promise<void>;
  createCV: (data: Partial<CV>) => Promise<CV>;
  updateCV: (id: number, data: Partial<CV>) => Promise<void>;
  deleteCV: (id: number) => Promise<void>;
  duplicateCV: (id: number) => Promise<CV>;
  setCurrentCV: (cv: CV | null) => void;
  clearError: () => void;
}

// Initial state
const initialState: CVState = {
  cvs: [],
  currentCV: null,
  isLoading: false,
  error: null,
};

// CV reducer
const cvReducer = (state: CVState, action: CVAction): CVState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'SET_CVS':
      return {
        ...state,
        cvs: action.payload,
        isLoading: false,
        error: null,
      };
    case 'SET_CURRENT_CV':
      return {
        ...state,
        currentCV: action.payload,
        isLoading: false,
        error: null,
      };
    case 'ADD_CV':
      return {
        ...state,
        cvs: [action.payload, ...state.cvs],
        isLoading: false,
        error: null,
      };
    case 'UPDATE_CV':
      return {
        ...state,
        cvs: state.cvs.map(cv => cv.id === action.payload.id ? action.payload : cv),
        currentCV: state.currentCV?.id === action.payload.id ? action.payload : state.currentCV,
        isLoading: false,
        error: null,
      };
    case 'DELETE_CV':
      return {
        ...state,
        cvs: state.cvs.filter(cv => cv.id !== action.payload),
        currentCV: state.currentCV?.id === action.payload ? null : state.currentCV,
        isLoading: false,
        error: null,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Create context
const CVContext = createContext<CVContextType | undefined>(undefined);

// CV provider component
interface CVProviderProps {
  children: ReactNode;
}

export const CVProvider: React.FC<CVProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(cvReducer, initialState);

  // Fetch all CVs
  const fetchCVs = async (): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const cvs = await apiService.getCVs();
      dispatch({ type: 'SET_CVS', payload: cvs });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch CVs';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    }
  };

  // Fetch single CV
  const fetchCV = async (id: number): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const cv = await apiService.getCV(id);
      dispatch({ type: 'SET_CURRENT_CV', payload: cv });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch CV';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    }
  };

  // Create new CV
  const createCV = async (data: Partial<CV>): Promise<CV> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const cv = await apiService.createCV(data);
      dispatch({ type: 'ADD_CV', payload: cv });
      return cv;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to create CV';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  };

  // Update CV
  const updateCV = async (id: number, data: Partial<CV>): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const cv = await apiService.updateCV(id, data);
      dispatch({ type: 'UPDATE_CV', payload: cv });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to update CV';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  };

  // Delete CV
  const deleteCV = async (id: number): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      await apiService.deleteCV(id);
      dispatch({ type: 'DELETE_CV', payload: id });
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to delete CV';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  };

  // Duplicate CV
  const duplicateCV = async (id: number): Promise<CV> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const cv = await apiService.duplicateCV(id);
      dispatch({ type: 'ADD_CV', payload: cv });
      return cv;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to duplicate CV';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  };

  // Set current CV
  const setCurrentCV = (cv: CV | null): void => {
    dispatch({ type: 'SET_CURRENT_CV', payload: cv });
  };

  // Clear error
  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Context value
  const value: CVContextType = {
    cvs: state.cvs,
    currentCV: state.currentCV,
    isLoading: state.isLoading,
    error: state.error,
    fetchCVs,
    fetchCV,
    createCV,
    updateCV,
    deleteCV,
    duplicateCV,
    setCurrentCV,
    clearError,
  };

  return (
    <CVContext.Provider value={value}>
      {children}
    </CVContext.Provider>
  );
};

// Custom hook to use CV context
export const useCV = (): CVContextType => {
  const context = useContext(CVContext);
  if (context === undefined) {
    throw new Error('useCV must be used within a CVProvider');
  }
  return context;
};

export default CVContext;
