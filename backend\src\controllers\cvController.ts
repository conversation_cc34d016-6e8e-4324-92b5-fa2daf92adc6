import { Request, Response } from 'express';
import { CV } from '../models/CV';
import { AuthenticatedRequest, AppError, ErrorType, HttpStatus } from '../types';
import { sendSuccess, sendError, sendCreated, sendNotFound, sendPaginatedResponse } from '../utils/response';
import { asyncHandler } from '../middleware/errorHandler';

/**
 * Get all CVs for the authenticated user
 */
export const getUserCVs = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const offset = (page - 1) * limit;

  const { count, rows: cvs } = await CV.findAndCountAll({
    where: { userId: req.user.id },
    order: [['updatedAt', 'DESC']],
    limit,
    offset,
  });

  sendPaginatedResponse(res, 'CVs retrieved successfully', cvs, {
    page,
    limit,
    total: count,
  });
});

/**
 * Get a specific CV by ID
 */
export const getCVById = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  const { id } = req.params;
  
  const cv = await CV.findOne({
    where: { 
      id: parseInt(id),
      userId: req.user.id 
    },
  });

  if (!cv) {
    sendNotFound(res, 'CV');
    return;
  }

  sendSuccess(res, 'CV retrieved successfully', cv);
});

/**
 * Create a new CV
 */
export const createCV = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  const { title, data, templateId, isPublic } = req.body;

  const cv = await CV.create({
    userId: req.user.id,
    title,
    data,
    templateId,
    isPublic,
  });

  sendCreated(res, 'CV created successfully', cv);
});

/**
 * Update an existing CV
 */
export const updateCV = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  const { id } = req.params;
  const { title, data, templateId, isPublic } = req.body;

  const cv = await CV.findOne({
    where: { 
      id: parseInt(id),
      userId: req.user.id 
    },
  });

  if (!cv) {
    sendNotFound(res, 'CV');
    return;
  }

  const updateData: Partial<{
    title: string;
    data: any;
    templateId: string;
    isPublic: boolean;
  }> = {};

  if (title !== undefined) updateData.title = title;
  if (data !== undefined) updateData.data = data;
  if (templateId !== undefined) updateData.templateId = templateId;
  if (isPublic !== undefined) updateData.isPublic = isPublic;

  await cv.update(updateData);

  sendSuccess(res, 'CV updated successfully', cv);
});

/**
 * Delete a CV
 */
export const deleteCV = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  const { id } = req.params;

  const cv = await CV.findOne({
    where: { 
      id: parseInt(id),
      userId: req.user.id 
    },
  });

  if (!cv) {
    sendNotFound(res, 'CV');
    return;
  }

  await cv.destroy();

  sendSuccess(res, 'CV deleted successfully');
});

/**
 * Duplicate a CV
 */
export const duplicateCV = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  const { id } = req.params;

  const originalCV = await CV.findOne({
    where: { 
      id: parseInt(id),
      userId: req.user.id 
    },
  });

  if (!originalCV) {
    sendNotFound(res, 'CV');
    return;
  }

  const duplicatedCV = await CV.create({
    userId: req.user.id,
    title: `${originalCV.title} (Copy)`,
    data: originalCV.data,
    templateId: originalCV.templateId,
    isPublic: false, // Duplicated CVs are private by default
  });

  sendCreated(res, 'CV duplicated successfully', duplicatedCV);
});

/**
 * Get public CV by slug
 */
export const getPublicCV = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { slug } = req.params;

  const cv = await CV.findByPublicSlug(slug);

  if (!cv) {
    sendNotFound(res, 'CV');
    return;
  }

  sendSuccess(res, 'Public CV retrieved successfully', cv.toPublicJSON());
});

/**
 * Toggle CV public status
 */
export const togglePublicStatus = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  const { id } = req.params;

  const cv = await CV.findOne({
    where: { 
      id: parseInt(id),
      userId: req.user.id 
    },
  });

  if (!cv) {
    sendNotFound(res, 'CV');
    return;
  }

  await cv.update({ isPublic: !cv.isPublic });

  sendSuccess(res, `CV ${cv.isPublic ? 'published' : 'unpublished'} successfully`, {
    isPublic: cv.isPublic,
    publicSlug: cv.publicSlug,
  });
});

/**
 * Get CV statistics for user
 */
export const getCVStats = asyncHandler(async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  if (!req.user) {
    sendError(res, 'User not found', HttpStatus.UNAUTHORIZED);
    return;
  }

  const totalCVs = await CV.count({
    where: { userId: req.user.id },
  });

  const publicCVs = await CV.count({
    where: { 
      userId: req.user.id,
      isPublic: true 
    },
  });

  const templateStats = await CV.findAll({
    where: { userId: req.user.id },
    attributes: ['templateId'],
    group: ['templateId'],
    raw: true,
  });

  sendSuccess(res, 'CV statistics retrieved successfully', {
    totalCVs,
    publicCVs,
    privateCVs: totalCVs - publicCVs,
    templateStats,
  });
});

export default {
  getUserCVs,
  getCVById,
  createCV,
  updateCV,
  deleteCV,
  duplicateCV,
  getPublicCV,
  togglePublicStatus,
  getCVStats,
};
