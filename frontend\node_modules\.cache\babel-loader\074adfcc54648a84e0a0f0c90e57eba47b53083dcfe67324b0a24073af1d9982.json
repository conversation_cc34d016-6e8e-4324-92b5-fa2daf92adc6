{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\frontend\\\\src\\\\components\\\\ui\\\\Button.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Button = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  onClick,\n  type = 'button',\n  className = ''\n}) => {\n  const getButtonClasses = () => {\n    let classes = 'btn';\n\n    // Add variant classes\n    switch (variant) {\n      case 'primary':\n        classes += ' btn-primary';\n        break;\n      case 'secondary':\n        classes += ' btn-secondary';\n        break;\n      case 'outline':\n        classes += ' btn-outline';\n        break;\n      case 'ghost':\n        classes += ' btn-ghost';\n        break;\n      case 'danger':\n        classes += ' btn-danger';\n        break;\n      default:\n        classes += ' btn-primary';\n    }\n\n    // Add size classes\n    switch (size) {\n      case 'sm':\n        classes += ' btn-sm';\n        break;\n      case 'md':\n        classes += ' btn-md';\n        break;\n      case 'lg':\n        classes += ' btn-lg';\n        break;\n      default:\n        classes += ' btn-md';\n    }\n    return `${classes} ${className}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"button\", {\n    type: type,\n    className: getButtonClasses(),\n    disabled: disabled || loading,\n    onClick: onClick,\n    children: [loading && /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"loading-spinner mr-2\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      style: {\n        width: '16px',\n        height: '16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n        style: {\n          opacity: 0.25\n        },\n        cx: \"12\",\n        cy: \"12\",\n        r: \"10\",\n        stroke: \"currentColor\",\n        strokeWidth: \"4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        style: {\n          opacity: 0.75\n        },\n        fill: \"currentColor\",\n        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 9\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_c = Button;\nexport default Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "children", "variant", "size", "disabled", "loading", "onClick", "type", "className", "getButtonClasses", "classes", "xmlns", "fill", "viewBox", "style", "width", "height", "opacity", "cx", "cy", "r", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { ButtonProps } from '../../types';\n\nconst Button: React.FC<ButtonProps> = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  onClick,\n  type = 'button',\n  className = '',\n}) => {\n  const getButtonClasses = () => {\n    let classes = 'btn';\n\n    // Add variant classes\n    switch (variant) {\n      case 'primary':\n        classes += ' btn-primary';\n        break;\n      case 'secondary':\n        classes += ' btn-secondary';\n        break;\n      case 'outline':\n        classes += ' btn-outline';\n        break;\n      case 'ghost':\n        classes += ' btn-ghost';\n        break;\n      case 'danger':\n        classes += ' btn-danger';\n        break;\n      default:\n        classes += ' btn-primary';\n    }\n\n    // Add size classes\n    switch (size) {\n      case 'sm':\n        classes += ' btn-sm';\n        break;\n      case 'md':\n        classes += ' btn-md';\n        break;\n      case 'lg':\n        classes += ' btn-lg';\n        break;\n      default:\n        classes += ' btn-md';\n    }\n\n    return `${classes} ${className}`;\n  };\n\n  return (\n    <button\n      type={type}\n      className={getButtonClasses()}\n      disabled={disabled || loading}\n      onClick={onClick}\n    >\n      {loading && (\n        <svg\n          className=\"loading-spinner mr-2\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n          style={{ width: '16px', height: '16px' }}\n        >\n          <circle\n            style={{ opacity: 0.25 }}\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          ></circle>\n          <path\n            style={{ opacity: 0.75 }}\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          ></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1B,MAAMC,MAA6B,GAAGA,CAAC;EACrCC,QAAQ;EACRC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,IAAI;EACXC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,OAAO;EACPC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,OAAO,GAAG,KAAK;;IAEnB;IACA,QAAQR,OAAO;MACb,KAAK,SAAS;QACZQ,OAAO,IAAI,cAAc;QACzB;MACF,KAAK,WAAW;QACdA,OAAO,IAAI,gBAAgB;QAC3B;MACF,KAAK,SAAS;QACZA,OAAO,IAAI,cAAc;QACzB;MACF,KAAK,OAAO;QACVA,OAAO,IAAI,YAAY;QACvB;MACF,KAAK,QAAQ;QACXA,OAAO,IAAI,aAAa;QACxB;MACF;QACEA,OAAO,IAAI,cAAc;IAC7B;;IAEA;IACA,QAAQP,IAAI;MACV,KAAK,IAAI;QACPO,OAAO,IAAI,SAAS;QACpB;MACF,KAAK,IAAI;QACPA,OAAO,IAAI,SAAS;QACpB;MACF,KAAK,IAAI;QACPA,OAAO,IAAI,SAAS;QACpB;MACF;QACEA,OAAO,IAAI,SAAS;IACxB;IAEA,OAAO,GAAGA,OAAO,IAAIF,SAAS,EAAE;EAClC,CAAC;EAED,oBACET,OAAA;IACEQ,IAAI,EAAEA,IAAK;IACXC,SAAS,EAAEC,gBAAgB,CAAC,CAAE;IAC9BL,QAAQ,EAAEA,QAAQ,IAAIC,OAAQ;IAC9BC,OAAO,EAAEA,OAAQ;IAAAL,QAAA,GAEhBI,OAAO,iBACNN,OAAA;MACES,SAAS,EAAC,sBAAsB;MAChCG,KAAK,EAAC,4BAA4B;MAClCC,IAAI,EAAC,MAAM;MACXC,OAAO,EAAC,WAAW;MACnBC,KAAK,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAf,QAAA,gBAEzCF,OAAA;QACEe,KAAK,EAAE;UAAEG,OAAO,EAAE;QAAK,CAAE;QACzBC,EAAE,EAAC,IAAI;QACPC,EAAE,EAAC,IAAI;QACPC,CAAC,EAAC,IAAI;QACNC,MAAM,EAAC,cAAc;QACrBC,WAAW,EAAC;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACV3B,OAAA;QACEe,KAAK,EAAE;UAAEG,OAAO,EAAE;QAAK,CAAE;QACzBL,IAAI,EAAC,cAAc;QACnBe,CAAC,EAAC;MAAiH;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9G,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACN,EACAzB,QAAQ;EAAA;IAAAsB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACE,EAAA,GArFI5B,MAA6B;AAuFnC,eAAeA,MAAM;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}