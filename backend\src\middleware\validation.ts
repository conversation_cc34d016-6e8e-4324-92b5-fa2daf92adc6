import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { sendValidationError } from '../utils/response';

/**
 * Validation middleware factory
 */
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const validationErrors: Record<string, string[]> = {};
      
      error.details.forEach((detail) => {
        const field = detail.path.join('.');
        if (!validationErrors[field]) {
          validationErrors[field] = [];
        }
        validationErrors[field].push(detail.message);
      });

      sendValidationError(res, validationErrors);
      return;
    }

    // Replace req.body with validated and sanitized data
    req.body = value;
    next();
  };
};

/**
 * User registration validation schema
 */
export const registerSchema = Joi.object({
  name: Joi.string()
    .min(2)
    .max(255)
    .required()
    .messages({
      'string.min': 'Name must be at least 2 characters long',
      'string.max': 'Name must not exceed 255 characters',
      'any.required': 'Name is required',
    }),
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required',
    }),
  password: Joi.string()
    .min(6)
    .max(128)
    .required()
    .messages({
      'string.min': 'Password must be at least 6 characters long',
      'string.max': 'Password must not exceed 128 characters',
      'any.required': 'Password is required',
    }),
});

/**
 * User login validation schema
 */
export const loginSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required',
    }),
  password: Joi.string()
    .required()
    .messages({
      'any.required': 'Password is required',
    }),
});

/**
 * CV creation/update validation schema
 */
export const cvSchema = Joi.object({
  title: Joi.string()
    .min(1)
    .max(255)
    .required()
    .messages({
      'string.min': 'Title is required',
      'string.max': 'Title must not exceed 255 characters',
      'any.required': 'Title is required',
    }),
  data: Joi.object({
    personalInfo: Joi.object({
      firstName: Joi.string().required(),
      lastName: Joi.string().required(),
      email: Joi.string().email().required(),
      phone: Joi.string().optional(),
      address: Joi.string().optional(),
      website: Joi.string().uri().optional(),
      linkedin: Joi.string().uri().optional(),
      github: Joi.string().uri().optional(),
      summary: Joi.string().optional(),
    }).required(),
    experience: Joi.array().items(
      Joi.object({
        id: Joi.string().required(),
        company: Joi.string().required(),
        position: Joi.string().required(),
        startDate: Joi.string().required(),
        endDate: Joi.string().optional(),
        current: Joi.boolean().default(false),
        description: Joi.string().optional(),
        achievements: Joi.array().items(Joi.string()).optional(),
      })
    ).default([]),
    education: Joi.array().items(
      Joi.object({
        id: Joi.string().required(),
        institution: Joi.string().required(),
        degree: Joi.string().required(),
        startDate: Joi.string().required(),
        endDate: Joi.string().optional(),
        gpa: Joi.string().optional(),
        description: Joi.string().optional(),
      })
    ).default([]),
    skills: Joi.array().items(
      Joi.object({
        category: Joi.string().required(),
        items: Joi.array().items(Joi.string()).required(),
      })
    ).default([]),
    projects: Joi.array().items(
      Joi.object({
        id: Joi.string().required(),
        name: Joi.string().required(),
        description: Joi.string().required(),
        technologies: Joi.array().items(Joi.string()).required(),
        url: Joi.string().uri().optional(),
        startDate: Joi.string().optional(),
        endDate: Joi.string().optional(),
      })
    ).default([]),
    certifications: Joi.array().items(
      Joi.object({
        id: Joi.string().required(),
        name: Joi.string().required(),
        issuer: Joi.string().required(),
        date: Joi.string().required(),
        url: Joi.string().uri().optional(),
      })
    ).default([]),
    languages: Joi.array().items(
      Joi.object({
        language: Joi.string().required(),
        proficiency: Joi.string().required(),
      })
    ).default([]),
  }).required(),
  templateId: Joi.string()
    .valid('modern', 'classic', 'creative', 'minimal')
    .default('modern'),
  isPublic: Joi.boolean().default(false),
});

/**
 * Password reset request validation schema
 */
export const passwordResetRequestSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required',
    }),
});

/**
 * Password reset validation schema
 */
export const passwordResetSchema = Joi.object({
  token: Joi.string()
    .required()
    .messages({
      'any.required': 'Reset token is required',
    }),
  password: Joi.string()
    .min(6)
    .max(128)
    .required()
    .messages({
      'string.min': 'Password must be at least 6 characters long',
      'string.max': 'Password must not exceed 128 characters',
      'any.required': 'Password is required',
    }),
});

export default {
  validate,
  registerSchema,
  loginSchema,
  cvSchema,
  passwordResetRequestSchema,
  passwordResetSchema,
};
