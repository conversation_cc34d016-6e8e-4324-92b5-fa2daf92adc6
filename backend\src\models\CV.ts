import { DataTypes, Model, Optional, BelongsToGetAssociationMixin } from 'sequelize';
import { sequelize } from '../config/database';
import { User } from './User';

// CV data structure interfaces
export interface PersonalInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  address?: string;
  website?: string;
  linkedin?: string;
  github?: string;
  summary?: string;
}

export interface Experience {
  id: string;
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  description?: string;
  achievements?: string[];
}

export interface Education {
  id: string;
  institution: string;
  degree: string;
  startDate: string;
  endDate?: string;
  gpa?: string;
  description?: string;
}

export interface Skill {
  category: string;
  items: string[];
}

export interface Project {
  id: string;
  name: string;
  description: string;
  technologies: string[];
  url?: string;
  startDate?: string;
  endDate?: string;
}

export interface Certification {
  id: string;
  name: string;
  issuer: string;
  date: string;
  url?: string;
}

export interface Language {
  language: string;
  proficiency: string;
}

export interface CVData {
  personalInfo: PersonalInfo;
  experience: Experience[];
  education: Education[];
  skills: Skill[];
  projects: Project[];
  certifications: Certification[];
  languages: Language[];
}

// CV attributes interface
export interface CVAttributes {
  id: number;
  userId: number;
  title: string;
  data: CVData;
  templateId: string;
  isPublic: boolean;
  publicSlug?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Optional attributes for CV creation
export interface CVCreationAttributes extends Optional<CVAttributes, 'id' | 'isPublic' | 'publicSlug' | 'createdAt' | 'updatedAt'> {}

// CV model class
export class CV extends Model<CVAttributes, CVCreationAttributes> implements CVAttributes {
  public id!: number;
  public userId!: number;
  public title!: string;
  public data!: CVData;
  public templateId!: string;
  public isPublic!: boolean;
  public publicSlug?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Association methods
  public getUser!: BelongsToGetAssociationMixin<User>;

  // Instance methods
  public generatePublicSlug(): string {
    const randomString = Math.random().toString(36).substring(2, 15);
    return `${this.data.personalInfo.firstName.toLowerCase()}-${this.data.personalInfo.lastName.toLowerCase()}-${randomString}`;
  }

  public toPublicJSON(): Partial<CVAttributes> {
    return {
      id: this.id,
      title: this.title,
      data: this.data,
      templateId: this.templateId,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }

  // Static methods
  public static async findByUserId(userId: number): Promise<CV[]> {
    return this.findAll({
      where: { userId },
      order: [['updatedAt', 'DESC']],
    });
  }

  public static async findByPublicSlug(slug: string): Promise<CV | null> {
    return this.findOne({
      where: { 
        publicSlug: slug,
        isPublic: true 
      },
    });
  }
}

// Initialize the CV model
CV.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'CASCADE',
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: false,
      defaultValue: 'My CV',
      validate: {
        notEmpty: {
          msg: 'CV title is required',
        },
        len: {
          args: [1, 255],
          msg: 'CV title must be between 1 and 255 characters',
        },
      },
    },
    data: {
      type: DataTypes.JSON,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'CV data is required',
        },
        isValidCVData(value: any) {
          if (!value || typeof value !== 'object') {
            throw new Error('CV data must be a valid object');
          }
          if (!value.personalInfo || typeof value.personalInfo !== 'object') {
            throw new Error('Personal info is required');
          }
          if (!value.personalInfo.firstName || !value.personalInfo.lastName) {
            throw new Error('First name and last name are required');
          }
        },
      },
    },
    templateId: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: 'modern',
      field: 'template_id',
      validate: {
        isIn: {
          args: [['modern', 'classic', 'creative', 'minimal']],
          msg: 'Invalid template ID',
        },
      },
    },
    isPublic: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_public',
    },
    publicSlug: {
      type: DataTypes.STRING(100),
      allowNull: true,
      unique: true,
      field: 'public_slug',
      validate: {
        len: {
          args: [3, 100],
          msg: 'Public slug must be between 3 and 100 characters',
        },
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at',
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'updated_at',
    },
  },
  {
    sequelize,
    modelName: 'CV',
    tableName: 'cvs',
    timestamps: true,
    underscored: true,
    hooks: {
      beforeCreate: (cv: CV) => {
        if (cv.isPublic && !cv.publicSlug) {
          cv.publicSlug = cv.generatePublicSlug();
        }
      },
      beforeUpdate: (cv: CV) => {
        if (cv.changed('isPublic') && cv.isPublic && !cv.publicSlug) {
          cv.publicSlug = cv.generatePublicSlug();
        }
      },
    },
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['created_at'],
      },
      {
        unique: true,
        fields: ['public_slug'],
      },
      {
        fields: ['is_public'],
      },
      {
        fields: ['user_id', 'updated_at'],
      },
    ],
  }
);

// Define associations
CV.belongsTo(User, { foreignKey: 'userId', as: 'user' });
User.hasMany(CV, { foreignKey: 'userId', as: 'cvs' });

export default CV;
