import { Response } from 'express';
import { ApiResponse, PaginatedResponse, HttpStatus } from '../types';

/**
 * Send success response
 */
export const sendSuccess = <T>(
  res: Response,
  message: string,
  data?: T,
  statusCode: number = HttpStatus.OK
): Response => {
  const response: ApiResponse<T> = {
    success: true,
    message,
    data,
  };
  
  return res.status(statusCode).json(response);
};

/**
 * Send error response
 */
export const sendError = (
  res: Response,
  message: string,
  statusCode: number = HttpStatus.INTERNAL_SERVER_ERROR,
  error?: string,
  errors?: Record<string, string[]>
): Response => {
  const response: ApiResponse = {
    success: false,
    message,
    error,
    errors,
  };
  
  return res.status(statusCode).json(response);
};

/**
 * Send paginated response
 */
export const sendPaginatedResponse = <T>(
  res: Response,
  message: string,
  data: T[],
  pagination: {
    page: number;
    limit: number;
    total: number;
  },
  statusCode: number = HttpStatus.OK
): Response => {
  const totalPages = Math.ceil(pagination.total / pagination.limit);
  
  const response: PaginatedResponse<T[]> = {
    success: true,
    message,
    data,
    pagination: {
      ...pagination,
      totalPages,
    },
  };
  
  return res.status(statusCode).json(response);
};

/**
 * Send validation error response
 */
export const sendValidationError = (
  res: Response,
  errors: Record<string, string[]>
): Response => {
  return sendError(
    res,
    'Validation failed',
    HttpStatus.UNPROCESSABLE_ENTITY,
    undefined,
    errors
  );
};

/**
 * Send not found response
 */
export const sendNotFound = (
  res: Response,
  resource: string = 'Resource'
): Response => {
  return sendError(
    res,
    `${resource} not found`,
    HttpStatus.NOT_FOUND
  );
};

/**
 * Send unauthorized response
 */
export const sendUnauthorized = (
  res: Response,
  message: string = 'Unauthorized access'
): Response => {
  return sendError(
    res,
    message,
    HttpStatus.UNAUTHORIZED
  );
};

/**
 * Send forbidden response
 */
export const sendForbidden = (
  res: Response,
  message: string = 'Access forbidden'
): Response => {
  return sendError(
    res,
    message,
    HttpStatus.FORBIDDEN
  );
};

/**
 * Send conflict response
 */
export const sendConflict = (
  res: Response,
  message: string = 'Resource already exists'
): Response => {
  return sendError(
    res,
    message,
    HttpStatus.CONFLICT
  );
};

/**
 * Send created response
 */
export const sendCreated = <T>(
  res: Response,
  message: string,
  data?: T
): Response => {
  return sendSuccess(res, message, data, HttpStatus.CREATED);
};

/**
 * Send no content response
 */
export const sendNoContent = (res: Response): Response => {
  return res.status(HttpStatus.NO_CONTENT).send();
};

export default {
  sendSuccess,
  sendError,
  sendPaginatedResponse,
  sendValidationError,
  sendNotFound,
  sendUnauthorized,
  sendForbidden,
  sendConflict,
  sendCreated,
  sendNoContent,
};
