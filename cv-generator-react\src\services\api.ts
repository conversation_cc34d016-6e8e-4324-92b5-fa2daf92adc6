import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  AuthResponse,
  LoginCredentials,
  RegisterData,
  User,
  CV,
  ApiResponse
} from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Authentication methods
  async register(data: RegisterData): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/auth/register', data);
    return response.data;
  }

  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response: AxiosResponse<AuthResponse> = await this.api.post('/auth/login', credentials);
    return response.data;
  }

  async logout(): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.post('/auth/logout');
    return response.data;
  }

  async getProfile(): Promise<User> {
    const response: AxiosResponse<{ success: boolean; user: User }> = await this.api.get('/auth/profile');
    return response.data.user;
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    const response: AxiosResponse<{ success: boolean; user: User }> = await this.api.put('/auth/profile', data);
    return response.data.user;
  }

  async changePassword(data: { current_password: string; password: string; password_confirmation: string }): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.put('/auth/password', data);
    return response.data;
  }

  // CV methods
  async getCVs(): Promise<CV[]> {
    const response: AxiosResponse<{ success: boolean; cvs: CV[] }> = await this.api.get('/cvs');
    return response.data.cvs;
  }

  async getCV(id: number): Promise<CV> {
    const response: AxiosResponse<{ success: boolean; cv: CV }> = await this.api.get(`/cvs/${id}`);
    return response.data.cv;
  }

  async createCV(data: Partial<CV>): Promise<CV> {
    const response: AxiosResponse<{ success: boolean; cv: CV }> = await this.api.post('/cvs', data);
    return response.data.cv;
  }

  async updateCV(id: number, data: Partial<CV>): Promise<CV> {
    const response: AxiosResponse<{ success: boolean; cv: CV }> = await this.api.put(`/cvs/${id}`, data);
    return response.data.cv;
  }

  async deleteCV(id: number): Promise<ApiResponse> {
    const response: AxiosResponse<ApiResponse> = await this.api.delete(`/cvs/${id}`);
    return response.data;
  }

  async duplicateCV(id: number): Promise<CV> {
    const response: AxiosResponse<{ success: boolean; cv: CV }> = await this.api.post(`/cvs/${id}/duplicate`);
    return response.data.cv;
  }

  async getCVStatistics(): Promise<any> {
    const response: AxiosResponse<{ success: boolean; statistics: any }> = await this.api.get('/cvs/statistics');
    return response.data.statistics;
  }

  // Public CV methods
  async getPublicCV(slug: string): Promise<CV> {
    const response: AxiosResponse<{ success: boolean; cv: CV }> = await this.api.get(`/cv/public/${slug}`);
    return response.data.cv;
  }

  // PDF generation
  async generatePDF(id: number): Promise<Blob> {
    const response: AxiosResponse<Blob> = await this.api.get(`/cvs/${id}/pdf`, {
      responseType: 'blob',
    });
    return response.data;
  }

  async generatePublicPDF(slug: string): Promise<Blob> {
    const response: AxiosResponse<Blob> = await this.api.get(`/cv/public/${slug}/pdf`, {
      responseType: 'blob',
    });
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
