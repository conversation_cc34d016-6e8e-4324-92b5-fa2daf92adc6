{"version": 3, "file": "Connection.js", "sourceRoot": "", "sources": ["../../../../src/cdp/Connection.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAMH,OAAO,EACL,eAAe,GAGhB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAC,gBAAgB,EAAC,MAAM,+BAA+B,CAAC;AAE/D,OAAO,EAAC,KAAK,EAAC,MAAM,oBAAoB,CAAC;AACzC,OAAO,EAAC,gBAAgB,EAAC,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAC,0BAA0B,EAAC,MAAM,sBAAsB,CAAC;AAEhE,OAAO,EAAC,aAAa,EAAC,MAAM,iBAAiB,CAAC;AAE9C,MAAM,iBAAiB,GAAG,KAAK,CAAC,2BAA2B,CAAC,CAAC;AAC7D,MAAM,oBAAoB,GAAG,KAAK,CAAC,2BAA2B,CAAC,CAAC;AAOhE;;GAEG;AACH,MAAM,OAAO,UAAW,SAAQ,YAA8B;IAC5D,IAAI,CAAS;IACb,UAAU,CAAsB;IAChC,MAAM,CAAS;IACf,QAAQ,CAAS;IACjB,SAAS,GAAG,IAAI,GAAG,EAAyB,CAAC;IAC7C,OAAO,GAAG,KAAK,CAAC;IAChB,iBAAiB,GAAG,IAAI,GAAG,EAAU,CAAC;IACtC,UAAU,GAAG,IAAI,gBAAgB,EAAE,CAAC;IAEpC,YACE,GAAW,EACX,SAA8B,EAC9B,KAAK,GAAG,CAAC,EACT,OAAgB;QAEhB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,MAAO,CAAC;QAEnC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAAmB;QACpC,OAAO,OAAO,CAAC,UAAU,EAAE,CAAC;IAC9B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,OAAO,CAAC,SAAiB;QACvB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;IAC/C,CAAC;IAED,GAAG;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,IAAI,CACF,MAAS,EACT,MAAqD,EACrD,OAAwB;QAExB,2EAA2E;QAC3E,0CAA0C;QAC1C,sFAAsF;QACtF,yEAAyE;QACzE,kBAAkB;QAClB,iFAAiF;QACjF,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,QAAQ,CACN,SAA2B,EAC3B,MAAS,EACT,MAAoD,EACpD,SAAkB,EAClB,OAAwB;QAExB,OAAO,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE;YACtE,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC;gBACxC,MAAM;gBACN,MAAM;gBACN,EAAE;gBACF,SAAS;aACV,CAAC,CAAC;YACH,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC3C,CAAC,CAAuD,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,SAAS,CAAC,OAAe;QACvC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;gBACpB,OAAO,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;QACL,CAAC;QACD,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACnC,IAAI,MAAM,CAAC,MAAM,KAAK,yBAAyB,EAAE,CAAC;YAChD,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;YAC1C,MAAM,OAAO,GAAG,IAAI,aAAa,CAC/B,IAAI,EACJ,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAC7B,SAAS,EACT,MAAM,CAAC,SAAS,CACjB,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YACpD,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC3D,IAAI,aAAa,EAAE,CAAC;gBAClB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,CAAC,MAAM,KAAK,2BAA2B,EAAE,CAAC;YACzD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC5D,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC/C,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;gBACpD,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC3D,IAAI,aAAa,EAAE,CAAC;oBAClB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACrD,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,CAAC,EAAE,EAAE,CAAC;YACrB,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjB,IAAI,CAAC,UAAU,CAAC,MAAM,CACpB,MAAM,CAAC,EAAE,EACT,0BAA0B,CAAC,MAAM,CAAC,EAClC,MAAM,CAAC,KAAK,CAAC,OAAO,CACrB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,QAAQ;QACN,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,SAAS,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QACxB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,OAAO,CAAC,SAAS,EAAE,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IACrD,CAAC;IAED,OAAO;QACL,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAAgB;QAC7B,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,UAAsC,EACtC,oBAAoB,GAAG,IAAI;QAE3B,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC;QACD,MAAM,EAAC,SAAS,EAAC,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC3D,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa,CACjB,UAAsC;QAEtC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,wBAAwB;QACtB,MAAM,MAAM,GAAY,EAAE,CAAC;QAC3B,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE,CAAC,CAAC;QAC3D,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,wBAAwB,EAAE,CAAC,CAAC;QACrD,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,KAAY;IAC9C,OAAO,KAAK,YAAY,gBAAgB,CAAC;AAC3C,CAAC"}