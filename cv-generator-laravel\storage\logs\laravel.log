[2025-06-30 11:59:45] local.ERROR: Class "finfo" not found {"exception":"[object] (Error(code: 0): Class \"finfo\" not found at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\league\\mime-type-detection\\src\\FinfoMimeTypeDetector.php:48)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\league\\flysystem-local\\LocalFilesystemAdapter.php(86): League\\MimeTypeDetection\\FinfoMimeTypeDetector->__construct()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(295): League\\Flysystem\\Local\\LocalFilesystemAdapter->__construct()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(244): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishDirectory()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(207): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishItem()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(102): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishTag()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\VendorPublishCommand->handle()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#19 {main}
"} 
[2025-06-30 15:32:35] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (auth_token, 6a237a4d6186f341c278dc388347f8d9db9f7c532a10f322682da9a9a64c4007, ["*"], ?, 1, App\Models\User, 2025-06-30 15:32:34, 2025-06-30 15:32:34)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (auth_token, 6a237a4d6186f341c278dc388347f8d9db9f7c532a10f322682da9a9a64c4007, [\"*\"], ?, 1, App\\Models\\User, 2025-06-30 15:32:34, 2025-06-30 15:32:34)) at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\app\\Http\\Controllers\\Api\\AuthController.php(41): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->register()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():25}()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#36 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#37 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#38 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#39 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#40 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#41 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#42 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#43 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#44 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#45 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#46 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#47 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#48 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#49 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#50 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#51 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#52 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#53 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#54 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#55 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#56 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#57 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\app\\Http\\Controllers\\Api\\AuthController.php(41): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->register()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->{closure:Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful::handle():25}()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#36 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#39 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#42 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#43 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#44 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#46 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#48 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#50 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#51 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#52 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#53 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#54 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#55 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#56 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#57 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#58 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#59 {main}
"} 
[2025-06-30 15:33:52] local.ERROR: Class "finfo" not found {"exception":"[object] (Error(code: 0): Class \"finfo\" not found at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\league\\mime-type-detection\\src\\FinfoMimeTypeDetector.php:48)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\league\\flysystem-local\\LocalFilesystemAdapter.php(86): League\\MimeTypeDetection\\FinfoMimeTypeDetector->__construct()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(295): League\\Flysystem\\Local\\LocalFilesystemAdapter->__construct()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(244): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishDirectory()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(207): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishItem()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(102): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishTag()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\VendorPublishCommand->handle()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#19 {main}
"} 
[2025-06-30 15:34:27] local.ERROR: SQLSTATE[HY000]: General error: 1 index personal_access_tokens_tokenable_type_tokenable_id_index already exists (Connection: sqlite, SQL: create index "personal_access_tokens_tokenable_type_tokenable_id_index" on "personal_access_tokens" ("tokenable_type", "tokenable_id")) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 index personal_access_tokens_tokenable_type_tokenable_id_index already exists (Connection: sqlite, SQL: create index \"personal_access_tokens_tokenable_type_tokenable_id_index\" on \"personal_access_tokens\" (\"tokenable_type\", \"tokenable_id\")) at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\database\\migrations\\2019_12_14_000001_create_personal_access_tokens_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runUp():250}()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 index personal_access_tokens_tokenable_type_tokenable_id_index already exists at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\database\\migrations\\2019_12_14_000001_create_personal_access_tokens_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runUp():250}()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#36 {main}
"} 
[2025-06-30 15:35:01] local.ERROR: SQLSTATE[HY000]: General error: 1 index personal_access_tokens_tokenable_type_tokenable_id_index already exists (Connection: sqlite, SQL: create index "personal_access_tokens_tokenable_type_tokenable_id_index" on "personal_access_tokens" ("tokenable_type", "tokenable_id")) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 index personal_access_tokens_tokenable_type_tokenable_id_index already exists (Connection: sqlite, SQL: create index \"personal_access_tokens_tokenable_type_tokenable_id_index\" on \"personal_access_tokens\" (\"tokenable_type\", \"tokenable_id\")) at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\database\\migrations\\2019_12_14_000001_create_personal_access_tokens_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runUp():250}()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#36 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#37 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#38 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#39 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#40 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#41 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#42 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#43 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#45 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 index personal_access_tokens_tokenable_type_tokenable_id_index already exists at C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\database\\migrations\\2019_12_14_000001_create_personal_access_tokens_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runUp():250}()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#36 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#37 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#38 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#39 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#40 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#41 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#42 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#43 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#44 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#45 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#46 C:\\Users\\<USER>\\Desktop\\Projects\\CV Generator\\cv-generator-laravel\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#47 {main}
"} 
