import { Router } from 'express';
import cvController from '../controllers/cvController';
import { authenticate, optionalAuth } from '../middleware/auth';
import { validate, cvSchema } from '../middleware/validation';
import <PERSON><PERSON> from 'joi';

const router = Router();

/**
 * @route   GET /api/cv
 * @desc    Get all CVs for authenticated user
 * @access  Private
 */
router.get('/', authenticate, cvController.getUserCVs);

/**
 * @route   GET /api/cv/stats
 * @desc    Get CV statistics for user
 * @access  Private
 */
router.get('/stats', authenticate, cvController.getCVStats);

/**
 * @route   GET /api/cv/:id
 * @desc    Get specific CV by ID
 * @access  Private
 */
router.get('/:id', authenticate, cvController.getCVById);

/**
 * @route   POST /api/cv
 * @desc    Create a new CV
 * @access  Private
 */
router.post('/', authenticate, validate(cvSchema), cvController.createCV);

/**
 * @route   PUT /api/cv/:id
 * @desc    Update an existing CV
 * @access  Private
 */
router.put('/:id', authenticate, validate(cvSchema), cvController.updateCV);

/**
 * @route   DELETE /api/cv/:id
 * @desc    Delete a CV
 * @access  Private
 */
router.delete('/:id', authenticate, cvController.deleteCV);

/**
 * @route   POST /api/cv/:id/duplicate
 * @desc    Duplicate a CV
 * @access  Private
 */
router.post('/:id/duplicate', authenticate, cvController.duplicateCV);

/**
 * @route   PUT /api/cv/:id/toggle-public
 * @desc    Toggle CV public status
 * @access  Private
 */
router.put('/:id/toggle-public', authenticate, cvController.togglePublicStatus);

/**
 * @route   GET /api/cv/public/:slug
 * @desc    Get public CV by slug
 * @access  Public
 */
router.get('/public/:slug', cvController.getPublicCV);

/**
 * @route   POST /api/cv/:id/export/pdf
 * @desc    Export CV as PDF
 * @access  Private
 */
router.post('/:id/export/pdf', 
  authenticate, 
  validate(Joi.object({
    format: Joi.string().valid('A4', 'Letter').default('A4'),
    template: Joi.string().valid('modern', 'classic', 'creative', 'minimal').optional(),
  })), 
  async (req, res) => {
    // PDF export will be implemented in the PDF generation task
    res.status(501).json({
      success: false,
      message: 'PDF export functionality coming soon',
    });
  }
);

export default router;
