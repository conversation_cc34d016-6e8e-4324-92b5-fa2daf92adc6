{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\cv-generator-react\\\\src\\\\components\\\\templates\\\\ClassicTemplate.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ClassicTemplate = ({\n  data\n}) => {\n  const {\n    personal_info,\n    education,\n    experience,\n    skills,\n    projects,\n    languages\n  } = data;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto bg-white shadow-lg print:shadow-none\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-b-4 border-gray-800 p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 mb-4\",\n          children: [personal_info.first_name, \" \", personal_info.last_name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap justify-center gap-6 text-gray-600\",\n          children: [personal_info.email && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"Email:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-1\",\n              children: personal_info.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 15\n          }, this), personal_info.phone && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"Phone:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-1\",\n              children: personal_info.phone\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 15\n          }, this), personal_info.address && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"Address:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-1\",\n              children: personal_info.address\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), (personal_info.website || personal_info.linkedin || personal_info.github) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap justify-center gap-4 mt-4\",\n          children: [personal_info.website && /*#__PURE__*/_jsxDEV(\"a\", {\n            href: personal_info.website,\n            className: \"text-gray-600 hover:text-gray-800 underline\",\n            children: \"Website\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 17\n          }, this), personal_info.linkedin && /*#__PURE__*/_jsxDEV(\"a\", {\n            href: personal_info.linkedin,\n            className: \"text-gray-600 hover:text-gray-800 underline\",\n            children: \"LinkedIn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 17\n          }, this), personal_info.github && /*#__PURE__*/_jsxDEV(\"a\", {\n            href: personal_info.github,\n            className: \"text-gray-600 hover:text-gray-800 underline\",\n            children: \"GitHub\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-8\",\n      children: [personal_info.summary && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-800 mb-3 uppercase tracking-wide border-b border-gray-300 pb-1\",\n          children: \"Professional Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700 leading-relaxed text-justify\",\n          children: personal_info.summary\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 11\n      }, this), experience.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1\",\n          children: \"Professional Experience\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: experience.map((exp, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-800\",\n                  children: exp.position\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 font-medium\",\n                  children: exp.company\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 font-medium\",\n                  children: [exp.start_date, \" - \", exp.current ? 'Present' : exp.end_date]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 19\n            }, this), exp.description && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 leading-relaxed text-justify\",\n              children: exp.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 21\n            }, this), index < experience.length - 1 && /*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"mt-4 border-gray-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 53\n            }, this)]\n          }, exp.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this), education.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1\",\n          children: \"Education\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: education.map((edu, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-lg font-semibold text-gray-800\",\n                  children: edu.degree\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 font-medium\",\n                  children: edu.institution\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: [edu.field_of_study, edu.gpa && ` • GPA: ${edu.gpa}`]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 font-medium\",\n                  children: [edu.start_date, \" - \", edu.current ? 'Present' : edu.end_date]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 19\n            }, this), edu.description && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 text-sm\",\n              children: edu.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 21\n            }, this), index < education.length - 1 && /*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"mt-3 border-gray-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 52\n            }, this)]\n          }, edu.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 11\n      }, this), skills.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1\",\n          children: \"Skills\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: skills.reduce((acc, skill, index) => {\n            const category = skill.category || 'General';\n            const existingCategory = acc.find(cat => cat.name === category);\n            if (existingCategory) {\n              existingCategory.skills.push(skill);\n            } else {\n              acc.push({\n                name: category,\n                skills: [skill]\n              });\n            }\n            return acc;\n          }, []).map((category, categoryIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-700 mb-2\",\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-1\",\n              children: category.skills.map((skill, skillIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-700\",\n                  children: skill.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                  children: skill.level\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 25\n                }, this)]\n              }, skillIndex, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 19\n            }, this)]\n          }, categoryIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this), projects.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1\",\n          children: \"Projects\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: projects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-800\",\n                children: project.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [project.url && /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: project.url,\n                  className: \"text-gray-600 hover:text-gray-800 text-sm underline\",\n                  children: \"Live Demo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 25\n                }, this), project.github_url && /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: project.github_url,\n                  className: \"text-gray-600 hover:text-gray-800 text-sm underline\",\n                  children: \"Source Code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 text-sm mb-2 text-justify\",\n              children: project.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this), project.technologies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Technologies: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-700\",\n                children: project.technologies.join(', ')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 21\n            }, this), index < projects.length - 1 && /*#__PURE__*/_jsxDEV(\"hr\", {\n              className: \"mt-3 border-gray-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 51\n            }, this)]\n          }, project.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this), languages.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1\",\n          children: \"Languages\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n          children: languages.map((language, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium text-gray-700\",\n              children: language.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: language.proficiency\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 19\n            }, this)]\n          }, language.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = ClassicTemplate;\nexport default ClassicTemplate;\nvar _c;\n$RefreshReg$(_c, \"ClassicTemplate\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ClassicTemplate", "data", "personal_info", "education", "experience", "skills", "projects", "languages", "className", "children", "first_name", "last_name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "phone", "address", "website", "linkedin", "github", "href", "summary", "length", "map", "exp", "index", "position", "company", "start_date", "current", "end_date", "description", "id", "edu", "degree", "institution", "field_of_study", "gpa", "reduce", "acc", "skill", "category", "existingCategory", "find", "cat", "name", "push", "categoryIndex", "skillIndex", "level", "project", "url", "github_url", "technologies", "join", "language", "proficiency", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/cv-generator-react/src/components/templates/ClassicTemplate.tsx"], "sourcesContent": ["import React from 'react';\nimport { CVData } from '../../types';\n\ninterface ClassicTemplateProps {\n  data: CVData;\n}\n\nconst ClassicTemplate: React.FC<ClassicTemplateProps> = ({ data }) => {\n  const { personal_info, education, experience, skills, projects, languages } = data;\n\n  return (\n    <div className=\"max-w-4xl mx-auto bg-white shadow-lg print:shadow-none\">\n      {/* Header */}\n      <div className=\"border-b-4 border-gray-800 p-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl font-bold text-gray-800 mb-4\">\n            {personal_info.first_name} {personal_info.last_name}\n          </h1>\n          <div className=\"flex flex-wrap justify-center gap-6 text-gray-600\">\n            {personal_info.email && (\n              <div className=\"flex items-center\">\n                <span className=\"font-medium\">Email:</span>\n                <span className=\"ml-1\">{personal_info.email}</span>\n              </div>\n            )}\n            {personal_info.phone && (\n              <div className=\"flex items-center\">\n                <span className=\"font-medium\">Phone:</span>\n                <span className=\"ml-1\">{personal_info.phone}</span>\n              </div>\n            )}\n            {personal_info.address && (\n              <div className=\"flex items-center\">\n                <span className=\"font-medium\">Address:</span>\n                <span className=\"ml-1\">{personal_info.address}</span>\n              </div>\n            )}\n          </div>\n          {(personal_info.website || personal_info.linkedin || personal_info.github) && (\n            <div className=\"flex flex-wrap justify-center gap-4 mt-4\">\n              {personal_info.website && (\n                <a href={personal_info.website} className=\"text-gray-600 hover:text-gray-800 underline\">\n                  Website\n                </a>\n              )}\n              {personal_info.linkedin && (\n                <a href={personal_info.linkedin} className=\"text-gray-600 hover:text-gray-800 underline\">\n                  LinkedIn\n                </a>\n              )}\n              {personal_info.github && (\n                <a href={personal_info.github} className=\"text-gray-600 hover:text-gray-800 underline\">\n                  GitHub\n                </a>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n\n      <div className=\"p-8\">\n        {/* Professional Summary */}\n        {personal_info.summary && (\n          <section className=\"mb-8\">\n            <h2 className=\"text-xl font-bold text-gray-800 mb-3 uppercase tracking-wide border-b border-gray-300 pb-1\">\n              Professional Summary\n            </h2>\n            <p className=\"text-gray-700 leading-relaxed text-justify\">{personal_info.summary}</p>\n          </section>\n        )}\n\n        {/* Experience */}\n        {experience.length > 0 && (\n          <section className=\"mb-8\">\n            <h2 className=\"text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1\">\n              Professional Experience\n            </h2>\n            <div className=\"space-y-6\">\n              {experience.map((exp, index) => (\n                <div key={exp.id}>\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-800\">{exp.position}</h3>\n                      <p className=\"text-gray-600 font-medium\">{exp.company}</p>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-sm text-gray-600 font-medium\">\n                        {exp.start_date} - {exp.current ? 'Present' : exp.end_date}\n                      </p>\n                    </div>\n                  </div>\n                  {exp.description && (\n                    <p className=\"text-gray-700 leading-relaxed text-justify\">{exp.description}</p>\n                  )}\n                  {index < experience.length - 1 && <hr className=\"mt-4 border-gray-200\" />}\n                </div>\n              ))}\n            </div>\n          </section>\n        )}\n\n        {/* Education */}\n        {education.length > 0 && (\n          <section className=\"mb-8\">\n            <h2 className=\"text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1\">\n              Education\n            </h2>\n            <div className=\"space-y-4\">\n              {education.map((edu, index) => (\n                <div key={edu.id}>\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-800\">{edu.degree}</h3>\n                      <p className=\"text-gray-600 font-medium\">{edu.institution}</p>\n                      <p className=\"text-sm text-gray-600\">\n                        {edu.field_of_study}\n                        {edu.gpa && ` • GPA: ${edu.gpa}`}\n                      </p>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-sm text-gray-600 font-medium\">\n                        {edu.start_date} - {edu.current ? 'Present' : edu.end_date}\n                      </p>\n                    </div>\n                  </div>\n                  {edu.description && (\n                    <p className=\"text-gray-700 text-sm\">{edu.description}</p>\n                  )}\n                  {index < education.length - 1 && <hr className=\"mt-3 border-gray-200\" />}\n                </div>\n              ))}\n            </div>\n          </section>\n        )}\n\n        {/* Skills */}\n        {skills.length > 0 && (\n          <section className=\"mb-8\">\n            <h2 className=\"text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1\">\n              Skills\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {skills.reduce((acc: any[], skill, index) => {\n                const category = skill.category || 'General';\n                const existingCategory = acc.find(cat => cat.name === category);\n                \n                if (existingCategory) {\n                  existingCategory.skills.push(skill);\n                } else {\n                  acc.push({ name: category, skills: [skill] });\n                }\n                \n                return acc;\n              }, []).map((category, categoryIndex) => (\n                <div key={categoryIndex}>\n                  <h3 className=\"font-semibold text-gray-700 mb-2\">{category.name}</h3>\n                  <div className=\"space-y-1\">\n                    {category.skills.map((skill: any, skillIndex: number) => (\n                      <div key={skillIndex} className=\"flex justify-between items-center\">\n                        <span className=\"text-sm text-gray-700\">{skill.name}</span>\n                        <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n                          {skill.level}\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </section>\n        )}\n\n        {/* Projects */}\n        {projects.length > 0 && (\n          <section className=\"mb-8\">\n            <h2 className=\"text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1\">\n              Projects\n            </h2>\n            <div className=\"space-y-4\">\n              {projects.map((project, index) => (\n                <div key={project.id}>\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <h3 className=\"text-lg font-semibold text-gray-800\">{project.name}</h3>\n                    <div className=\"flex space-x-2\">\n                      {project.url && (\n                        <a href={project.url} className=\"text-gray-600 hover:text-gray-800 text-sm underline\">\n                          Live Demo\n                        </a>\n                      )}\n                      {project.github_url && (\n                        <a href={project.github_url} className=\"text-gray-600 hover:text-gray-800 text-sm underline\">\n                          Source Code\n                        </a>\n                      )}\n                    </div>\n                  </div>\n                  <p className=\"text-gray-700 text-sm mb-2 text-justify\">{project.description}</p>\n                  {project.technologies.length > 0 && (\n                    <div>\n                      <span className=\"text-sm font-medium text-gray-600\">Technologies: </span>\n                      <span className=\"text-sm text-gray-700\">{project.technologies.join(', ')}</span>\n                    </div>\n                  )}\n                  {index < projects.length - 1 && <hr className=\"mt-3 border-gray-200\" />}\n                </div>\n              ))}\n            </div>\n          </section>\n        )}\n\n        {/* Languages */}\n        {languages.length > 0 && (\n          <section className=\"mb-8\">\n            <h2 className=\"text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1\">\n              Languages\n            </h2>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              {languages.map((language, index) => (\n                <div key={language.id} className=\"text-center\">\n                  <div className=\"font-medium text-gray-700\">{language.name}</div>\n                  <div className=\"text-sm text-gray-500\">{language.proficiency}</div>\n                </div>\n              ))}\n            </div>\n          </section>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ClassicTemplate;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO1B,MAAMC,eAA+C,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EACpE,MAAM;IAAEC,aAAa;IAAEC,SAAS;IAAEC,UAAU;IAAEC,MAAM;IAAEC,QAAQ;IAAEC;EAAU,CAAC,GAAGN,IAAI;EAElF,oBACEF,OAAA;IAAKS,SAAS,EAAC,wDAAwD;IAAAC,QAAA,gBAErEV,OAAA;MAAKS,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7CV,OAAA;QAAKS,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BV,OAAA;UAAIS,SAAS,EAAC,uCAAuC;UAAAC,QAAA,GAClDP,aAAa,CAACQ,UAAU,EAAC,GAAC,EAACR,aAAa,CAACS,SAAS;QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACLhB,OAAA;UAAKS,SAAS,EAAC,mDAAmD;UAAAC,QAAA,GAC/DP,aAAa,CAACc,KAAK,iBAClBjB,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAMS,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3ChB,OAAA;cAAMS,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAEP,aAAa,CAACc;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CACN,EACAb,aAAa,CAACe,KAAK,iBAClBlB,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAMS,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3ChB,OAAA;cAAMS,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAEP,aAAa,CAACe;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CACN,EACAb,aAAa,CAACgB,OAAO,iBACpBnB,OAAA;YAAKS,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCV,OAAA;cAAMS,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7ChB,OAAA;cAAMS,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAEP,aAAa,CAACgB;YAAO;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EACL,CAACb,aAAa,CAACiB,OAAO,IAAIjB,aAAa,CAACkB,QAAQ,IAAIlB,aAAa,CAACmB,MAAM,kBACvEtB,OAAA;UAAKS,SAAS,EAAC,0CAA0C;UAAAC,QAAA,GACtDP,aAAa,CAACiB,OAAO,iBACpBpB,OAAA;YAAGuB,IAAI,EAAEpB,aAAa,CAACiB,OAAQ;YAACX,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EAAC;UAExF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ,EACAb,aAAa,CAACkB,QAAQ,iBACrBrB,OAAA;YAAGuB,IAAI,EAAEpB,aAAa,CAACkB,QAAS;YAACZ,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EAAC;UAEzF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ,EACAb,aAAa,CAACmB,MAAM,iBACnBtB,OAAA;YAAGuB,IAAI,EAAEpB,aAAa,CAACmB,MAAO;YAACb,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EAAC;UAEvF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhB,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAC,QAAA,GAEjBP,aAAa,CAACqB,OAAO,iBACpBxB,OAAA;QAASS,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACvBV,OAAA;UAAIS,SAAS,EAAC,4FAA4F;UAAAC,QAAA,EAAC;QAE3G;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhB,OAAA;UAAGS,SAAS,EAAC,4CAA4C;UAAAC,QAAA,EAAEP,aAAa,CAACqB;QAAO;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CACV,EAGAX,UAAU,CAACoB,MAAM,GAAG,CAAC,iBACpBzB,OAAA;QAASS,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACvBV,OAAA;UAAIS,SAAS,EAAC,4FAA4F;UAAAC,QAAA,EAAC;QAE3G;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhB,OAAA;UAAKS,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBL,UAAU,CAACqB,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACzB5B,OAAA;YAAAU,QAAA,gBACEV,OAAA;cAAKS,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDV,OAAA;gBAAAU,QAAA,gBACEV,OAAA;kBAAIS,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAEiB,GAAG,CAACE;gBAAQ;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvEhB,OAAA;kBAAGS,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAEiB,GAAG,CAACG;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNhB,OAAA;gBAAKS,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBV,OAAA;kBAAGS,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAC7CiB,GAAG,CAACI,UAAU,EAAC,KAAG,EAACJ,GAAG,CAACK,OAAO,GAAG,SAAS,GAAGL,GAAG,CAACM,QAAQ;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLW,GAAG,CAACO,WAAW,iBACdlC,OAAA;cAAGS,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EAAEiB,GAAG,CAACO;YAAW;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC/E,EACAY,KAAK,GAAGvB,UAAU,CAACoB,MAAM,GAAG,CAAC,iBAAIzB,OAAA;cAAIS,SAAS,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAfjEW,GAAG,CAACQ,EAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV,EAGAZ,SAAS,CAACqB,MAAM,GAAG,CAAC,iBACnBzB,OAAA;QAASS,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACvBV,OAAA;UAAIS,SAAS,EAAC,4FAA4F;UAAAC,QAAA,EAAC;QAE3G;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhB,OAAA;UAAKS,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBN,SAAS,CAACsB,GAAG,CAAC,CAACU,GAAG,EAAER,KAAK,kBACxB5B,OAAA;YAAAU,QAAA,gBACEV,OAAA;cAAKS,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDV,OAAA;gBAAAU,QAAA,gBACEV,OAAA;kBAAIS,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAE0B,GAAG,CAACC;gBAAM;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrEhB,OAAA;kBAAGS,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAE0B,GAAG,CAACE;gBAAW;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9DhB,OAAA;kBAAGS,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GACjC0B,GAAG,CAACG,cAAc,EAClBH,GAAG,CAACI,GAAG,IAAI,WAAWJ,GAAG,CAACI,GAAG,EAAE;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNhB,OAAA;gBAAKS,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBV,OAAA;kBAAGS,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAC7C0B,GAAG,CAACL,UAAU,EAAC,KAAG,EAACK,GAAG,CAACJ,OAAO,GAAG,SAAS,GAAGI,GAAG,CAACH,QAAQ;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLoB,GAAG,CAACF,WAAW,iBACdlC,OAAA;cAAGS,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAE0B,GAAG,CAACF;YAAW;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC1D,EACAY,KAAK,GAAGxB,SAAS,CAACqB,MAAM,GAAG,CAAC,iBAAIzB,OAAA;cAAIS,SAAS,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAnBhEoB,GAAG,CAACD,EAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBX,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV,EAGAV,MAAM,CAACmB,MAAM,GAAG,CAAC,iBAChBzB,OAAA;QAASS,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACvBV,OAAA;UAAIS,SAAS,EAAC,4FAA4F;UAAAC,QAAA,EAAC;QAE3G;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhB,OAAA;UAAKS,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDJ,MAAM,CAACmC,MAAM,CAAC,CAACC,GAAU,EAAEC,KAAK,EAAEf,KAAK,KAAK;YAC3C,MAAMgB,QAAQ,GAAGD,KAAK,CAACC,QAAQ,IAAI,SAAS;YAC5C,MAAMC,gBAAgB,GAAGH,GAAG,CAACI,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAKJ,QAAQ,CAAC;YAE/D,IAAIC,gBAAgB,EAAE;cACpBA,gBAAgB,CAACvC,MAAM,CAAC2C,IAAI,CAACN,KAAK,CAAC;YACrC,CAAC,MAAM;cACLD,GAAG,CAACO,IAAI,CAAC;gBAAED,IAAI,EAAEJ,QAAQ;gBAAEtC,MAAM,EAAE,CAACqC,KAAK;cAAE,CAAC,CAAC;YAC/C;YAEA,OAAOD,GAAG;UACZ,CAAC,EAAE,EAAE,CAAC,CAAChB,GAAG,CAAC,CAACkB,QAAQ,EAAEM,aAAa,kBACjClD,OAAA;YAAAU,QAAA,gBACEV,OAAA;cAAIS,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAEkC,QAAQ,CAACI;YAAI;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrEhB,OAAA;cAAKS,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBkC,QAAQ,CAACtC,MAAM,CAACoB,GAAG,CAAC,CAACiB,KAAU,EAAEQ,UAAkB,kBAClDnD,OAAA;gBAAsBS,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBACjEV,OAAA;kBAAMS,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEiC,KAAK,CAACK;gBAAI;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3DhB,OAAA;kBAAMS,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAClEiC,KAAK,CAACS;gBAAK;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA,GAJCmC,UAAU;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAXEkC,aAAa;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYlB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV,EAGAT,QAAQ,CAACkB,MAAM,GAAG,CAAC,iBAClBzB,OAAA;QAASS,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACvBV,OAAA;UAAIS,SAAS,EAAC,4FAA4F;UAAAC,QAAA,EAAC;QAE3G;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhB,OAAA;UAAKS,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBH,QAAQ,CAACmB,GAAG,CAAC,CAAC2B,OAAO,EAAEzB,KAAK,kBAC3B5B,OAAA;YAAAU,QAAA,gBACEV,OAAA;cAAKS,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDV,OAAA;gBAAIS,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAE2C,OAAO,CAACL;cAAI;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvEhB,OAAA;gBAAKS,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAC5B2C,OAAO,CAACC,GAAG,iBACVtD,OAAA;kBAAGuB,IAAI,EAAE8B,OAAO,CAACC,GAAI;kBAAC7C,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAAC;gBAEtF;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ,EACAqC,OAAO,CAACE,UAAU,iBACjBvD,OAAA;kBAAGuB,IAAI,EAAE8B,OAAO,CAACE,UAAW;kBAAC9C,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAAC;gBAE7F;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhB,OAAA;cAAGS,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAE2C,OAAO,CAACnB;YAAW;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC/EqC,OAAO,CAACG,YAAY,CAAC/B,MAAM,GAAG,CAAC,iBAC9BzB,OAAA;cAAAU,QAAA,gBACEV,OAAA;gBAAMS,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzEhB,OAAA;gBAAMS,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAE2C,OAAO,CAACG,YAAY,CAACC,IAAI,CAAC,IAAI;cAAC;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CACN,EACAY,KAAK,GAAGrB,QAAQ,CAACkB,MAAM,GAAG,CAAC,iBAAIzB,OAAA;cAAIS,SAAS,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAvB/DqC,OAAO,CAAClB,EAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV,EAGAR,SAAS,CAACiB,MAAM,GAAG,CAAC,iBACnBzB,OAAA;QAASS,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACvBV,OAAA;UAAIS,SAAS,EAAC,4FAA4F;UAAAC,QAAA,EAAC;QAE3G;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhB,OAAA;UAAKS,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDF,SAAS,CAACkB,GAAG,CAAC,CAACgC,QAAQ,EAAE9B,KAAK,kBAC7B5B,OAAA;YAAuBS,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC5CV,OAAA;cAAKS,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEgD,QAAQ,CAACV;YAAI;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChEhB,OAAA;cAAKS,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAEgD,QAAQ,CAACC;YAAW;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAF3D0C,QAAQ,CAACvB,EAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGhB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC4C,EAAA,GA9NI3D,eAA+C;AAgOrD,eAAeA,eAAe;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}