{"version": 3, "file": "Connection.d.ts", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/Connection.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,KAAK,IAAI,MAAM,4CAA4C,CAAC;AAExE,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAC;AAE/D;;GAEG;AACH,MAAM,WAAW,QAAQ;IACvB,iBAAiB,EAAE;QACjB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;QACvC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;KACxC,CAAC;IACF,qBAAqB,EAAE;QACrB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;QAC3C,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;KACxC,CAAC;IACF,eAAe,EAAE;QACf,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACrC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;IACF,yBAAyB,EAAE;QACzB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,0BAA0B,CAAC;QAC/C,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;KAChD,CAAC;IACF,4BAA4B,EAAE;QAC5B,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,6BAA6B,CAAC;QAClD,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;IAEF,eAAe,EAAE;QACf,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;QACzB,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;IAEF,2BAA2B,EAAE;QAC3B,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;QACzB,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC;KAClD,CAAC;IACF,yBAAyB,EAAE;QACzB,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;QACzB,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC;KAChD,CAAC;IACF,2BAA2B,EAAE;QAC3B,MAAM,EAAE;YACN,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;SACvC,CAAC;QACF,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;KAC5C,CAAC;IAEF,0BAA0B,EAAE;QAC1B,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC;QAChD,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;IACF,wBAAwB,EAAE;QACxB,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;QAC9C,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;KAC/C,CAAC;IACF,uBAAuB,EAAE;QACvB,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;QAC7C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;IACF,yBAAyB,EAAE;QACzB,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC;QAC/C,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;KAChD,CAAC;IACF,0BAA0B,EAAE;QAC1B,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC;QAChD,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;KACjD,CAAC;IACF,wBAAwB,EAAE;QACxB,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;QAC9C,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;KACjD,CAAC;IACF,uBAAuB,EAAE;QACvB,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC;QAC7C,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;KAC9C,CAAC;IACF,mCAAmC,EAAE;QACnC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,2BAA2B,CAAC;QACzD,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,uBAAuB,CAAC;KAC1D,CAAC;IACF,kCAAkC,EAAE;QAClC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,0BAA0B,CAAC;QACxD,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;IACF,6BAA6B,EAAE;QAC7B,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,qBAAqB,CAAC;QACnD,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;IACF,iCAAiC,EAAE;QACjC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC;QACvD,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;IAEF,sBAAsB,EAAE;QACtB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC;QAC5C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;IACF,sBAAsB,EAAE;QACtB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC;QAC5C,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;IAEF,aAAa,EAAE;QACb,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;QACzB,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;IACF,aAAa,EAAE;QACb,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;QACnC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;KACpC,CAAC;IACF,gBAAgB,EAAE;QAChB,MAAM,EAAE,MAAM,CAAC;QACf,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;KACvC,CAAC;IACF,mBAAmB,EAAE;QACnB,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;QACzC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;IACF,qBAAqB,EAAE;QACrB,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;QACzC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC;KAC9B,CAAC;CACH;AAED;;GAEG;AACH,MAAM,MAAM,UAAU,GAAG;KACtB,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,OAAO,CAC/C,IAAI,CAAC,YAAY,CAAC,KAAK,EACvB;QAAC,MAAM,EAAE,CAAC,CAAA;KAAC,CACZ,CAAC,QAAQ,CAAC;CACZ,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,UAAU,CAAC,MAAM,SAAS,UAAU,GAAG,UAAU,CAChE,SAAQ,YAAY,CAAC,MAAM,CAAC;IAC5B,IAAI,CAAC,CAAC,SAAS,MAAM,QAAQ,EAC3B,MAAM,EAAE,CAAC,EACT,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAC5B,OAAO,CAAC;QAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAA;KAAC,CAAC,CAAC;IAGhD,MAAM,CAAC,MAAM,SAAS,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;CACxE"}