import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { CV, CVFormData, CVContextType } from '../types';
import { cvAPI, handleApiError } from '../services/api';
import { toast } from 'react-hot-toast';

// CV state interface
interface CVState {
  cvs: CV[];
  currentCV: CV | null;
  isLoading: boolean;
  error: string | null;
}

// CV actions
type CVAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_CVS'; payload: CV[] }
  | { type: 'SET_CURRENT_CV'; payload: CV | null }
  | { type: 'ADD_CV'; payload: CV }
  | { type: 'UPDATE_CV'; payload: CV }
  | { type: 'REMOVE_CV'; payload: number }
  | { type: 'CLEAR_CVS' };

// Initial state
const initialState: CVState = {
  cvs: [],
  currentCV: null,
  isLoading: false,
  error: null,
};

// CV reducer
const cvReducer = (state: CVState, action: CVAction): CVState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'SET_CVS':
      return {
        ...state,
        cvs: action.payload,
        isLoading: false,
        error: null,
      };
    case 'SET_CURRENT_CV':
      return {
        ...state,
        currentCV: action.payload,
      };
    case 'ADD_CV':
      return {
        ...state,
        cvs: [action.payload, ...state.cvs],
        isLoading: false,
        error: null,
      };
    case 'UPDATE_CV':
      return {
        ...state,
        cvs: state.cvs.map(cv => 
          cv.id === action.payload.id ? action.payload : cv
        ),
        currentCV: state.currentCV?.id === action.payload.id ? action.payload : state.currentCV,
        isLoading: false,
        error: null,
      };
    case 'REMOVE_CV':
      return {
        ...state,
        cvs: state.cvs.filter(cv => cv.id !== action.payload),
        currentCV: state.currentCV?.id === action.payload ? null : state.currentCV,
        isLoading: false,
        error: null,
      };
    case 'CLEAR_CVS':
      return {
        ...initialState,
      };
    default:
      return state;
  }
};

// Create context
const CVContext = createContext<CVContextType | undefined>(undefined);

// CV provider component
interface CVProviderProps {
  children: ReactNode;
}

export const CVProvider: React.FC<CVProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(cvReducer, initialState);

  // Fetch all CVs
  const fetchCVs = async (): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });
      
      const response = await cvAPI.getCVs();
      dispatch({ type: 'SET_CVS', payload: response.data || [] });
    } catch (error) {
      const errorMessage = handleApiError(error);
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast.error(errorMessage);
    }
  };

  // Create new CV
  const createCV = async (data: CVFormData): Promise<CV> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });
      
      const newCV = await cvAPI.createCV(data);
      dispatch({ type: 'ADD_CV', payload: newCV });
      
      toast.success('CV created successfully!');
      return newCV;
    } catch (error) {
      const errorMessage = handleApiError(error);
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  // Update existing CV
  const updateCV = async (id: number, data: Partial<CVFormData>): Promise<CV> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });
      
      const updatedCV = await cvAPI.updateCV(id, data);
      dispatch({ type: 'UPDATE_CV', payload: updatedCV });
      
      toast.success('CV updated successfully!');
      return updatedCV;
    } catch (error) {
      const errorMessage = handleApiError(error);
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  // Delete CV
  const deleteCV = async (id: number): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });
      
      await cvAPI.deleteCV(id);
      dispatch({ type: 'REMOVE_CV', payload: id });
      
      toast.success('CV deleted successfully!');
    } catch (error) {
      const errorMessage = handleApiError(error);
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  // Duplicate CV
  const duplicateCV = async (id: number): Promise<CV> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });
      
      const duplicatedCV = await cvAPI.duplicateCV(id);
      dispatch({ type: 'ADD_CV', payload: duplicatedCV });
      
      toast.success('CV duplicated successfully!');
      return duplicatedCV;
    } catch (error) {
      const errorMessage = handleApiError(error);
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  };

  // Set current CV
  const setCurrentCV = (cv: CV | null): void => {
    dispatch({ type: 'SET_CURRENT_CV', payload: cv });
  };

  // Context value
  const value: CVContextType = {
    cvs: state.cvs,
    currentCV: state.currentCV,
    isLoading: state.isLoading,
    error: state.error,
    fetchCVs,
    createCV,
    updateCV,
    deleteCV,
    duplicateCV,
    setCurrentCV,
  };

  return (
    <CVContext.Provider value={value}>
      {children}
    </CVContext.Provider>
  );
};

// Custom hook to use CV context
export const useCV = (): CVContextType => {
  const context = useContext(CVContext);
  if (context === undefined) {
    throw new Error('useCV must be used within a CVProvider');
  }
  return context;
};

export default CVContext;
