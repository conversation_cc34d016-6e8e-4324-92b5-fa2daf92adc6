import React from 'react';
import { CVData } from '../../types';

interface ClassicTemplateProps {
  data: CVData;
}

const ClassicTemplate: React.FC<ClassicTemplateProps> = ({ data }) => {
  const { personal_info, education, experience, skills, projects, languages } = data;

  return (
    <div className="max-w-4xl mx-auto bg-white shadow-lg print:shadow-none">
      {/* Header */}
      <div className="border-b-4 border-gray-800 p-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            {personal_info.first_name} {personal_info.last_name}
          </h1>
          <div className="flex flex-wrap justify-center gap-6 text-gray-600">
            {personal_info.email && (
              <div className="flex items-center">
                <span className="font-medium">Email:</span>
                <span className="ml-1">{personal_info.email}</span>
              </div>
            )}
            {personal_info.phone && (
              <div className="flex items-center">
                <span className="font-medium">Phone:</span>
                <span className="ml-1">{personal_info.phone}</span>
              </div>
            )}
            {personal_info.address && (
              <div className="flex items-center">
                <span className="font-medium">Address:</span>
                <span className="ml-1">{personal_info.address}</span>
              </div>
            )}
          </div>
          {(personal_info.website || personal_info.linkedin || personal_info.github) && (
            <div className="flex flex-wrap justify-center gap-4 mt-4">
              {personal_info.website && (
                <a href={personal_info.website} className="text-gray-600 hover:text-gray-800 underline">
                  Website
                </a>
              )}
              {personal_info.linkedin && (
                <a href={personal_info.linkedin} className="text-gray-600 hover:text-gray-800 underline">
                  LinkedIn
                </a>
              )}
              {personal_info.github && (
                <a href={personal_info.github} className="text-gray-600 hover:text-gray-800 underline">
                  GitHub
                </a>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="p-8">
        {/* Professional Summary */}
        {personal_info.summary && (
          <section className="mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-3 uppercase tracking-wide border-b border-gray-300 pb-1">
              Professional Summary
            </h2>
            <p className="text-gray-700 leading-relaxed text-justify">{personal_info.summary}</p>
          </section>
        )}

        {/* Experience */}
        {experience.length > 0 && (
          <section className="mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1">
              Professional Experience
            </h2>
            <div className="space-y-6">
              {experience.map((exp, index) => (
                <div key={exp.id}>
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">{exp.position}</h3>
                      <p className="text-gray-600 font-medium">{exp.company}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600 font-medium">
                        {exp.start_date} - {exp.current ? 'Present' : exp.end_date}
                      </p>
                    </div>
                  </div>
                  {exp.description && (
                    <p className="text-gray-700 leading-relaxed text-justify">{exp.description}</p>
                  )}
                  {index < experience.length - 1 && <hr className="mt-4 border-gray-200" />}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Education */}
        {education.length > 0 && (
          <section className="mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1">
              Education
            </h2>
            <div className="space-y-4">
              {education.map((edu, index) => (
                <div key={edu.id}>
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800">{edu.degree}</h3>
                      <p className="text-gray-600 font-medium">{edu.institution}</p>
                      <p className="text-sm text-gray-600">
                        {edu.field_of_study}
                        {edu.gpa && ` • GPA: ${edu.gpa}`}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600 font-medium">
                        {edu.start_date} - {edu.current ? 'Present' : edu.end_date}
                      </p>
                    </div>
                  </div>
                  {edu.description && (
                    <p className="text-gray-700 text-sm">{edu.description}</p>
                  )}
                  {index < education.length - 1 && <hr className="mt-3 border-gray-200" />}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills */}
        {skills.length > 0 && (
          <section className="mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1">
              Skills
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {skills.reduce((acc: any[], skill, index) => {
                const category = skill.category || 'General';
                const existingCategory = acc.find(cat => cat.name === category);
                
                if (existingCategory) {
                  existingCategory.skills.push(skill);
                } else {
                  acc.push({ name: category, skills: [skill] });
                }
                
                return acc;
              }, []).map((category, categoryIndex) => (
                <div key={categoryIndex}>
                  <h3 className="font-semibold text-gray-700 mb-2">{category.name}</h3>
                  <div className="space-y-1">
                    {category.skills.map((skill: any, skillIndex: number) => (
                      <div key={skillIndex} className="flex justify-between items-center">
                        <span className="text-sm text-gray-700">{skill.name}</span>
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {skill.level}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Projects */}
        {projects.length > 0 && (
          <section className="mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1">
              Projects
            </h2>
            <div className="space-y-4">
              {projects.map((project, index) => (
                <div key={project.id}>
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-semibold text-gray-800">{project.name}</h3>
                    <div className="flex space-x-2">
                      {project.url && (
                        <a href={project.url} className="text-gray-600 hover:text-gray-800 text-sm underline">
                          Live Demo
                        </a>
                      )}
                      {project.github_url && (
                        <a href={project.github_url} className="text-gray-600 hover:text-gray-800 text-sm underline">
                          Source Code
                        </a>
                      )}
                    </div>
                  </div>
                  <p className="text-gray-700 text-sm mb-2 text-justify">{project.description}</p>
                  {project.technologies.length > 0 && (
                    <div>
                      <span className="text-sm font-medium text-gray-600">Technologies: </span>
                      <span className="text-sm text-gray-700">{project.technologies.join(', ')}</span>
                    </div>
                  )}
                  {index < projects.length - 1 && <hr className="mt-3 border-gray-200" />}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Languages */}
        {languages.length > 0 && (
          <section className="mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-4 uppercase tracking-wide border-b border-gray-300 pb-1">
              Languages
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {languages.map((language, index) => (
                <div key={language.id} className="text-center">
                  <div className="font-medium text-gray-700">{language.name}</div>
                  <div className="text-sm text-gray-500">{language.proficiency}</div>
                </div>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
};

export default ClassicTemplate;
