import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { 
  ApiResponse, 
  PaginatedResponse, 
  User, 
  CV, 
  LoginCredentials, 
  RegisterData, 
  AuthResponse,
  CVFormData 
} from '../types';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  // Register new user
  register: async (data: RegisterData): Promise<AuthResponse> => {
    const response = await api.post<ApiResponse<AuthResponse>>('/auth/register', data);
    return response.data.data!;
  },

  // Login user
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await api.post<ApiResponse<AuthResponse>>('/auth/login', credentials);
    return response.data.data!;
  },

  // Logout user
  logout: async (): Promise<void> => {
    await api.post('/auth/logout');
  },

  // Get current user profile
  getProfile: async (): Promise<User> => {
    const response = await api.get<ApiResponse<{ user: User }>>('/auth/profile');
    return response.data.data!.user;
  },

  // Update user profile
  updateProfile: async (data: Partial<User>): Promise<User> => {
    const response = await api.put<ApiResponse<{ user: User }>>('/auth/profile', data);
    return response.data.data!.user;
  },

  // Change password
  changePassword: async (currentPassword: string, newPassword: string): Promise<void> => {
    await api.put('/auth/change-password', {
      currentPassword,
      newPassword,
    });
  },

  // Delete account
  deleteAccount: async (password: string): Promise<void> => {
    await api.delete('/auth/account', {
      data: { password },
    });
  },

  // Refresh token
  refreshToken: async (): Promise<{ token: string }> => {
    const response = await api.post<ApiResponse<{ token: string }>>('/auth/refresh');
    return response.data.data!;
  },
};

// CV API
export const cvAPI = {
  // Get all CVs for user
  getCVs: async (page = 1, limit = 10): Promise<PaginatedResponse<CV[]>> => {
    const response = await api.get<PaginatedResponse<CV[]>>(`/cv?page=${page}&limit=${limit}`);
    return response.data;
  },

  // Get CV by ID
  getCVById: async (id: number): Promise<CV> => {
    const response = await api.get<ApiResponse<CV>>(`/cv/${id}`);
    return response.data.data!;
  },

  // Create new CV
  createCV: async (data: CVFormData): Promise<CV> => {
    const response = await api.post<ApiResponse<CV>>('/cv', data);
    return response.data.data!;
  },

  // Update CV
  updateCV: async (id: number, data: Partial<CVFormData>): Promise<CV> => {
    const response = await api.put<ApiResponse<CV>>(`/cv/${id}`, data);
    return response.data.data!;
  },

  // Delete CV
  deleteCV: async (id: number): Promise<void> => {
    await api.delete(`/cv/${id}`);
  },

  // Duplicate CV
  duplicateCV: async (id: number): Promise<CV> => {
    const response = await api.post<ApiResponse<CV>>(`/cv/${id}/duplicate`);
    return response.data.data!;
  },

  // Toggle public status
  togglePublicStatus: async (id: number): Promise<{ isPublic: boolean; publicSlug?: string }> => {
    const response = await api.put<ApiResponse<{ isPublic: boolean; publicSlug?: string }>>(`/cv/${id}/toggle-public`);
    return response.data.data!;
  },

  // Get public CV by slug
  getPublicCV: async (slug: string): Promise<CV> => {
    const response = await api.get<ApiResponse<CV>>(`/cv/public/${slug}`);
    return response.data.data!;
  },

  // Get CV statistics
  getCVStats: async (): Promise<{
    totalCVs: number;
    publicCVs: number;
    privateCVs: number;
    templateStats: any[];
  }> => {
    const response = await api.get<ApiResponse<{
      totalCVs: number;
      publicCVs: number;
      privateCVs: number;
      templateStats: any[];
    }>>('/cv/stats');
    return response.data.data!;
  },

  // Export CV as PDF
  exportPDF: async (id: number, options?: { format?: 'A4' | 'Letter'; template?: string }): Promise<Blob> => {
    const response = await api.post(`/cv/${id}/export/pdf`, options, {
      responseType: 'blob',
    });
    return response.data;
  },
};

// Generic API error handler
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.response?.data?.errors) {
    const errors = error.response.data.errors;
    const firstError = Object.values(errors)[0] as string[];
    return firstError[0] || 'Validation error';
  }
  
  if (error.message) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};

// Export the axios instance for custom requests
export default api;
