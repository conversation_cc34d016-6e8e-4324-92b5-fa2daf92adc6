import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import { CVProvider } from './contexts/CVContext';
import ProtectedRoute from './components/ProtectedRoute';
import PublicRoute from './components/PublicRoute';
import Layout from './layouts/Layout';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import CVBuilder from './pages/CVBuilder';
import CVPreview from './pages/CVPreview';
import Profile from './pages/Profile';
import PublicCV from './pages/PublicCV';
import NotFound from './pages/NotFound';

function App() {
  return (
    <AuthProvider>
      <CVProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <Routes>
              {/* Public routes */}
              <Route path="/" element={<Home />} />
              <Route path="/cv/:slug" element={<PublicCV />} />

              {/* Auth routes (only accessible when not authenticated) */}
              <Route
                path="/login"
                element={
                  <PublicRoute>
                    <Login />
                  </PublicRoute>
                }
              />
              <Route
                path="/register"
                element={
                  <PublicRoute>
                    <Register />
                  </PublicRoute>
                }
              />

              {/* Protected routes (only accessible when authenticated) */}
              <Route
                path="/dashboard"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <Dashboard />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/cv-builder/:id?"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <CVBuilder />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/cv-preview/:id"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <CVPreview />
                    </Layout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/profile"
                element={
                  <ProtectedRoute>
                    <Layout>
                      <Profile />
                    </Layout>
                  </ProtectedRoute>
                }
              />

              {/* Redirect /app to /dashboard */}
              <Route path="/app" element={<Navigate to="/dashboard" replace />} />

              {/* 404 page */}
              <Route path="*" element={<NotFound />} />
            </Routes>

            {/* Toast notifications */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
                success: {
                  duration: 3000,
                  iconTheme: {
                    primary: '#10b981',
                    secondary: '#fff',
                  },
                },
                error: {
                  duration: 5000,
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#fff',
                  },
                },
              }}
            />
          </div>
        </Router>
      </CVProvider>
    </AuthProvider>
  );
}

export default App;
