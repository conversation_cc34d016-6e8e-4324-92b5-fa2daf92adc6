import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import rateLimit from 'express-rate-limit';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
}));
app.use(limiter);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'CV Generator API is running',
    timestamp: new Date().toISOString(),
    cors: 'enabled',
  });
});

// Test CORS endpoint
app.get('/api/test', (req, res) => {
  res.status(200).json({
    message: 'CORS is working correctly!',
    origin: req.headers.origin,
    timestamp: new Date().toISOString(),
  });
});

// Simple auth routes for testing
app.post('/api/auth/register', (req, res) => {
  console.log('Registration request received:', req.body);
  res.status(200).json({
    success: true,
    message: 'Registration endpoint working! (Mock response)',
    user: {
      id: 1,
      email: req.body.email,
      firstName: req.body.firstName || 'Test',
      lastName: req.body.lastName || 'User',
    },
    token: 'mock-jwt-token-for-testing'
  });
});

app.post('/api/auth/login', (req, res) => {
  console.log('Login request received:', req.body);
  res.status(200).json({
    success: true,
    message: 'Login endpoint working! (Mock response)',
    user: {
      id: 1,
      email: req.body.email,
      firstName: 'Test',
      lastName: 'User',
    },
    token: 'mock-jwt-token-for-testing'
  });
});

app.get('/api/auth/profile', (req, res) => {
  res.status(200).json({
    success: true,
    user: {
      id: 1,
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
    }
  });
});

// Simple CV routes for testing
app.get('/api/cv', (req, res) => {
  res.status(200).json({
    success: true,
    cvs: [
      {
        id: 1,
        title: 'My First CV',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    ]
  });
});

// Error handling
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

app.use((error: any, req: any, res: any, next: any) => {
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Start server
const startServer = () => {
  app.listen(PORT, () => {
    console.log(`🚀 Server is running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔗 API URL: http://localhost:${PORT}/api`);
    console.log(`🌐 CORS enabled for: ${process.env.FRONTEND_URL || 'http://localhost:3000'}`);
    console.log(`⚠️  Running with mock data (database not connected)`);
  });
};

// Handle unhandled promise rejections
process.on('unhandledRejection', (err: Error) => {
  console.error('Unhandled Promise Rejection:', err);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err: Error) => {
  console.error('Uncaught Exception:', err);
  process.exit(1);
});

startServer();

export default app;
