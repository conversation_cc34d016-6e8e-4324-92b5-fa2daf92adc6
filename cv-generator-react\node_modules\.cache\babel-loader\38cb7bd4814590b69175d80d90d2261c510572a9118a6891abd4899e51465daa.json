{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\CV Generator\\\\cv-generator-react\\\\src\\\\pages\\\\CVBuilder.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useCV } from '../contexts/CVContext';\nimport Button from '../components/ui/Button';\nimport Input from '../components/ui/Input';\nimport Select from '../components/ui/Select';\nimport Textarea from '../components/ui/Textarea';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CVBuilder = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    currentCV,\n    fetchCV,\n    createCV,\n    updateCV,\n    setCurrentCV\n  } = useCV();\n  const [currentStep, setCurrentStep] = useState(0);\n  const [cvTitle, setCvTitle] = useState('My CV');\n  const [templateType, setTemplateType] = useState('modern');\n  const [cvData, setCvData] = useState({\n    personal_info: {\n      first_name: '',\n      last_name: '',\n      email: '',\n      phone: '',\n      address: '',\n      website: '',\n      linkedin: '',\n      github: '',\n      summary: ''\n    },\n    education: [],\n    experience: [],\n    skills: [],\n    projects: [],\n    languages: []\n  });\n  const steps = [{\n    title: 'Basic Info',\n    component: 'basic'\n  }, {\n    title: 'Personal Info',\n    component: 'personal'\n  }];\n  useEffect(() => {\n    if (id && id !== 'new') {\n      fetchCV(parseInt(id));\n    } else {\n      setCurrentCV(null);\n    }\n  }, [id, fetchCV, setCurrentCV]);\n  useEffect(() => {\n    if (currentCV) {\n      setCvTitle(currentCV.title);\n      setTemplateType(currentCV.template_type);\n      setCvData(currentCV.cv_data);\n    }\n  }, [currentCV]);\n  const handleSave = async () => {\n    try {\n      const cvPayload = {\n        title: cvTitle,\n        template_type: templateType,\n        cv_data: cvData\n      };\n      if (id && id !== 'new') {\n        await updateCV(parseInt(id), cvPayload);\n      } else {\n        const newCV = await createCV(cvPayload);\n        navigate(`/cv-builder/${newCV.id}`, {\n          replace: true\n        });\n      }\n    } catch (error) {\n      console.error('Failed to save CV:', error);\n    }\n  };\n  const handleNext = () => {\n    if (currentStep < steps.length - 1) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n  const handlePrevious = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  const updatePersonalInfo = (field, value) => {\n    setCvData(prev => ({\n      ...prev,\n      personal_info: {\n        ...prev.personal_info,\n        [field]: value\n      }\n    }));\n  };\n  const addEducation = () => {\n    const newEducation = {\n      id: Date.now().toString(),\n      institution: '',\n      degree: '',\n      field_of_study: '',\n      start_date: '',\n      end_date: '',\n      current: false,\n      description: '',\n      gpa: ''\n    };\n    setCvData(prev => ({\n      ...prev,\n      education: [...prev.education, newEducation]\n    }));\n  };\n  const updateEducation = (index, field, value) => {\n    setCvData(prev => ({\n      ...prev,\n      education: prev.education.map((edu, i) => i === index ? {\n        ...edu,\n        [field]: value\n      } : edu)\n    }));\n  };\n  const removeEducation = index => {\n    setCvData(prev => ({\n      ...prev,\n      education: prev.education.filter((_, i) => i !== index)\n    }));\n  };\n  const addExperience = () => {\n    const newExperience = {\n      id: Date.now().toString(),\n      company: '',\n      position: '',\n      start_date: '',\n      end_date: '',\n      current: false,\n      description: '',\n      achievements: []\n    };\n    setCvData(prev => ({\n      ...prev,\n      experience: [...prev.experience, newExperience]\n    }));\n  };\n  const updateExperience = (index, field, value) => {\n    setCvData(prev => ({\n      ...prev,\n      experience: prev.experience.map((exp, i) => i === index ? {\n        ...exp,\n        [field]: value\n      } : exp)\n    }));\n  };\n  const removeExperience = index => {\n    setCvData(prev => ({\n      ...prev,\n      experience: prev.experience.filter((_, i) => i !== index)\n    }));\n  };\n  const addSkill = () => {\n    const newSkill = {\n      id: Date.now().toString(),\n      name: '',\n      level: 'Intermediate',\n      category: ''\n    };\n    setCvData(prev => ({\n      ...prev,\n      skills: [...prev.skills, newSkill]\n    }));\n  };\n  const updateSkill = (index, field, value) => {\n    setCvData(prev => ({\n      ...prev,\n      skills: prev.skills.map((skill, i) => i === index ? {\n        ...skill,\n        [field]: value\n      } : skill)\n    }));\n  };\n  const removeSkill = index => {\n    setCvData(prev => ({\n      ...prev,\n      skills: prev.skills.filter((_, i) => i !== index)\n    }));\n  };\n  const renderBasicInfo = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        fontSize: '1.125rem',\n        fontWeight: '500',\n        color: '#111827',\n        marginBottom: '1.5rem'\n      },\n      children: \"Basic Information\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n        gap: '1.5rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Input, {\n        label: \"CV Title\",\n        placeholder: \"e.g., Software Engineer Resume\",\n        value: cvTitle,\n        onChange: setCvTitle,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        label: \"Template\",\n        value: templateType,\n        onChange: value => setTemplateType(value),\n        options: [{\n          value: 'modern',\n          label: 'Modern'\n        }, {\n          value: 'classic',\n          label: 'Classic'\n        }, {\n          value: 'creative',\n          label: 'Creative'\n        }, {\n          value: 'minimal',\n          label: 'Minimal'\n        }],\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 216,\n    columnNumber: 5\n  }, this);\n  const renderPersonalInfo = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      style: {\n        fontSize: '1.125rem',\n        fontWeight: '500',\n        color: '#111827',\n        marginBottom: '1.5rem'\n      },\n      children: \"Personal Information\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n        gap: '1rem',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Input, {\n        label: \"First Name\",\n        placeholder: \"John\",\n        value: cvData.personal_info.first_name,\n        onChange: value => updatePersonalInfo('first_name', value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Last Name\",\n        placeholder: \"Doe\",\n        value: cvData.personal_info.last_name,\n        onChange: value => updatePersonalInfo('last_name', value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Email\",\n        type: \"email\",\n        placeholder: \"<EMAIL>\",\n        value: cvData.personal_info.email,\n        onChange: value => updatePersonalInfo('email', value),\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Phone\",\n        type: \"tel\",\n        placeholder: \"+****************\",\n        value: cvData.personal_info.phone || '',\n        onChange: value => updatePersonalInfo('phone', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        label: \"Address\",\n        placeholder: \"123 Main St, City, State 12345\",\n        value: cvData.personal_info.address || '',\n        onChange: value => updatePersonalInfo('address', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n        gap: '1rem',\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Input, {\n        label: \"Website\",\n        type: \"url\",\n        placeholder: \"https://johndoe.com\",\n        value: cvData.personal_info.website || '',\n        onChange: value => updatePersonalInfo('website', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Input, {\n        label: \"LinkedIn\",\n        type: \"url\",\n        placeholder: \"https://linkedin.com/in/johndoe\",\n        value: cvData.personal_info.linkedin || '',\n        onChange: value => updatePersonalInfo('linkedin', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        label: \"GitHub\",\n        type: \"url\",\n        placeholder: \"https://github.com/johndoe\",\n        value: cvData.personal_info.github || '',\n        onChange: value => updatePersonalInfo('github', value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Textarea, {\n      label: \"Professional Summary\",\n      placeholder: \"Brief description of your professional background and goals...\",\n      value: cvData.personal_info.summary || '',\n      onChange: value => updatePersonalInfo('summary', value),\n      rows: 4\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 245,\n    columnNumber: 5\n  }, this);\n  const renderEducation = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900\",\n        children: \"Education\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: addEducation,\n        size: \"sm\",\n        children: \"Add Education\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this), cvData.education.map((edu, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border border-gray-200 rounded-lg p-6 space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-md font-medium text-gray-800\",\n          children: [\"Education #\", index + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => removeEducation(index),\n          variant: \"danger\",\n          size: \"sm\",\n          children: \"Remove\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          label: \"Institution\",\n          placeholder: \"University of Example\",\n          value: edu.institution,\n          onChange: value => updateEducation(index, 'institution', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Degree\",\n          placeholder: \"Bachelor of Science\",\n          value: edu.degree,\n          onChange: value => updateEducation(index, 'degree', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Field of Study\",\n          placeholder: \"Computer Science\",\n          value: edu.field_of_study,\n          onChange: value => updateEducation(index, 'field_of_study', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"GPA (Optional)\",\n          placeholder: \"3.8\",\n          value: edu.gpa || '',\n          onChange: value => updateEducation(index, 'gpa', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Start Date\",\n          type: \"date\",\n          value: edu.start_date,\n          onChange: value => updateEducation(index, 'start_date', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"End Date\",\n          type: \"date\",\n          value: edu.end_date || '',\n          onChange: value => updateEducation(index, 'end_date', value),\n          disabled: edu.current\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          id: `current-education-${index}`,\n          checked: edu.current,\n          onChange: e => updateEducation(index, 'current', e.target.checked),\n          className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: `current-education-${index}`,\n          className: \"ml-2 text-sm text-gray-700\",\n          children: \"Currently studying here\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Textarea, {\n        label: \"Description (Optional)\",\n        placeholder: \"Relevant coursework, achievements, activities...\",\n        value: edu.description || '',\n        onChange: value => updateEducation(index, 'description', value),\n        rows: 3\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 11\n      }, this)]\n    }, edu.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 9\n    }, this)), cvData.education.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No education entries yet. Click \\\"Add Education\\\" to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 324,\n    columnNumber: 5\n  }, this);\n  const renderExperience = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900\",\n        children: \"Work Experience\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: addExperience,\n        size: \"sm\",\n        children: \"Add Experience\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this), cvData.experience.map((exp, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border border-gray-200 rounded-lg p-6 space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-md font-medium text-gray-800\",\n          children: [\"Experience #\", index + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => removeExperience(index),\n          variant: \"danger\",\n          size: \"sm\",\n          children: \"Remove\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          label: \"Company\",\n          placeholder: \"Tech Company Inc.\",\n          value: exp.company,\n          onChange: value => updateExperience(index, 'company', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Position\",\n          placeholder: \"Software Engineer\",\n          value: exp.position,\n          onChange: value => updateExperience(index, 'position', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Start Date\",\n          type: \"date\",\n          value: exp.start_date,\n          onChange: value => updateExperience(index, 'start_date', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"End Date\",\n          type: \"date\",\n          value: exp.end_date || '',\n          onChange: value => updateExperience(index, 'end_date', value),\n          disabled: exp.current\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          id: `current-experience-${index}`,\n          checked: exp.current,\n          onChange: e => updateExperience(index, 'current', e.target.checked),\n          className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: `current-experience-${index}`,\n          className: \"ml-2 text-sm text-gray-700\",\n          children: \"Currently working here\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Textarea, {\n        label: \"Job Description\",\n        placeholder: \"Describe your responsibilities and achievements...\",\n        value: exp.description || '',\n        onChange: value => updateExperience(index, 'description', value),\n        rows: 4\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 11\n      }, this)]\n    }, exp.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 9\n    }, this)), cvData.experience.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No work experience entries yet. Click \\\"Add Experience\\\" to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 414,\n    columnNumber: 5\n  }, this);\n  const renderSkills = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900\",\n        children: \"Skills\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: addSkill,\n        size: \"sm\",\n        children: \"Add Skill\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n      children: cvData.skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border border-gray-200 rounded-lg p-4 space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-start\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-gray-800\",\n            children: [\"Skill #\", index + 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => removeSkill(index),\n            variant: \"danger\",\n            size: \"sm\",\n            children: \"Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Skill Name\",\n          placeholder: \"JavaScript\",\n          value: skill.name,\n          onChange: value => updateSkill(index, 'name', value),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          label: \"Proficiency Level\",\n          value: skill.level,\n          onChange: value => updateSkill(index, 'level', value),\n          options: [{\n            value: 'Beginner',\n            label: 'Beginner'\n          }, {\n            value: 'Intermediate',\n            label: 'Intermediate'\n          }, {\n            value: 'Advanced',\n            label: 'Advanced'\n          }, {\n            value: 'Expert',\n            label: 'Expert'\n          }],\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Category (Optional)\",\n          placeholder: \"Programming Languages\",\n          value: skill.category || '',\n          onChange: value => updateSkill(index, 'category', value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 13\n        }, this)]\n      }, skill.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 7\n    }, this), cvData.skills.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No skills added yet. Click \\\"Add Skill\\\" to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 491,\n    columnNumber: 5\n  }, this);\n  const renderProjects = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900\",\n        children: \"Projects\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => {\n          const newProject = {\n            id: Date.now().toString(),\n            name: '',\n            description: '',\n            technologies: [],\n            start_date: '',\n            end_date: '',\n            url: '',\n            github_url: ''\n          };\n          setCvData(prev => ({\n            ...prev,\n            projects: [...prev.projects, newProject]\n          }));\n        },\n        size: \"sm\",\n        children: \"Add Project\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 7\n    }, this), cvData.projects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border border-gray-200 rounded-lg p-6 space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-md font-medium text-gray-800\",\n          children: [\"Project #\", index + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setCvData(prev => ({\n            ...prev,\n            projects: prev.projects.filter((_, i) => i !== index)\n          })),\n          variant: \"danger\",\n          size: \"sm\",\n          children: \"Remove\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          label: \"Project Name\",\n          placeholder: \"My Awesome Project\",\n          value: project.name,\n          onChange: value => setCvData(prev => ({\n            ...prev,\n            projects: prev.projects.map((p, i) => i === index ? {\n              ...p,\n              name: value\n            } : p)\n          })),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Technologies\",\n          placeholder: \"React, Node.js, MongoDB\",\n          value: project.technologies.join(', '),\n          onChange: value => setCvData(prev => ({\n            ...prev,\n            projects: prev.projects.map((p, i) => i === index ? {\n              ...p,\n              technologies: value.split(',').map(t => t.trim())\n            } : p)\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Project URL (Optional)\",\n          type: \"url\",\n          placeholder: \"https://myproject.com\",\n          value: project.url || '',\n          onChange: value => setCvData(prev => ({\n            ...prev,\n            projects: prev.projects.map((p, i) => i === index ? {\n              ...p,\n              url: value\n            } : p)\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"GitHub URL (Optional)\",\n          type: \"url\",\n          placeholder: \"https://github.com/user/project\",\n          value: project.github_url || '',\n          onChange: value => setCvData(prev => ({\n            ...prev,\n            projects: prev.projects.map((p, i) => i === index ? {\n              ...p,\n              github_url: value\n            } : p)\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Textarea, {\n        label: \"Project Description\",\n        placeholder: \"Describe what the project does and your role...\",\n        value: project.description,\n        onChange: value => setCvData(prev => ({\n          ...prev,\n          projects: prev.projects.map((p, i) => i === index ? {\n            ...p,\n            description: value\n          } : p)\n        })),\n        rows: 3\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 630,\n        columnNumber: 11\n      }, this)]\n    }, project.id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 9\n    }, this)), cvData.projects.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No projects added yet. Click \\\"Add Project\\\" to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 645,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 546,\n    columnNumber: 5\n  }, this);\n  const renderLanguages = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900\",\n        children: \"Languages\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => {\n          const newLanguage = {\n            id: Date.now().toString(),\n            name: '',\n            proficiency: 'Conversational'\n          };\n          setCvData(prev => ({\n            ...prev,\n            languages: [...prev.languages, newLanguage]\n          }));\n        },\n        size: \"sm\",\n        children: \"Add Language\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n      children: cvData.languages.map((language, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border border-gray-200 rounded-lg p-4 space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-start\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-gray-800\",\n            children: [\"Language #\", index + 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setCvData(prev => ({\n              ...prev,\n              languages: prev.languages.filter((_, i) => i !== index)\n            })),\n            variant: \"danger\",\n            size: \"sm\",\n            children: \"Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          label: \"Language\",\n          placeholder: \"English\",\n          value: language.name,\n          onChange: value => setCvData(prev => ({\n            ...prev,\n            languages: prev.languages.map((l, i) => i === index ? {\n              ...l,\n              name: value\n            } : l)\n          })),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          label: \"Proficiency Level\",\n          value: language.proficiency,\n          onChange: value => setCvData(prev => ({\n            ...prev,\n            languages: prev.languages.map((l, i) => i === index ? {\n              ...l,\n              proficiency: value\n            } : l)\n          })),\n          options: [{\n            value: 'Basic',\n            label: 'Basic'\n          }, {\n            value: 'Conversational',\n            label: 'Conversational'\n          }, {\n            value: 'Fluent',\n            label: 'Fluent'\n          }, {\n            value: 'Native',\n            label: 'Native'\n          }],\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 13\n        }, this)]\n      }, language.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 7\n    }, this), cvData.languages.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8 text-gray-500\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No languages added yet. Click \\\"Add Language\\\" to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 718,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 717,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 653,\n    columnNumber: 5\n  }, this);\n  const renderCurrentStep = () => {\n    switch (steps[currentStep].component) {\n      case 'basic':\n        return renderBasicInfo();\n      case 'personal':\n        return renderPersonalInfo();\n      default:\n        return renderBasicInfo();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '64rem',\n      margin: '0 auto',\n      padding: '2rem 1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          fontSize: '1.875rem',\n          fontWeight: 'bold',\n          color: '#111827',\n          marginBottom: '0.5rem'\n        },\n        children: id && id !== 'new' ? 'Edit CV' : 'Create New CV'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 739,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#6b7280'\n        },\n        children: \"Fill out the information below to create your professional CV\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 742,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 738,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            marginRight: index !== steps.length - 1 ? '2rem' : '0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              width: '2rem',\n              height: '2rem',\n              borderRadius: '50%',\n              backgroundColor: index < currentStep ? '#3b82f6' : index === currentStep ? 'white' : 'white',\n              border: index === currentStep ? '2px solid #3b82f6' : '2px solid #d1d5db',\n              color: index < currentStep ? 'white' : index === currentStep ? '#3b82f6' : '#6b7280',\n              fontSize: '0.875rem',\n              fontWeight: '500'\n            },\n            children: index < currentStep ? /*#__PURE__*/_jsxDEV(\"svg\", {\n              style: {\n                width: '1.25rem',\n                height: '1.25rem'\n              },\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 19\n            }, this) : index + 1\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '1rem',\n              fontSize: '0.875rem',\n              fontWeight: '500',\n              color: index === currentStep ? '#3b82f6' : '#6b7280'\n            },\n            children: step.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 15\n          }, this)]\n        }, step.title, true, {\n          fileName: _jsxFileName,\n          lineNumber: 751,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 749,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 748,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: 'white',\n        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',\n        borderRadius: '0.5rem',\n        padding: '1.5rem',\n        marginBottom: '2rem'\n      },\n      children: renderCurrentStep()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 789,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: currentStep > 0 && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePrevious,\n          variant: \"outline\",\n          children: \"Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 795,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSave,\n          variant: \"outline\",\n          children: \"Save Draft\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 11\n        }, this), currentStep < steps.length - 1 ? /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleNext,\n          children: \"Next\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => navigate(`/dashboard`),\n          children: \"Finish\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 805,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 794,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 736,\n    columnNumber: 5\n  }, this);\n};\n_s(CVBuilder, \"ImTEXjSAouEwGkEeKamxRjTZFe4=\", false, function () {\n  return [useParams, useNavigate, useCV];\n});\n_c = CVBuilder;\nexport default CVBuilder;\nvar _c;\n$RefreshReg$(_c, \"CVBuilder\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useCV", "<PERSON><PERSON>", "Input", "Select", "Textarea", "jsxDEV", "_jsxDEV", "CVBuilder", "_s", "id", "navigate", "currentCV", "fetchCV", "createCV", "updateCV", "setCurrentCV", "currentStep", "setCurrentStep", "cvTitle", "setCvTitle", "templateType", "setTemplateType", "cvData", "setCvData", "personal_info", "first_name", "last_name", "email", "phone", "address", "website", "linkedin", "github", "summary", "education", "experience", "skills", "projects", "languages", "steps", "title", "component", "parseInt", "template_type", "cv_data", "handleSave", "cvPayload", "newCV", "replace", "error", "console", "handleNext", "length", "handlePrevious", "updatePersonalInfo", "field", "value", "prev", "addEducation", "newEducation", "Date", "now", "toString", "institution", "degree", "field_of_study", "start_date", "end_date", "current", "description", "gpa", "updateEducation", "index", "map", "edu", "i", "removeEducation", "filter", "_", "addExperience", "newExperience", "company", "position", "achievements", "updateExperience", "exp", "removeExperience", "addSkill", "newSkill", "name", "level", "category", "updateSkill", "skill", "removeSkill", "renderBasicInfo", "children", "style", "fontSize", "fontWeight", "color", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "gridTemplateColumns", "gap", "label", "placeholder", "onChange", "required", "options", "renderPersonalInfo", "type", "rows", "renderEducation", "className", "onClick", "size", "variant", "disabled", "checked", "e", "target", "htmlFor", "renderExperience", "renderSkills", "renderProjects", "newProject", "technologies", "url", "github_url", "project", "p", "join", "split", "t", "trim", "renderLanguages", "newLanguage", "proficiency", "language", "l", "renderCurrentStep", "max<PERSON><PERSON><PERSON>", "margin", "padding", "alignItems", "step", "marginRight", "justifyContent", "width", "height", "borderRadius", "backgroundColor", "border", "fill", "viewBox", "fillRule", "d", "clipRule", "marginLeft", "boxShadow", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/CV Generator/cv-generator-react/src/pages/CVBuilder.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useCV } from '../contexts/CVContext';\nimport Button from '../components/ui/Button';\nimport Input from '../components/ui/Input';\nimport Select from '../components/ui/Select';\nimport Textarea from '../components/ui/Textarea';\n\ninterface PersonalInfo {\n  first_name: string;\n  last_name: string;\n  email: string;\n  phone?: string;\n  address?: string;\n  website?: string;\n  linkedin?: string;\n  github?: string;\n  summary?: string;\n}\n\ninterface CVData {\n  personal_info: PersonalInfo;\n  education: any[];\n  experience: any[];\n  skills: any[];\n  projects: any[];\n  languages: any[];\n}\n\nconst CVBuilder: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const { currentCV, fetchCV, createCV, updateCV, setCurrentCV } = useCV();\n\n  const [currentStep, setCurrentStep] = useState(0);\n  const [cvTitle, setCvTitle] = useState('My CV');\n  const [templateType, setTemplateType] = useState<'modern' | 'classic' | 'creative' | 'minimal'>('modern');\n  const [cvData, setCvData] = useState<CVData>({\n    personal_info: {\n      first_name: '',\n      last_name: '',\n      email: '',\n      phone: '',\n      address: '',\n      website: '',\n      linkedin: '',\n      github: '',\n      summary: '',\n    },\n    education: [],\n    experience: [],\n    skills: [],\n    projects: [],\n    languages: [],\n  });\n\n  const steps = [\n    { title: 'Basic Info', component: 'basic' },\n    { title: 'Personal Info', component: 'personal' },\n  ];\n\n  useEffect(() => {\n    if (id && id !== 'new') {\n      fetchCV(parseInt(id));\n    } else {\n      setCurrentCV(null);\n    }\n  }, [id, fetchCV, setCurrentCV]);\n\n  useEffect(() => {\n    if (currentCV) {\n      setCvTitle(currentCV.title);\n      setTemplateType(currentCV.template_type);\n      setCvData(currentCV.cv_data);\n    }\n  }, [currentCV]);\n\n  const handleSave = async () => {\n    try {\n      const cvPayload = {\n        title: cvTitle,\n        template_type: templateType,\n        cv_data: cvData,\n      };\n\n      if (id && id !== 'new') {\n        await updateCV(parseInt(id), cvPayload);\n      } else {\n        const newCV = await createCV(cvPayload);\n        navigate(`/cv-builder/${newCV.id}`, { replace: true });\n      }\n    } catch (error) {\n      console.error('Failed to save CV:', error);\n    }\n  };\n\n  const handleNext = () => {\n    if (currentStep < steps.length - 1) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentStep > 0) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const updatePersonalInfo = (field: keyof PersonalInfo, value: string) => {\n    setCvData(prev => ({\n      ...prev,\n      personal_info: {\n        ...prev.personal_info,\n        [field]: value,\n      },\n    }));\n  };\n\n  const addEducation = () => {\n    const newEducation: Education = {\n      id: Date.now().toString(),\n      institution: '',\n      degree: '',\n      field_of_study: '',\n      start_date: '',\n      end_date: '',\n      current: false,\n      description: '',\n      gpa: '',\n    };\n    setCvData(prev => ({\n      ...prev,\n      education: [...prev.education, newEducation],\n    }));\n  };\n\n  const updateEducation = (index: number, field: keyof Education, value: string | boolean) => {\n    setCvData(prev => ({\n      ...prev,\n      education: prev.education.map((edu, i) => \n        i === index ? { ...edu, [field]: value } : edu\n      ),\n    }));\n  };\n\n  const removeEducation = (index: number) => {\n    setCvData(prev => ({\n      ...prev,\n      education: prev.education.filter((_, i) => i !== index),\n    }));\n  };\n\n  const addExperience = () => {\n    const newExperience: Experience = {\n      id: Date.now().toString(),\n      company: '',\n      position: '',\n      start_date: '',\n      end_date: '',\n      current: false,\n      description: '',\n      achievements: [],\n    };\n    setCvData(prev => ({\n      ...prev,\n      experience: [...prev.experience, newExperience],\n    }));\n  };\n\n  const updateExperience = (index: number, field: keyof Experience, value: string | boolean | string[]) => {\n    setCvData(prev => ({\n      ...prev,\n      experience: prev.experience.map((exp, i) => \n        i === index ? { ...exp, [field]: value } : exp\n      ),\n    }));\n  };\n\n  const removeExperience = (index: number) => {\n    setCvData(prev => ({\n      ...prev,\n      experience: prev.experience.filter((_, i) => i !== index),\n    }));\n  };\n\n  const addSkill = () => {\n    const newSkill: Skill = {\n      id: Date.now().toString(),\n      name: '',\n      level: 'Intermediate',\n      category: '',\n    };\n    setCvData(prev => ({\n      ...prev,\n      skills: [...prev.skills, newSkill],\n    }));\n  };\n\n  const updateSkill = (index: number, field: keyof Skill, value: string) => {\n    setCvData(prev => ({\n      ...prev,\n      skills: prev.skills.map((skill, i) => \n        i === index ? { ...skill, [field]: value } : skill\n      ),\n    }));\n  };\n\n  const removeSkill = (index: number) => {\n    setCvData(prev => ({\n      ...prev,\n      skills: prev.skills.filter((_, i) => i !== index),\n    }));\n  };\n\n  const renderBasicInfo = () => (\n    <div>\n      <h3 style={{ fontSize: '1.125rem', fontWeight: '500', color: '#111827', marginBottom: '1.5rem' }}>\n        Basic Information\n      </h3>\n      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>\n        <Input\n          label=\"CV Title\"\n          placeholder=\"e.g., Software Engineer Resume\"\n          value={cvTitle}\n          onChange={setCvTitle}\n          required\n        />\n        <Select\n          label=\"Template\"\n          value={templateType}\n          onChange={(value) => setTemplateType(value as any)}\n          options={[\n            { value: 'modern', label: 'Modern' },\n            { value: 'classic', label: 'Classic' },\n            { value: 'creative', label: 'Creative' },\n            { value: 'minimal', label: 'Minimal' },\n          ]}\n          required\n        />\n      </div>\n    </div>\n  );\n\n  const renderPersonalInfo = () => (\n    <div>\n      <h3 style={{ fontSize: '1.125rem', fontWeight: '500', color: '#111827', marginBottom: '1.5rem' }}>\n        Personal Information\n      </h3>\n      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginBottom: '1rem' }}>\n        <Input\n          label=\"First Name\"\n          placeholder=\"John\"\n          value={cvData.personal_info.first_name}\n          onChange={(value) => updatePersonalInfo('first_name', value)}\n          required\n        />\n        <Input\n          label=\"Last Name\"\n          placeholder=\"Doe\"\n          value={cvData.personal_info.last_name}\n          onChange={(value) => updatePersonalInfo('last_name', value)}\n          required\n        />\n        <Input\n          label=\"Email\"\n          type=\"email\"\n          placeholder=\"<EMAIL>\"\n          value={cvData.personal_info.email}\n          onChange={(value) => updatePersonalInfo('email', value)}\n          required\n        />\n        <Input\n          label=\"Phone\"\n          type=\"tel\"\n          placeholder=\"+****************\"\n          value={cvData.personal_info.phone || ''}\n          onChange={(value) => updatePersonalInfo('phone', value)}\n        />\n      </div>\n      <div style={{ marginBottom: '1rem' }}>\n        <Input\n          label=\"Address\"\n          placeholder=\"123 Main St, City, State 12345\"\n          value={cvData.personal_info.address || ''}\n          onChange={(value) => updatePersonalInfo('address', value)}\n        />\n      </div>\n      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem', marginBottom: '1rem' }}>\n        <Input\n          label=\"Website\"\n          type=\"url\"\n          placeholder=\"https://johndoe.com\"\n          value={cvData.personal_info.website || ''}\n          onChange={(value) => updatePersonalInfo('website', value)}\n        />\n        <Input\n          label=\"LinkedIn\"\n          type=\"url\"\n          placeholder=\"https://linkedin.com/in/johndoe\"\n          value={cvData.personal_info.linkedin || ''}\n          onChange={(value) => updatePersonalInfo('linkedin', value)}\n        />\n      </div>\n      <div style={{ marginBottom: '1rem' }}>\n        <Input\n          label=\"GitHub\"\n          type=\"url\"\n          placeholder=\"https://github.com/johndoe\"\n          value={cvData.personal_info.github || ''}\n          onChange={(value) => updatePersonalInfo('github', value)}\n        />\n      </div>\n      <Textarea\n        label=\"Professional Summary\"\n        placeholder=\"Brief description of your professional background and goals...\"\n        value={cvData.personal_info.summary || ''}\n        onChange={(value) => updatePersonalInfo('summary', value)}\n        rows={4}\n      />\n    </div>\n  );\n\n  const renderEducation = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Education</h3>\n        <Button onClick={addEducation} size=\"sm\">Add Education</Button>\n      </div>\n      {cvData.education.map((edu, index) => (\n        <div key={edu.id} className=\"border border-gray-200 rounded-lg p-6 space-y-4\">\n          <div className=\"flex justify-between items-start\">\n            <h4 className=\"text-md font-medium text-gray-800\">Education #{index + 1}</h4>\n            <Button\n              onClick={() => removeEducation(index)}\n              variant=\"danger\"\n              size=\"sm\"\n            >\n              Remove\n            </Button>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <Input\n              label=\"Institution\"\n              placeholder=\"University of Example\"\n              value={edu.institution}\n              onChange={(value) => updateEducation(index, 'institution', value)}\n              required\n            />\n            <Input\n              label=\"Degree\"\n              placeholder=\"Bachelor of Science\"\n              value={edu.degree}\n              onChange={(value) => updateEducation(index, 'degree', value)}\n              required\n            />\n            <Input\n              label=\"Field of Study\"\n              placeholder=\"Computer Science\"\n              value={edu.field_of_study}\n              onChange={(value) => updateEducation(index, 'field_of_study', value)}\n              required\n            />\n            <Input\n              label=\"GPA (Optional)\"\n              placeholder=\"3.8\"\n              value={edu.gpa || ''}\n              onChange={(value) => updateEducation(index, 'gpa', value)}\n            />\n            <Input\n              label=\"Start Date\"\n              type=\"date\"\n              value={edu.start_date}\n              onChange={(value) => updateEducation(index, 'start_date', value)}\n              required\n            />\n            <Input\n              label=\"End Date\"\n              type=\"date\"\n              value={edu.end_date || ''}\n              onChange={(value) => updateEducation(index, 'end_date', value)}\n              disabled={edu.current}\n            />\n          </div>\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              id={`current-education-${index}`}\n              checked={edu.current}\n              onChange={(e) => updateEducation(index, 'current', e.target.checked)}\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label htmlFor={`current-education-${index}`} className=\"ml-2 text-sm text-gray-700\">\n              Currently studying here\n            </label>\n          </div>\n          <Textarea\n            label=\"Description (Optional)\"\n            placeholder=\"Relevant coursework, achievements, activities...\"\n            value={edu.description || ''}\n            onChange={(value) => updateEducation(index, 'description', value)}\n            rows={3}\n          />\n        </div>\n      ))}\n      {cvData.education.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <p>No education entries yet. Click \"Add Education\" to get started.</p>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderExperience = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Work Experience</h3>\n        <Button onClick={addExperience} size=\"sm\">Add Experience</Button>\n      </div>\n      {cvData.experience.map((exp, index) => (\n        <div key={exp.id} className=\"border border-gray-200 rounded-lg p-6 space-y-4\">\n          <div className=\"flex justify-between items-start\">\n            <h4 className=\"text-md font-medium text-gray-800\">Experience #{index + 1}</h4>\n            <Button\n              onClick={() => removeExperience(index)}\n              variant=\"danger\"\n              size=\"sm\"\n            >\n              Remove\n            </Button>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <Input\n              label=\"Company\"\n              placeholder=\"Tech Company Inc.\"\n              value={exp.company}\n              onChange={(value) => updateExperience(index, 'company', value)}\n              required\n            />\n            <Input\n              label=\"Position\"\n              placeholder=\"Software Engineer\"\n              value={exp.position}\n              onChange={(value) => updateExperience(index, 'position', value)}\n              required\n            />\n            <Input\n              label=\"Start Date\"\n              type=\"date\"\n              value={exp.start_date}\n              onChange={(value) => updateExperience(index, 'start_date', value)}\n              required\n            />\n            <Input\n              label=\"End Date\"\n              type=\"date\"\n              value={exp.end_date || ''}\n              onChange={(value) => updateExperience(index, 'end_date', value)}\n              disabled={exp.current}\n            />\n          </div>\n          <div className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              id={`current-experience-${index}`}\n              checked={exp.current}\n              onChange={(e) => updateExperience(index, 'current', e.target.checked)}\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n            />\n            <label htmlFor={`current-experience-${index}`} className=\"ml-2 text-sm text-gray-700\">\n              Currently working here\n            </label>\n          </div>\n          <Textarea\n            label=\"Job Description\"\n            placeholder=\"Describe your responsibilities and achievements...\"\n            value={exp.description || ''}\n            onChange={(value) => updateExperience(index, 'description', value)}\n            rows={4}\n          />\n        </div>\n      ))}\n      {cvData.experience.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <p>No work experience entries yet. Click \"Add Experience\" to get started.</p>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderSkills = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Skills</h3>\n        <Button onClick={addSkill} size=\"sm\">Add Skill</Button>\n      </div>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        {cvData.skills.map((skill, index) => (\n          <div key={skill.id} className=\"border border-gray-200 rounded-lg p-4 space-y-3\">\n            <div className=\"flex justify-between items-start\">\n              <h4 className=\"text-sm font-medium text-gray-800\">Skill #{index + 1}</h4>\n              <Button\n                onClick={() => removeSkill(index)}\n                variant=\"danger\"\n                size=\"sm\"\n              >\n                Remove\n              </Button>\n            </div>\n            <Input\n              label=\"Skill Name\"\n              placeholder=\"JavaScript\"\n              value={skill.name}\n              onChange={(value) => updateSkill(index, 'name', value)}\n              required\n            />\n            <Select\n              label=\"Proficiency Level\"\n              value={skill.level}\n              onChange={(value) => updateSkill(index, 'level', value)}\n              options={[\n                { value: 'Beginner', label: 'Beginner' },\n                { value: 'Intermediate', label: 'Intermediate' },\n                { value: 'Advanced', label: 'Advanced' },\n                { value: 'Expert', label: 'Expert' },\n              ]}\n              required\n            />\n            <Input\n              label=\"Category (Optional)\"\n              placeholder=\"Programming Languages\"\n              value={skill.category || ''}\n              onChange={(value) => updateSkill(index, 'category', value)}\n            />\n          </div>\n        ))}\n      </div>\n      {cvData.skills.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <p>No skills added yet. Click \"Add Skill\" to get started.</p>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderProjects = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Projects</h3>\n        <Button onClick={() => {\n          const newProject: Project = {\n            id: Date.now().toString(),\n            name: '',\n            description: '',\n            technologies: [],\n            start_date: '',\n            end_date: '',\n            url: '',\n            github_url: '',\n          };\n          setCvData(prev => ({\n            ...prev,\n            projects: [...prev.projects, newProject],\n          }));\n        }} size=\"sm\">Add Project</Button>\n      </div>\n      {cvData.projects.map((project, index) => (\n        <div key={project.id} className=\"border border-gray-200 rounded-lg p-6 space-y-4\">\n          <div className=\"flex justify-between items-start\">\n            <h4 className=\"text-md font-medium text-gray-800\">Project #{index + 1}</h4>\n            <Button\n              onClick={() => setCvData(prev => ({\n                ...prev,\n                projects: prev.projects.filter((_, i) => i !== index),\n              }))}\n              variant=\"danger\"\n              size=\"sm\"\n            >\n              Remove\n            </Button>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <Input\n              label=\"Project Name\"\n              placeholder=\"My Awesome Project\"\n              value={project.name}\n              onChange={(value) => setCvData(prev => ({\n                ...prev,\n                projects: prev.projects.map((p, i) =>\n                  i === index ? { ...p, name: value } : p\n                ),\n              }))}\n              required\n            />\n            <Input\n              label=\"Technologies\"\n              placeholder=\"React, Node.js, MongoDB\"\n              value={project.technologies.join(', ')}\n              onChange={(value) => setCvData(prev => ({\n                ...prev,\n                projects: prev.projects.map((p, i) =>\n                  i === index ? { ...p, technologies: value.split(',').map(t => t.trim()) } : p\n                ),\n              }))}\n            />\n            <Input\n              label=\"Project URL (Optional)\"\n              type=\"url\"\n              placeholder=\"https://myproject.com\"\n              value={project.url || ''}\n              onChange={(value) => setCvData(prev => ({\n                ...prev,\n                projects: prev.projects.map((p, i) =>\n                  i === index ? { ...p, url: value } : p\n                ),\n              }))}\n            />\n            <Input\n              label=\"GitHub URL (Optional)\"\n              type=\"url\"\n              placeholder=\"https://github.com/user/project\"\n              value={project.github_url || ''}\n              onChange={(value) => setCvData(prev => ({\n                ...prev,\n                projects: prev.projects.map((p, i) =>\n                  i === index ? { ...p, github_url: value } : p\n                ),\n              }))}\n            />\n          </div>\n          <Textarea\n            label=\"Project Description\"\n            placeholder=\"Describe what the project does and your role...\"\n            value={project.description}\n            onChange={(value) => setCvData(prev => ({\n              ...prev,\n              projects: prev.projects.map((p, i) =>\n                i === index ? { ...p, description: value } : p\n              ),\n            }))}\n            rows={3}\n          />\n        </div>\n      ))}\n      {cvData.projects.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <p>No projects added yet. Click \"Add Project\" to get started.</p>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderLanguages = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Languages</h3>\n        <Button onClick={() => {\n          const newLanguage: Language = {\n            id: Date.now().toString(),\n            name: '',\n            proficiency: 'Conversational',\n          };\n          setCvData(prev => ({\n            ...prev,\n            languages: [...prev.languages, newLanguage],\n          }));\n        }} size=\"sm\">Add Language</Button>\n      </div>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        {cvData.languages.map((language, index) => (\n          <div key={language.id} className=\"border border-gray-200 rounded-lg p-4 space-y-3\">\n            <div className=\"flex justify-between items-start\">\n              <h4 className=\"text-sm font-medium text-gray-800\">Language #{index + 1}</h4>\n              <Button\n                onClick={() => setCvData(prev => ({\n                  ...prev,\n                  languages: prev.languages.filter((_, i) => i !== index),\n                }))}\n                variant=\"danger\"\n                size=\"sm\"\n              >\n                Remove\n              </Button>\n            </div>\n            <Input\n              label=\"Language\"\n              placeholder=\"English\"\n              value={language.name}\n              onChange={(value) => setCvData(prev => ({\n                ...prev,\n                languages: prev.languages.map((l, i) =>\n                  i === index ? { ...l, name: value } : l\n                ),\n              }))}\n              required\n            />\n            <Select\n              label=\"Proficiency Level\"\n              value={language.proficiency}\n              onChange={(value) => setCvData(prev => ({\n                ...prev,\n                languages: prev.languages.map((l, i) =>\n                  i === index ? { ...l, proficiency: value as any } : l\n                ),\n              }))}\n              options={[\n                { value: 'Basic', label: 'Basic' },\n                { value: 'Conversational', label: 'Conversational' },\n                { value: 'Fluent', label: 'Fluent' },\n                { value: 'Native', label: 'Native' },\n              ]}\n              required\n            />\n          </div>\n        ))}\n      </div>\n      {cvData.languages.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <p>No languages added yet. Click \"Add Language\" to get started.</p>\n        </div>\n      )}\n    </div>\n  );\n\n  const renderCurrentStep = () => {\n    switch (steps[currentStep].component) {\n      case 'basic':\n        return renderBasicInfo();\n      case 'personal':\n        return renderPersonalInfo();\n      default:\n        return renderBasicInfo();\n    }\n  };\n\n  return (\n    <div style={{ maxWidth: '64rem', margin: '0 auto', padding: '2rem 1rem' }}>\n      {/* Header */}\n      <div style={{ marginBottom: '2rem' }}>\n        <h1 style={{ fontSize: '1.875rem', fontWeight: 'bold', color: '#111827', marginBottom: '0.5rem' }}>\n          {id && id !== 'new' ? 'Edit CV' : 'Create New CV'}\n        </h1>\n        <p style={{ color: '#6b7280' }}>\n          Fill out the information below to create your professional CV\n        </p>\n      </div>\n\n      {/* Progress Steps */}\n      <div style={{ marginBottom: '2rem' }}>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          {steps.map((step, index) => (\n            <div key={step.title} style={{ display: 'flex', alignItems: 'center', marginRight: index !== steps.length - 1 ? '2rem' : '0' }}>\n              <div\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  width: '2rem',\n                  height: '2rem',\n                  borderRadius: '50%',\n                  backgroundColor: index < currentStep ? '#3b82f6' : index === currentStep ? 'white' : 'white',\n                  border: index === currentStep ? '2px solid #3b82f6' : '2px solid #d1d5db',\n                  color: index < currentStep ? 'white' : index === currentStep ? '#3b82f6' : '#6b7280',\n                  fontSize: '0.875rem',\n                  fontWeight: '500'\n                }}\n              >\n                {index < currentStep ? (\n                  <svg style={{ width: '1.25rem', height: '1.25rem' }} fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                ) : (\n                  index + 1\n                )}\n              </div>\n              <span style={{\n                marginLeft: '1rem',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                color: index === currentStep ? '#3b82f6' : '#6b7280'\n              }}>\n                {step.title}\n              </span>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Form Content */}\n      <div style={{ backgroundColor: 'white', boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)', borderRadius: '0.5rem', padding: '1.5rem', marginBottom: '2rem' }}>\n        {renderCurrentStep()}\n      </div>\n\n      {/* Navigation Buttons */}\n      <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n        <div>\n          {currentStep > 0 && (\n            <Button\n              onClick={handlePrevious}\n              variant=\"outline\"\n            >\n              Previous\n            </Button>\n          )}\n        </div>\n        <div style={{ display: 'flex', gap: '1rem' }}>\n          <Button\n            onClick={handleSave}\n            variant=\"outline\"\n          >\n            Save Draft\n          </Button>\n          {currentStep < steps.length - 1 ? (\n            <Button onClick={handleNext}>\n              Next\n            </Button>\n          ) : (\n            <Button onClick={() => navigate(`/dashboard`)}>\n              Finish\n            </Button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CVBuilder;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,QAAQ,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAuBjD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAG,CAAC,GAAGX,SAAS,CAAiB,CAAC;EAC1C,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,SAAS;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC;EAAa,CAAC,GAAGf,KAAK,CAAC,CAAC;EAExE,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,OAAO,CAAC;EAC/C,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAgD,QAAQ,CAAC;EACzG,MAAM,CAAC0B,MAAM,EAAEC,SAAS,CAAC,GAAG3B,QAAQ,CAAS;IAC3C4B,aAAa,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE;IACX,CAAC;IACDC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,KAAK,GAAG,CACZ;IAAEC,KAAK,EAAE,YAAY;IAAEC,SAAS,EAAE;EAAQ,CAAC,EAC3C;IAAED,KAAK,EAAE,eAAe;IAAEC,SAAS,EAAE;EAAW,CAAC,CAClD;EAED5C,SAAS,CAAC,MAAM;IACd,IAAIY,EAAE,IAAIA,EAAE,KAAK,KAAK,EAAE;MACtBG,OAAO,CAAC8B,QAAQ,CAACjC,EAAE,CAAC,CAAC;IACvB,CAAC,MAAM;MACLM,YAAY,CAAC,IAAI,CAAC;IACpB;EACF,CAAC,EAAE,CAACN,EAAE,EAAEG,OAAO,EAAEG,YAAY,CAAC,CAAC;EAE/BlB,SAAS,CAAC,MAAM;IACd,IAAIc,SAAS,EAAE;MACbQ,UAAU,CAACR,SAAS,CAAC6B,KAAK,CAAC;MAC3BnB,eAAe,CAACV,SAAS,CAACgC,aAAa,CAAC;MACxCpB,SAAS,CAACZ,SAAS,CAACiC,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACjC,SAAS,CAAC,CAAC;EAEf,MAAMkC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,SAAS,GAAG;QAChBN,KAAK,EAAEtB,OAAO;QACdyB,aAAa,EAAEvB,YAAY;QAC3BwB,OAAO,EAAEtB;MACX,CAAC;MAED,IAAIb,EAAE,IAAIA,EAAE,KAAK,KAAK,EAAE;QACtB,MAAMK,QAAQ,CAAC4B,QAAQ,CAACjC,EAAE,CAAC,EAAEqC,SAAS,CAAC;MACzC,CAAC,MAAM;QACL,MAAMC,KAAK,GAAG,MAAMlC,QAAQ,CAACiC,SAAS,CAAC;QACvCpC,QAAQ,CAAC,eAAeqC,KAAK,CAACtC,EAAE,EAAE,EAAE;UAAEuC,OAAO,EAAE;QAAK,CAAC,CAAC;MACxD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C;EACF,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAInC,WAAW,GAAGuB,KAAK,CAACa,MAAM,GAAG,CAAC,EAAE;MAClCnC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMqC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIrC,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMsC,kBAAkB,GAAGA,CAACC,KAAyB,EAAEC,KAAa,KAAK;IACvEjC,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPjC,aAAa,EAAE;QACb,GAAGiC,IAAI,CAACjC,aAAa;QACrB,CAAC+B,KAAK,GAAGC;MACX;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,YAAuB,GAAG;MAC9BlD,EAAE,EAAEmD,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,EAAE;MACVC,cAAc,EAAE,EAAE;MAClBC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,KAAK;MACdC,WAAW,EAAE,EAAE;MACfC,GAAG,EAAE;IACP,CAAC;IACD/C,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPvB,SAAS,EAAE,CAAC,GAAGuB,IAAI,CAACvB,SAAS,EAAEyB,YAAY;IAC7C,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMY,eAAe,GAAGA,CAACC,KAAa,EAAEjB,KAAsB,EAAEC,KAAuB,KAAK;IAC1FjC,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPvB,SAAS,EAAEuB,IAAI,CAACvB,SAAS,CAACuC,GAAG,CAAC,CAACC,GAAG,EAAEC,CAAC,KACnCA,CAAC,KAAKH,KAAK,GAAG;QAAE,GAAGE,GAAG;QAAE,CAACnB,KAAK,GAAGC;MAAM,CAAC,GAAGkB,GAC7C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,eAAe,GAAIJ,KAAa,IAAK;IACzCjD,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPvB,SAAS,EAAEuB,IAAI,CAACvB,SAAS,CAAC2C,MAAM,CAAC,CAACC,CAAC,EAAEH,CAAC,KAAKA,CAAC,KAAKH,KAAK;IACxD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMO,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,aAAyB,GAAG;MAChCvE,EAAE,EAAEmD,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzBmB,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,EAAE;MACZhB,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,KAAK;MACdC,WAAW,EAAE,EAAE;MACfc,YAAY,EAAE;IAChB,CAAC;IACD5D,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPtB,UAAU,EAAE,CAAC,GAAGsB,IAAI,CAACtB,UAAU,EAAE6C,aAAa;IAChD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAACZ,KAAa,EAAEjB,KAAuB,EAAEC,KAAkC,KAAK;IACvGjC,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPtB,UAAU,EAAEsB,IAAI,CAACtB,UAAU,CAACsC,GAAG,CAAC,CAACY,GAAG,EAAEV,CAAC,KACrCA,CAAC,KAAKH,KAAK,GAAG;QAAE,GAAGa,GAAG;QAAE,CAAC9B,KAAK,GAAGC;MAAM,CAAC,GAAG6B,GAC7C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,gBAAgB,GAAId,KAAa,IAAK;IAC1CjD,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPtB,UAAU,EAAEsB,IAAI,CAACtB,UAAU,CAAC0C,MAAM,CAAC,CAACC,CAAC,EAAEH,CAAC,KAAKA,CAAC,KAAKH,KAAK;IAC1D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMe,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAMC,QAAe,GAAG;MACtB/E,EAAE,EAAEmD,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;MACzB2B,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,cAAc;MACrBC,QAAQ,EAAE;IACZ,CAAC;IACDpE,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPrB,MAAM,EAAE,CAAC,GAAGqB,IAAI,CAACrB,MAAM,EAAEoD,QAAQ;IACnC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,WAAW,GAAGA,CAACpB,KAAa,EAAEjB,KAAkB,EAAEC,KAAa,KAAK;IACxEjC,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPrB,MAAM,EAAEqB,IAAI,CAACrB,MAAM,CAACqC,GAAG,CAAC,CAACoB,KAAK,EAAElB,CAAC,KAC/BA,CAAC,KAAKH,KAAK,GAAG;QAAE,GAAGqB,KAAK;QAAE,CAACtC,KAAK,GAAGC;MAAM,CAAC,GAAGqC,KAC/C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,WAAW,GAAItB,KAAa,IAAK;IACrCjD,SAAS,CAACkC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPrB,MAAM,EAAEqB,IAAI,CAACrB,MAAM,CAACyC,MAAM,CAAC,CAACC,CAAC,EAAEH,CAAC,KAAKA,CAAC,KAAKH,KAAK;IAClD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMuB,eAAe,GAAGA,CAAA,kBACtBzF,OAAA;IAAA0F,QAAA,gBACE1F,OAAA;MAAI2F,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,UAAU,EAAE,KAAK;QAAEC,KAAK,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAS,CAAE;MAAAL,QAAA,EAAC;IAElG;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACLnG,OAAA;MAAK2F,KAAK,EAAE;QAAES,OAAO,EAAE,MAAM;QAAEC,mBAAmB,EAAE,sCAAsC;QAAEC,GAAG,EAAE;MAAS,CAAE;MAAAZ,QAAA,gBAC1G1F,OAAA,CAACJ,KAAK;QACJ2G,KAAK,EAAC,UAAU;QAChBC,WAAW,EAAC,gCAAgC;QAC5CtD,KAAK,EAAEtC,OAAQ;QACf6F,QAAQ,EAAE5F,UAAW;QACrB6F,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFnG,OAAA,CAACH,MAAM;QACL0G,KAAK,EAAC,UAAU;QAChBrD,KAAK,EAAEpC,YAAa;QACpB2F,QAAQ,EAAGvD,KAAK,IAAKnC,eAAe,CAACmC,KAAY,CAAE;QACnDyD,OAAO,EAAE,CACP;UAAEzD,KAAK,EAAE,QAAQ;UAAEqD,KAAK,EAAE;QAAS,CAAC,EACpC;UAAErD,KAAK,EAAE,SAAS;UAAEqD,KAAK,EAAE;QAAU,CAAC,EACtC;UAAErD,KAAK,EAAE,UAAU;UAAEqD,KAAK,EAAE;QAAW,CAAC,EACxC;UAAErD,KAAK,EAAE,SAAS;UAAEqD,KAAK,EAAE;QAAU,CAAC,CACtC;QACFG,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMS,kBAAkB,GAAGA,CAAA,kBACzB5G,OAAA;IAAA0F,QAAA,gBACE1F,OAAA;MAAI2F,KAAK,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,UAAU,EAAE,KAAK;QAAEC,KAAK,EAAE,SAAS;QAAEC,YAAY,EAAE;MAAS,CAAE;MAAAL,QAAA,EAAC;IAElG;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACLnG,OAAA;MAAK2F,KAAK,EAAE;QAAES,OAAO,EAAE,MAAM;QAAEC,mBAAmB,EAAE,sCAAsC;QAAEC,GAAG,EAAE,MAAM;QAAEP,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBAC9H1F,OAAA,CAACJ,KAAK;QACJ2G,KAAK,EAAC,YAAY;QAClBC,WAAW,EAAC,MAAM;QAClBtD,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACC,UAAW;QACvCsF,QAAQ,EAAGvD,KAAK,IAAKF,kBAAkB,CAAC,YAAY,EAAEE,KAAK,CAAE;QAC7DwD,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFnG,OAAA,CAACJ,KAAK;QACJ2G,KAAK,EAAC,WAAW;QACjBC,WAAW,EAAC,KAAK;QACjBtD,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACE,SAAU;QACtCqF,QAAQ,EAAGvD,KAAK,IAAKF,kBAAkB,CAAC,WAAW,EAAEE,KAAK,CAAE;QAC5DwD,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFnG,OAAA,CAACJ,KAAK;QACJ2G,KAAK,EAAC,OAAO;QACbM,IAAI,EAAC,OAAO;QACZL,WAAW,EAAC,sBAAsB;QAClCtD,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACG,KAAM;QAClCoF,QAAQ,EAAGvD,KAAK,IAAKF,kBAAkB,CAAC,OAAO,EAAEE,KAAK,CAAE;QACxDwD,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFnG,OAAA,CAACJ,KAAK;QACJ2G,KAAK,EAAC,OAAO;QACbM,IAAI,EAAC,KAAK;QACVL,WAAW,EAAC,mBAAmB;QAC/BtD,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACI,KAAK,IAAI,EAAG;QACxCmF,QAAQ,EAAGvD,KAAK,IAAKF,kBAAkB,CAAC,OAAO,EAAEE,KAAK;MAAE;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNnG,OAAA;MAAK2F,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,eACnC1F,OAAA,CAACJ,KAAK;QACJ2G,KAAK,EAAC,SAAS;QACfC,WAAW,EAAC,gCAAgC;QAC5CtD,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACK,OAAO,IAAI,EAAG;QAC1CkF,QAAQ,EAAGvD,KAAK,IAAKF,kBAAkB,CAAC,SAAS,EAAEE,KAAK;MAAE;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNnG,OAAA;MAAK2F,KAAK,EAAE;QAAES,OAAO,EAAE,MAAM;QAAEC,mBAAmB,EAAE,sCAAsC;QAAEC,GAAG,EAAE,MAAM;QAAEP,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBAC9H1F,OAAA,CAACJ,KAAK;QACJ2G,KAAK,EAAC,SAAS;QACfM,IAAI,EAAC,KAAK;QACVL,WAAW,EAAC,qBAAqB;QACjCtD,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACM,OAAO,IAAI,EAAG;QAC1CiF,QAAQ,EAAGvD,KAAK,IAAKF,kBAAkB,CAAC,SAAS,EAAEE,KAAK;MAAE;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACFnG,OAAA,CAACJ,KAAK;QACJ2G,KAAK,EAAC,UAAU;QAChBM,IAAI,EAAC,KAAK;QACVL,WAAW,EAAC,iCAAiC;QAC7CtD,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACO,QAAQ,IAAI,EAAG;QAC3CgF,QAAQ,EAAGvD,KAAK,IAAKF,kBAAkB,CAAC,UAAU,EAAEE,KAAK;MAAE;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNnG,OAAA;MAAK2F,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,eACnC1F,OAAA,CAACJ,KAAK;QACJ2G,KAAK,EAAC,QAAQ;QACdM,IAAI,EAAC,KAAK;QACVL,WAAW,EAAC,4BAA4B;QACxCtD,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACQ,MAAM,IAAI,EAAG;QACzC+E,QAAQ,EAAGvD,KAAK,IAAKF,kBAAkB,CAAC,QAAQ,EAAEE,KAAK;MAAE;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNnG,OAAA,CAACF,QAAQ;MACPyG,KAAK,EAAC,sBAAsB;MAC5BC,WAAW,EAAC,gEAAgE;MAC5EtD,KAAK,EAAElC,MAAM,CAACE,aAAa,CAACS,OAAO,IAAI,EAAG;MAC1C8E,QAAQ,EAAGvD,KAAK,IAAKF,kBAAkB,CAAC,SAAS,EAAEE,KAAK,CAAE;MAC1D4D,IAAI,EAAE;IAAE;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,MAAMY,eAAe,GAAGA,CAAA,kBACtB/G,OAAA;IAAKgH,SAAS,EAAC,WAAW;IAAAtB,QAAA,gBACxB1F,OAAA;MAAKgH,SAAS,EAAC,mCAAmC;MAAAtB,QAAA,gBAChD1F,OAAA;QAAIgH,SAAS,EAAC,mCAAmC;QAAAtB,QAAA,EAAC;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChEnG,OAAA,CAACL,MAAM;QAACsH,OAAO,EAAE7D,YAAa;QAAC8D,IAAI,EAAC,IAAI;QAAAxB,QAAA,EAAC;MAAa;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC,EACLnF,MAAM,CAACY,SAAS,CAACuC,GAAG,CAAC,CAACC,GAAG,EAAEF,KAAK,kBAC/BlE,OAAA;MAAkBgH,SAAS,EAAC,iDAAiD;MAAAtB,QAAA,gBAC3E1F,OAAA;QAAKgH,SAAS,EAAC,kCAAkC;QAAAtB,QAAA,gBAC/C1F,OAAA;UAAIgH,SAAS,EAAC,mCAAmC;UAAAtB,QAAA,GAAC,aAAW,EAACxB,KAAK,GAAG,CAAC;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7EnG,OAAA,CAACL,MAAM;UACLsH,OAAO,EAAEA,CAAA,KAAM3C,eAAe,CAACJ,KAAK,CAAE;UACtCiD,OAAO,EAAC,QAAQ;UAChBD,IAAI,EAAC,IAAI;UAAAxB,QAAA,EACV;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnG,OAAA;QAAKgH,SAAS,EAAC,uCAAuC;QAAAtB,QAAA,gBACpD1F,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,aAAa;UACnBC,WAAW,EAAC,uBAAuB;UACnCtD,KAAK,EAAEkB,GAAG,CAACX,WAAY;UACvBgD,QAAQ,EAAGvD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,aAAa,EAAEhB,KAAK,CAAE;UAClEwD,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,QAAQ;UACdC,WAAW,EAAC,qBAAqB;UACjCtD,KAAK,EAAEkB,GAAG,CAACV,MAAO;UAClB+C,QAAQ,EAAGvD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,QAAQ,EAAEhB,KAAK,CAAE;UAC7DwD,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,gBAAgB;UACtBC,WAAW,EAAC,kBAAkB;UAC9BtD,KAAK,EAAEkB,GAAG,CAACT,cAAe;UAC1B8C,QAAQ,EAAGvD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,gBAAgB,EAAEhB,KAAK,CAAE;UACrEwD,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,gBAAgB;UACtBC,WAAW,EAAC,KAAK;UACjBtD,KAAK,EAAEkB,GAAG,CAACJ,GAAG,IAAI,EAAG;UACrByC,QAAQ,EAAGvD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,KAAK,EAAEhB,KAAK;QAAE;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACFnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,YAAY;UAClBM,IAAI,EAAC,MAAM;UACX3D,KAAK,EAAEkB,GAAG,CAACR,UAAW;UACtB6C,QAAQ,EAAGvD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,YAAY,EAAEhB,KAAK,CAAE;UACjEwD,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,UAAU;UAChBM,IAAI,EAAC,MAAM;UACX3D,KAAK,EAAEkB,GAAG,CAACP,QAAQ,IAAI,EAAG;UAC1B4C,QAAQ,EAAGvD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,UAAU,EAAEhB,KAAK,CAAE;UAC/DkE,QAAQ,EAAEhD,GAAG,CAACN;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNnG,OAAA;QAAKgH,SAAS,EAAC,mBAAmB;QAAAtB,QAAA,gBAChC1F,OAAA;UACE6G,IAAI,EAAC,UAAU;UACf1G,EAAE,EAAE,qBAAqB+D,KAAK,EAAG;UACjCmD,OAAO,EAAEjD,GAAG,CAACN,OAAQ;UACrB2C,QAAQ,EAAGa,CAAC,IAAKrD,eAAe,CAACC,KAAK,EAAE,SAAS,EAAEoD,CAAC,CAACC,MAAM,CAACF,OAAO,CAAE;UACrEL,SAAS,EAAC;QAAyE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eACFnG,OAAA;UAAOwH,OAAO,EAAE,qBAAqBtD,KAAK,EAAG;UAAC8C,SAAS,EAAC,4BAA4B;UAAAtB,QAAA,EAAC;QAErF;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNnG,OAAA,CAACF,QAAQ;QACPyG,KAAK,EAAC,wBAAwB;QAC9BC,WAAW,EAAC,kDAAkD;QAC9DtD,KAAK,EAAEkB,GAAG,CAACL,WAAW,IAAI,EAAG;QAC7B0C,QAAQ,EAAGvD,KAAK,IAAKe,eAAe,CAACC,KAAK,EAAE,aAAa,EAAEhB,KAAK,CAAE;QAClE4D,IAAI,EAAE;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA,GAxEM/B,GAAG,CAACjE,EAAE;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAyEX,CACN,CAAC,EACDnF,MAAM,CAACY,SAAS,CAACkB,MAAM,KAAK,CAAC,iBAC5B9C,OAAA;MAAKgH,SAAS,EAAC,gCAAgC;MAAAtB,QAAA,eAC7C1F,OAAA;QAAA0F,QAAA,EAAG;MAA+D;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMsB,gBAAgB,GAAGA,CAAA,kBACvBzH,OAAA;IAAKgH,SAAS,EAAC,WAAW;IAAAtB,QAAA,gBACxB1F,OAAA;MAAKgH,SAAS,EAAC,mCAAmC;MAAAtB,QAAA,gBAChD1F,OAAA;QAAIgH,SAAS,EAAC,mCAAmC;QAAAtB,QAAA,EAAC;MAAe;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtEnG,OAAA,CAACL,MAAM;QAACsH,OAAO,EAAExC,aAAc;QAACyC,IAAI,EAAC,IAAI;QAAAxB,QAAA,EAAC;MAAc;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,EACLnF,MAAM,CAACa,UAAU,CAACsC,GAAG,CAAC,CAACY,GAAG,EAAEb,KAAK,kBAChClE,OAAA;MAAkBgH,SAAS,EAAC,iDAAiD;MAAAtB,QAAA,gBAC3E1F,OAAA;QAAKgH,SAAS,EAAC,kCAAkC;QAAAtB,QAAA,gBAC/C1F,OAAA;UAAIgH,SAAS,EAAC,mCAAmC;UAAAtB,QAAA,GAAC,cAAY,EAACxB,KAAK,GAAG,CAAC;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9EnG,OAAA,CAACL,MAAM;UACLsH,OAAO,EAAEA,CAAA,KAAMjC,gBAAgB,CAACd,KAAK,CAAE;UACvCiD,OAAO,EAAC,QAAQ;UAChBD,IAAI,EAAC,IAAI;UAAAxB,QAAA,EACV;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnG,OAAA;QAAKgH,SAAS,EAAC,uCAAuC;QAAAtB,QAAA,gBACpD1F,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,SAAS;UACfC,WAAW,EAAC,mBAAmB;UAC/BtD,KAAK,EAAE6B,GAAG,CAACJ,OAAQ;UACnB8B,QAAQ,EAAGvD,KAAK,IAAK4B,gBAAgB,CAACZ,KAAK,EAAE,SAAS,EAAEhB,KAAK,CAAE;UAC/DwD,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,UAAU;UAChBC,WAAW,EAAC,mBAAmB;UAC/BtD,KAAK,EAAE6B,GAAG,CAACH,QAAS;UACpB6B,QAAQ,EAAGvD,KAAK,IAAK4B,gBAAgB,CAACZ,KAAK,EAAE,UAAU,EAAEhB,KAAK,CAAE;UAChEwD,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,YAAY;UAClBM,IAAI,EAAC,MAAM;UACX3D,KAAK,EAAE6B,GAAG,CAACnB,UAAW;UACtB6C,QAAQ,EAAGvD,KAAK,IAAK4B,gBAAgB,CAACZ,KAAK,EAAE,YAAY,EAAEhB,KAAK,CAAE;UAClEwD,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,UAAU;UAChBM,IAAI,EAAC,MAAM;UACX3D,KAAK,EAAE6B,GAAG,CAAClB,QAAQ,IAAI,EAAG;UAC1B4C,QAAQ,EAAGvD,KAAK,IAAK4B,gBAAgB,CAACZ,KAAK,EAAE,UAAU,EAAEhB,KAAK,CAAE;UAChEkE,QAAQ,EAAErC,GAAG,CAACjB;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNnG,OAAA;QAAKgH,SAAS,EAAC,mBAAmB;QAAAtB,QAAA,gBAChC1F,OAAA;UACE6G,IAAI,EAAC,UAAU;UACf1G,EAAE,EAAE,sBAAsB+D,KAAK,EAAG;UAClCmD,OAAO,EAAEtC,GAAG,CAACjB,OAAQ;UACrB2C,QAAQ,EAAGa,CAAC,IAAKxC,gBAAgB,CAACZ,KAAK,EAAE,SAAS,EAAEoD,CAAC,CAACC,MAAM,CAACF,OAAO,CAAE;UACtEL,SAAS,EAAC;QAAyE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eACFnG,OAAA;UAAOwH,OAAO,EAAE,sBAAsBtD,KAAK,EAAG;UAAC8C,SAAS,EAAC,4BAA4B;UAAAtB,QAAA,EAAC;QAEtF;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNnG,OAAA,CAACF,QAAQ;QACPyG,KAAK,EAAC,iBAAiB;QACvBC,WAAW,EAAC,oDAAoD;QAChEtD,KAAK,EAAE6B,GAAG,CAAChB,WAAW,IAAI,EAAG;QAC7B0C,QAAQ,EAAGvD,KAAK,IAAK4B,gBAAgB,CAACZ,KAAK,EAAE,aAAa,EAAEhB,KAAK,CAAE;QACnE4D,IAAI,EAAE;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA,GA3DMpB,GAAG,CAAC5E,EAAE;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA4DX,CACN,CAAC,EACDnF,MAAM,CAACa,UAAU,CAACiB,MAAM,KAAK,CAAC,iBAC7B9C,OAAA;MAAKgH,SAAS,EAAC,gCAAgC;MAAAtB,QAAA,eAC7C1F,OAAA;QAAA0F,QAAA,EAAG;MAAsE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1E,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMuB,YAAY,GAAGA,CAAA,kBACnB1H,OAAA;IAAKgH,SAAS,EAAC,WAAW;IAAAtB,QAAA,gBACxB1F,OAAA;MAAKgH,SAAS,EAAC,mCAAmC;MAAAtB,QAAA,gBAChD1F,OAAA;QAAIgH,SAAS,EAAC,mCAAmC;QAAAtB,QAAA,EAAC;MAAM;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7DnG,OAAA,CAACL,MAAM;QAACsH,OAAO,EAAEhC,QAAS;QAACiC,IAAI,EAAC,IAAI;QAAAxB,QAAA,EAAC;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC,eACNnG,OAAA;MAAKgH,SAAS,EAAC,uCAAuC;MAAAtB,QAAA,EACnD1E,MAAM,CAACc,MAAM,CAACqC,GAAG,CAAC,CAACoB,KAAK,EAAErB,KAAK,kBAC9BlE,OAAA;QAAoBgH,SAAS,EAAC,iDAAiD;QAAAtB,QAAA,gBAC7E1F,OAAA;UAAKgH,SAAS,EAAC,kCAAkC;UAAAtB,QAAA,gBAC/C1F,OAAA;YAAIgH,SAAS,EAAC,mCAAmC;YAAAtB,QAAA,GAAC,SAAO,EAACxB,KAAK,GAAG,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzEnG,OAAA,CAACL,MAAM;YACLsH,OAAO,EAAEA,CAAA,KAAMzB,WAAW,CAACtB,KAAK,CAAE;YAClCiD,OAAO,EAAC,QAAQ;YAChBD,IAAI,EAAC,IAAI;YAAAxB,QAAA,EACV;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,YAAY;UAClBC,WAAW,EAAC,YAAY;UACxBtD,KAAK,EAAEqC,KAAK,CAACJ,IAAK;UAClBsB,QAAQ,EAAGvD,KAAK,IAAKoC,WAAW,CAACpB,KAAK,EAAE,MAAM,EAAEhB,KAAK,CAAE;UACvDwD,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnG,OAAA,CAACH,MAAM;UACL0G,KAAK,EAAC,mBAAmB;UACzBrD,KAAK,EAAEqC,KAAK,CAACH,KAAM;UACnBqB,QAAQ,EAAGvD,KAAK,IAAKoC,WAAW,CAACpB,KAAK,EAAE,OAAO,EAAEhB,KAAK,CAAE;UACxDyD,OAAO,EAAE,CACP;YAAEzD,KAAK,EAAE,UAAU;YAAEqD,KAAK,EAAE;UAAW,CAAC,EACxC;YAAErD,KAAK,EAAE,cAAc;YAAEqD,KAAK,EAAE;UAAe,CAAC,EAChD;YAAErD,KAAK,EAAE,UAAU;YAAEqD,KAAK,EAAE;UAAW,CAAC,EACxC;YAAErD,KAAK,EAAE,QAAQ;YAAEqD,KAAK,EAAE;UAAS,CAAC,CACpC;UACFG,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,qBAAqB;UAC3BC,WAAW,EAAC,uBAAuB;UACnCtD,KAAK,EAAEqC,KAAK,CAACF,QAAQ,IAAI,EAAG;UAC5BoB,QAAQ,EAAGvD,KAAK,IAAKoC,WAAW,CAACpB,KAAK,EAAE,UAAU,EAAEhB,KAAK;QAAE;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA,GAnCMZ,KAAK,CAACpF,EAAE;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoCb,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EACLnF,MAAM,CAACc,MAAM,CAACgB,MAAM,KAAK,CAAC,iBACzB9C,OAAA;MAAKgH,SAAS,EAAC,gCAAgC;MAAAtB,QAAA,eAC7C1F,OAAA;QAAA0F,QAAA,EAAG;MAAsD;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMwB,cAAc,GAAGA,CAAA,kBACrB3H,OAAA;IAAKgH,SAAS,EAAC,WAAW;IAAAtB,QAAA,gBACxB1F,OAAA;MAAKgH,SAAS,EAAC,mCAAmC;MAAAtB,QAAA,gBAChD1F,OAAA;QAAIgH,SAAS,EAAC,mCAAmC;QAAAtB,QAAA,EAAC;MAAQ;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/DnG,OAAA,CAACL,MAAM;QAACsH,OAAO,EAAEA,CAAA,KAAM;UACrB,MAAMW,UAAmB,GAAG;YAC1BzH,EAAE,EAAEmD,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;YACzB2B,IAAI,EAAE,EAAE;YACRpB,WAAW,EAAE,EAAE;YACf8D,YAAY,EAAE,EAAE;YAChBjE,UAAU,EAAE,EAAE;YACdC,QAAQ,EAAE,EAAE;YACZiE,GAAG,EAAE,EAAE;YACPC,UAAU,EAAE;UACd,CAAC;UACD9G,SAAS,CAACkC,IAAI,KAAK;YACjB,GAAGA,IAAI;YACPpB,QAAQ,EAAE,CAAC,GAAGoB,IAAI,CAACpB,QAAQ,EAAE6F,UAAU;UACzC,CAAC,CAAC,CAAC;QACL,CAAE;QAACV,IAAI,EAAC,IAAI;QAAAxB,QAAA,EAAC;MAAW;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,EACLnF,MAAM,CAACe,QAAQ,CAACoC,GAAG,CAAC,CAAC6D,OAAO,EAAE9D,KAAK,kBAClClE,OAAA;MAAsBgH,SAAS,EAAC,iDAAiD;MAAAtB,QAAA,gBAC/E1F,OAAA;QAAKgH,SAAS,EAAC,kCAAkC;QAAAtB,QAAA,gBAC/C1F,OAAA;UAAIgH,SAAS,EAAC,mCAAmC;UAAAtB,QAAA,GAAC,WAAS,EAACxB,KAAK,GAAG,CAAC;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3EnG,OAAA,CAACL,MAAM;UACLsH,OAAO,EAAEA,CAAA,KAAMhG,SAAS,CAACkC,IAAI,KAAK;YAChC,GAAGA,IAAI;YACPpB,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,CAACwC,MAAM,CAAC,CAACC,CAAC,EAAEH,CAAC,KAAKA,CAAC,KAAKH,KAAK;UACtD,CAAC,CAAC,CAAE;UACJiD,OAAO,EAAC,QAAQ;UAChBD,IAAI,EAAC,IAAI;UAAAxB,QAAA,EACV;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNnG,OAAA;QAAKgH,SAAS,EAAC,uCAAuC;QAAAtB,QAAA,gBACpD1F,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,cAAc;UACpBC,WAAW,EAAC,oBAAoB;UAChCtD,KAAK,EAAE8E,OAAO,CAAC7C,IAAK;UACpBsB,QAAQ,EAAGvD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;YACtC,GAAGA,IAAI;YACPpB,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,CAACoC,GAAG,CAAC,CAAC8D,CAAC,EAAE5D,CAAC,KAC/BA,CAAC,KAAKH,KAAK,GAAG;cAAE,GAAG+D,CAAC;cAAE9C,IAAI,EAAEjC;YAAM,CAAC,GAAG+E,CACxC;UACF,CAAC,CAAC,CAAE;UACJvB,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,cAAc;UACpBC,WAAW,EAAC,yBAAyB;UACrCtD,KAAK,EAAE8E,OAAO,CAACH,YAAY,CAACK,IAAI,CAAC,IAAI,CAAE;UACvCzB,QAAQ,EAAGvD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;YACtC,GAAGA,IAAI;YACPpB,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,CAACoC,GAAG,CAAC,CAAC8D,CAAC,EAAE5D,CAAC,KAC/BA,CAAC,KAAKH,KAAK,GAAG;cAAE,GAAG+D,CAAC;cAAEJ,YAAY,EAAE3E,KAAK,CAACiF,KAAK,CAAC,GAAG,CAAC,CAAChE,GAAG,CAACiE,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC;YAAE,CAAC,GAAGJ,CAC9E;UACF,CAAC,CAAC;QAAE;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACFnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,wBAAwB;UAC9BM,IAAI,EAAC,KAAK;UACVL,WAAW,EAAC,uBAAuB;UACnCtD,KAAK,EAAE8E,OAAO,CAACF,GAAG,IAAI,EAAG;UACzBrB,QAAQ,EAAGvD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;YACtC,GAAGA,IAAI;YACPpB,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,CAACoC,GAAG,CAAC,CAAC8D,CAAC,EAAE5D,CAAC,KAC/BA,CAAC,KAAKH,KAAK,GAAG;cAAE,GAAG+D,CAAC;cAAEH,GAAG,EAAE5E;YAAM,CAAC,GAAG+E,CACvC;UACF,CAAC,CAAC;QAAE;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACFnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,uBAAuB;UAC7BM,IAAI,EAAC,KAAK;UACVL,WAAW,EAAC,iCAAiC;UAC7CtD,KAAK,EAAE8E,OAAO,CAACD,UAAU,IAAI,EAAG;UAChCtB,QAAQ,EAAGvD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;YACtC,GAAGA,IAAI;YACPpB,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,CAACoC,GAAG,CAAC,CAAC8D,CAAC,EAAE5D,CAAC,KAC/BA,CAAC,KAAKH,KAAK,GAAG;cAAE,GAAG+D,CAAC;cAAEF,UAAU,EAAE7E;YAAM,CAAC,GAAG+E,CAC9C;UACF,CAAC,CAAC;QAAE;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNnG,OAAA,CAACF,QAAQ;QACPyG,KAAK,EAAC,qBAAqB;QAC3BC,WAAW,EAAC,iDAAiD;QAC7DtD,KAAK,EAAE8E,OAAO,CAACjE,WAAY;QAC3B0C,QAAQ,EAAGvD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;UACtC,GAAGA,IAAI;UACPpB,QAAQ,EAAEoB,IAAI,CAACpB,QAAQ,CAACoC,GAAG,CAAC,CAAC8D,CAAC,EAAE5D,CAAC,KAC/BA,CAAC,KAAKH,KAAK,GAAG;YAAE,GAAG+D,CAAC;YAAElE,WAAW,EAAEb;UAAM,CAAC,GAAG+E,CAC/C;QACF,CAAC,CAAC,CAAE;QACJnB,IAAI,EAAE;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA,GA1EM6B,OAAO,CAAC7H,EAAE;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA2Ef,CACN,CAAC,EACDnF,MAAM,CAACe,QAAQ,CAACe,MAAM,KAAK,CAAC,iBAC3B9C,OAAA;MAAKgH,SAAS,EAAC,gCAAgC;MAAAtB,QAAA,eAC7C1F,OAAA;QAAA0F,QAAA,EAAG;MAA0D;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMmC,eAAe,GAAGA,CAAA,kBACtBtI,OAAA;IAAKgH,SAAS,EAAC,WAAW;IAAAtB,QAAA,gBACxB1F,OAAA;MAAKgH,SAAS,EAAC,mCAAmC;MAAAtB,QAAA,gBAChD1F,OAAA;QAAIgH,SAAS,EAAC,mCAAmC;QAAAtB,QAAA,EAAC;MAAS;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChEnG,OAAA,CAACL,MAAM;QAACsH,OAAO,EAAEA,CAAA,KAAM;UACrB,MAAMsB,WAAqB,GAAG;YAC5BpI,EAAE,EAAEmD,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;YACzB2B,IAAI,EAAE,EAAE;YACRqD,WAAW,EAAE;UACf,CAAC;UACDvH,SAAS,CAACkC,IAAI,KAAK;YACjB,GAAGA,IAAI;YACPnB,SAAS,EAAE,CAAC,GAAGmB,IAAI,CAACnB,SAAS,EAAEuG,WAAW;UAC5C,CAAC,CAAC,CAAC;QACL,CAAE;QAACrB,IAAI,EAAC,IAAI;QAAAxB,QAAA,EAAC;MAAY;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eACNnG,OAAA;MAAKgH,SAAS,EAAC,uCAAuC;MAAAtB,QAAA,EACnD1E,MAAM,CAACgB,SAAS,CAACmC,GAAG,CAAC,CAACsE,QAAQ,EAAEvE,KAAK,kBACpClE,OAAA;QAAuBgH,SAAS,EAAC,iDAAiD;QAAAtB,QAAA,gBAChF1F,OAAA;UAAKgH,SAAS,EAAC,kCAAkC;UAAAtB,QAAA,gBAC/C1F,OAAA;YAAIgH,SAAS,EAAC,mCAAmC;YAAAtB,QAAA,GAAC,YAAU,EAACxB,KAAK,GAAG,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5EnG,OAAA,CAACL,MAAM;YACLsH,OAAO,EAAEA,CAAA,KAAMhG,SAAS,CAACkC,IAAI,KAAK;cAChC,GAAGA,IAAI;cACPnB,SAAS,EAAEmB,IAAI,CAACnB,SAAS,CAACuC,MAAM,CAAC,CAACC,CAAC,EAAEH,CAAC,KAAKA,CAAC,KAAKH,KAAK;YACxD,CAAC,CAAC,CAAE;YACJiD,OAAO,EAAC,QAAQ;YAChBD,IAAI,EAAC,IAAI;YAAAxB,QAAA,EACV;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNnG,OAAA,CAACJ,KAAK;UACJ2G,KAAK,EAAC,UAAU;UAChBC,WAAW,EAAC,SAAS;UACrBtD,KAAK,EAAEuF,QAAQ,CAACtD,IAAK;UACrBsB,QAAQ,EAAGvD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;YACtC,GAAGA,IAAI;YACPnB,SAAS,EAAEmB,IAAI,CAACnB,SAAS,CAACmC,GAAG,CAAC,CAACuE,CAAC,EAAErE,CAAC,KACjCA,CAAC,KAAKH,KAAK,GAAG;cAAE,GAAGwE,CAAC;cAAEvD,IAAI,EAAEjC;YAAM,CAAC,GAAGwF,CACxC;UACF,CAAC,CAAC,CAAE;UACJhC,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFnG,OAAA,CAACH,MAAM;UACL0G,KAAK,EAAC,mBAAmB;UACzBrD,KAAK,EAAEuF,QAAQ,CAACD,WAAY;UAC5B/B,QAAQ,EAAGvD,KAAK,IAAKjC,SAAS,CAACkC,IAAI,KAAK;YACtC,GAAGA,IAAI;YACPnB,SAAS,EAAEmB,IAAI,CAACnB,SAAS,CAACmC,GAAG,CAAC,CAACuE,CAAC,EAAErE,CAAC,KACjCA,CAAC,KAAKH,KAAK,GAAG;cAAE,GAAGwE,CAAC;cAAEF,WAAW,EAAEtF;YAAa,CAAC,GAAGwF,CACtD;UACF,CAAC,CAAC,CAAE;UACJ/B,OAAO,EAAE,CACP;YAAEzD,KAAK,EAAE,OAAO;YAAEqD,KAAK,EAAE;UAAQ,CAAC,EAClC;YAAErD,KAAK,EAAE,gBAAgB;YAAEqD,KAAK,EAAE;UAAiB,CAAC,EACpD;YAAErD,KAAK,EAAE,QAAQ;YAAEqD,KAAK,EAAE;UAAS,CAAC,EACpC;YAAErD,KAAK,EAAE,QAAQ;YAAEqD,KAAK,EAAE;UAAS,CAAC,CACpC;UACFG,QAAQ;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA,GA1CMsC,QAAQ,CAACtI,EAAE;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2ChB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EACLnF,MAAM,CAACgB,SAAS,CAACc,MAAM,KAAK,CAAC,iBAC5B9C,OAAA;MAAKgH,SAAS,EAAC,gCAAgC;MAAAtB,QAAA,eAC7C1F,OAAA;QAAA0F,QAAA,EAAG;MAA4D;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMwC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQ1G,KAAK,CAACvB,WAAW,CAAC,CAACyB,SAAS;MAClC,KAAK,OAAO;QACV,OAAOsD,eAAe,CAAC,CAAC;MAC1B,KAAK,UAAU;QACb,OAAOmB,kBAAkB,CAAC,CAAC;MAC7B;QACE,OAAOnB,eAAe,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,oBACEzF,OAAA;IAAK2F,KAAK,EAAE;MAAEiD,QAAQ,EAAE,OAAO;MAAEC,MAAM,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAY,CAAE;IAAApD,QAAA,gBAExE1F,OAAA;MAAK2F,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBACnC1F,OAAA;QAAI2F,KAAK,EAAE;UAAEC,QAAQ,EAAE,UAAU;UAAEC,UAAU,EAAE,MAAM;UAAEC,KAAK,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAS,CAAE;QAAAL,QAAA,EAC/FvF,EAAE,IAAIA,EAAE,KAAK,KAAK,GAAG,SAAS,GAAG;MAAe;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACLnG,OAAA;QAAG2F,KAAK,EAAE;UAAEG,KAAK,EAAE;QAAU,CAAE;QAAAJ,QAAA,EAAC;MAEhC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNnG,OAAA;MAAK2F,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,eACnC1F,OAAA;QAAK2F,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAE2C,UAAU,EAAE;QAAS,CAAE;QAAArD,QAAA,EACnDzD,KAAK,CAACkC,GAAG,CAAC,CAAC6E,IAAI,EAAE9E,KAAK,kBACrBlE,OAAA;UAAsB2F,KAAK,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE2C,UAAU,EAAE,QAAQ;YAAEE,WAAW,EAAE/E,KAAK,KAAKjC,KAAK,CAACa,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;UAAI,CAAE;UAAA4C,QAAA,gBAC7H1F,OAAA;YACE2F,KAAK,EAAE;cACLS,OAAO,EAAE,MAAM;cACf2C,UAAU,EAAE,QAAQ;cACpBG,cAAc,EAAE,QAAQ;cACxBC,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBC,eAAe,EAAEpF,KAAK,GAAGxD,WAAW,GAAG,SAAS,GAAGwD,KAAK,KAAKxD,WAAW,GAAG,OAAO,GAAG,OAAO;cAC5F6I,MAAM,EAAErF,KAAK,KAAKxD,WAAW,GAAG,mBAAmB,GAAG,mBAAmB;cACzEoF,KAAK,EAAE5B,KAAK,GAAGxD,WAAW,GAAG,OAAO,GAAGwD,KAAK,KAAKxD,WAAW,GAAG,SAAS,GAAG,SAAS;cACpFkF,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE;YACd,CAAE;YAAAH,QAAA,EAEDxB,KAAK,GAAGxD,WAAW,gBAClBV,OAAA;cAAK2F,KAAK,EAAE;gBAAEwD,KAAK,EAAE,SAAS;gBAAEC,MAAM,EAAE;cAAU,CAAE;cAACI,IAAI,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA/D,QAAA,eAC1F1F,OAAA;gBAAM0J,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,oHAAoH;gBAACC,QAAQ,EAAC;cAAS;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClK,CAAC,GAENjC,KAAK,GAAG;UACT;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNnG,OAAA;YAAM2F,KAAK,EAAE;cACXkE,UAAU,EAAE,MAAM;cAClBjE,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE5B,KAAK,KAAKxD,WAAW,GAAG,SAAS,GAAG;YAC7C,CAAE;YAAAgF,QAAA,EACCsD,IAAI,CAAC9G;UAAK;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA,GA/BC6C,IAAI,CAAC9G,KAAK;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgCf,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnG,OAAA;MAAK2F,KAAK,EAAE;QAAE2D,eAAe,EAAE,OAAO;QAAEQ,SAAS,EAAE,gCAAgC;QAAET,YAAY,EAAE,QAAQ;QAAEP,OAAO,EAAE,QAAQ;QAAE/C,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,EACpJiD,iBAAiB,CAAC;IAAC;MAAA3C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGNnG,OAAA;MAAK2F,KAAK,EAAE;QAAES,OAAO,EAAE,MAAM;QAAE8C,cAAc,EAAE;MAAgB,CAAE;MAAAxD,QAAA,gBAC/D1F,OAAA;QAAA0F,QAAA,EACGhF,WAAW,GAAG,CAAC,iBACdV,OAAA,CAACL,MAAM;UACLsH,OAAO,EAAElE,cAAe;UACxBoE,OAAO,EAAC,SAAS;UAAAzB,QAAA,EAClB;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNnG,OAAA;QAAK2F,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEE,GAAG,EAAE;QAAO,CAAE;QAAAZ,QAAA,gBAC3C1F,OAAA,CAACL,MAAM;UACLsH,OAAO,EAAE1E,UAAW;UACpB4E,OAAO,EAAC,SAAS;UAAAzB,QAAA,EAClB;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRzF,WAAW,GAAGuB,KAAK,CAACa,MAAM,GAAG,CAAC,gBAC7B9C,OAAA,CAACL,MAAM;UAACsH,OAAO,EAAEpE,UAAW;UAAA6C,QAAA,EAAC;QAE7B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,gBAETnG,OAAA,CAACL,MAAM;UAACsH,OAAO,EAAEA,CAAA,KAAM7G,QAAQ,CAAC,YAAY,CAAE;UAAAsF,QAAA,EAAC;QAE/C;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjG,EAAA,CA3xBID,SAAmB;EAAA,QACRT,SAAS,EACPC,WAAW,EACqCC,KAAK;AAAA;AAAAqK,EAAA,GAHlE9J,SAAmB;AA6xBzB,eAAeA,SAAS;AAAC,IAAA8J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}