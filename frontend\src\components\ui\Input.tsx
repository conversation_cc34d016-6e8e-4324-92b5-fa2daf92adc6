import React from 'react';
import { InputProps } from '../../types';

const Input: React.FC<InputProps> = ({
  label,
  placeholder,
  value,
  onChange,
  error,
  disabled = false,
  required = false,
  type = 'text',
  className = '',
}) => {
  const getInputClasses = () => {
    let classes = 'input';

    if (error) {
      classes += ' error';
    }

    return `${classes} ${className}`;
  };

  return (
    <div style={{ marginBottom: '1rem' }}>
      {label && (
        <label
          style={{
            display: 'block',
            fontSize: '0.875rem',
            fontWeight: '500',
            color: '#374151',
            marginBottom: '0.25rem'
          }}
        >
          {label}
          {required && <span style={{ color: '#ef4444', marginLeft: '0.25rem' }}>*</span>}
        </label>
      )}
      <input
        type={type}
        className={getInputClasses()}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        required={required}
      />
      {error && (
        <p style={{ fontSize: '0.875rem', color: '#dc2626', marginTop: '0.25rem' }}>
          {error}
        </p>
      )}
    </div>
  );
};

export default Input;
